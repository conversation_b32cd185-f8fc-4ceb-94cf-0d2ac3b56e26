# 提现密码功能实现文档

## 功能概述

本次更新为交易所前端添加了完整的提现密码功能，支持多语言翻译，提高资金安全性。

## 🌍 多语言支持

已为以下10种语言添加了完整的提现密码翻译：

- 🇨🇳 简体中文 (zh)
- 🇺🇸 English (en) 
- 🇧🇷 Português (pt)
- 🇹🇷 Türkçe (tr)
- 🇹🇭 ไทย (th)
- 🇰🇷 한국어 (kor)
- 🇫🇷 Français (fra)
- 🇪🇸 Español (spa)
- 🇯🇵 日本語 (jp)
- 🇭🇰 繁體中文 (hk)

## 📁 修改的文件

### 1. 多语言翻译文件
```
common/locales/zh.js     - 简体中文翻译
common/locales/en.js     - 英语翻译
common/locales/pt.js     - 葡萄牙语翻译
common/locales/tr.js     - 土耳其语翻译
common/locales/th.js     - 泰语翻译
common/locales/kor.js    - 韩语翻译
common/locales/fra.js    - 法语翻译
common/locales/spa.js    - 西班牙语翻译
common/locales/jp.js     - 日语翻译
common/locales/hk.js     - 繁体中文翻译
```

### 2. 页面文件
```
pages/setting/bank.vue                - 银行卡绑定页面
pages/setting/bank_international.vue  - 国际银行卡绑定页面
pages/fund/withdraw.vue               - 提现页面
pages/setting/withdraw_password.vue   - 提现密码管理页面（新增）
```

### 3. API文件
```
common/http.api.js - API接口定义
```

## 🔧 新增的翻译键值

```javascript
// 提现密码相关翻译
withdrawPassword: '提现密码',
confirmWithdrawPassword: '确认提现密码',
plsInputWithdrawPassword: '请输入提现密码',
plsInputConfirmWithdrawPassword: '请输入确认提现密码',
withdrawPasswordPlaceholder: '请输入6-16位提现密码',
confirmWithdrawPasswordPlaceholder: '请再次输入提现密码',
updateWithdrawPassword: '修改提现密码',
oldWithdrawPassword: '原提现密码',
newWithdrawPassword: '新提现密码',
plsInputOldWithdrawPassword: '请输入原提现密码',
plsInputNewWithdrawPassword: '请输入新提现密码',
withdrawPasswordNotMatch: '两次输入的提现密码不一致',
withdrawPasswordLengthError: '提现密码长度应为6-16位',
withdrawPasswordRequired: '请设置提现密码',
withdrawPasswordVerifyFailed: '提现密码验证失败',
plsInputWithdrawPasswordForWithdraw: '请输入提现密码进行验证'
```

## 🚀 新增的API接口

```javascript
// 验证提现密码
checkWithdrawPassword: (password) => vm.$u.post("/checkWithdrawPassword", {password})

// 修改提现密码  
updateWithdrawPassword: (old_password, password, re_password) => 
  vm.$u.post("/updateWithdrawPassword", {old_password, password, re_password})
```

## 💡 使用方法

### 1. 银行卡绑定时设置提现密码

在银行卡绑定页面，用户需要：
1. 填写银行卡基本信息
2. 设置提现密码（6-16位）
3. 确认提现密码
4. 提交绑定

### 2. 提现时验证提现密码

在提现页面，用户需要：
1. 选择提现方式
2. 输入提现金额
3. **输入提现密码进行验证**
4. 确认提现

### 3. 修改提现密码

访问 `/pages/setting/withdraw_password` 页面：
1. 输入原密码（如果已设置）
2. 输入新密码（6-16位）
3. 确认新密码
4. 提交修改

## 🔒 安全特性

- ✅ 密码长度限制（6-16位字符）
- ✅ 密码确认验证
- ✅ 提现时强制密码验证
- ✅ 支持密码修改功能
- ✅ 多语言错误提示
- ✅ 表单验证完整

## 🎯 页面路由

```javascript
// 银行卡绑定
/pages/setting/bank

// 国际银行卡绑定  
/pages/setting/bank_international

// 提现页面
/pages/fund/withdraw

// 提现密码管理
/pages/setting/withdraw_password
```

## 📋 后端需要配合的工作

1. **数据库修改**
   - 在 `user_cash_info` 表添加 `withdraw_password` 字段
   - 字段类型：VARCHAR(255)，默认值：空字符串

2. **API接口实现**
   - `POST /api/checkWithdrawPassword` - 验证提现密码
   - `POST /api/updateWithdrawPassword` - 修改提现密码
   - 修改银行卡绑定API支持提现密码参数
   - 修改提现API支持提现密码验证

3. **安全实现**
   - 密码加密存储（建议MD5+盐值）
   - 提现时验证密码
   - 密码错误次数限制

## 🧪 测试建议

1. **多语言测试**
   - 切换不同语言验证翻译正确性
   - 测试所有错误提示信息

2. **功能测试**
   - 银行卡绑定时设置提现密码
   - 提现时验证提现密码
   - 修改提现密码功能

3. **安全测试**
   - 密码长度验证
   - 密码确认验证
   - 错误密码处理

## ✅ 完成状态

- ✅ 多语言翻译（10种语言）
- ✅ 银行卡绑定页面更新
- ✅ 国际银行卡绑定页面更新
- ✅ 提现页面更新
- ✅ 提现密码管理页面
- ✅ API接口定义
- ✅ 表单验证
- ✅ 错误处理

**功能已完全实现，可以进行测试和部署！**

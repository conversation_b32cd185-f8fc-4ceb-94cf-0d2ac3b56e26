export default {
	nav: ['Home', 'Markets', 'Trade', 'Finance', 'Assets'],
	common: {
		search: 'Search',
		confirm: "OK",
		cancel: "Cancel",
		submit: "Submit",
		logout: 'exit',
		all: "all",
		edit: "Edit",
		delete: "Delete",
		reset: "Reset",
		filter: "Filter",
		login: "login",
		register: "register",
		email: "Email",
		mobile: "Mobile phone number",
		account: "Account",
		password: "Password",
		passwordPlaceholder: "Please enter your password",
		noaccount: "No account?",
		go: "go",
		forgetPassword: "Forgot Password",
		confirmPassword: "Confirm Password",
		invitecode: "Invitation Code",
		hasaccount: "Already have an account?",
		mobilecodePlaceholder: "Please enter the mobile phone verification code",
		emailcodePlaceholder: "Please enter the email verification code",
		send: "Send verification code",
		selectArea: "Area selection",
		audit: "Audit",
		underAudit: "Under review",
		auditFailure: "Audit failed",
		untie: "untie",
		buy: "buy",
		sell: "sell",
		plsLogin: "Please login first",
		plsInputUsername: "Please enter an account",
		plsInputRePassword: "Please enter the confirmation password",
		plsInputInviteCode: "Please enter the invitation code",
		plsInputCode: "Please enter the verification code",
		plsInputMobile: "Please enter the mobile phone number",
		plsInputEmail: "Please enter your email",
		login_success: "Login successful",
		login_fail: "Login failed",
		reg_success: "Registration succeeded",
		reg_fail: "Login failed",
		pwdInconsistent: "The two passwords are inconsistent",
		hint: "hint",
		loading: 'Loading',
		success: "Success",
		plsInput: "Please input",
		click: "click",
		quotation: "Quote",
		edit: "Edit",
		second: 'second',
		specialChart: 'No special characters allowed',
		open: 'Open price',
		close: 'Close price',
		high: 'Highest price',
		low: 'lowest price',
		customerService: 'Contact customer service',
		rememberPassword: 'Remember password',
		newPassword: 'New password',
		pwdMoreThen6: 'The length of the password should be greater than six digits',
		codeLength6: 'The length of the verification code should be six digits',
		rePwdMoreThen6: 'The length of the confirmation password should be greater than six digits',
		pls: 'please',
		saveSuccess: "Saved to system album",
		nextStep: "Next",
		log: 'record',
		paramsWrong: "Parameter error",
		functionLoading: "Function in development",
		DIGICCY: "Digital Currency",
		quickly: "quick",
		more: "more",
		hasNoData: 'No more data',
		audioError: 'Audio file error',
		toLong: 'Character too long',
		selectLang : "Select language",
	},
	home: {
		contactUs: 'Contact Us',
		borrowing: 'Borrowing',
		borrowing_entry: 'loan entry',
		repayment_entry: 'loan repayment entry',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: 'Total assets equivalent',
		recharge: 'Recharge',
		withdraw: 'Withdrawal',
		stock: 'stock',
		futures: 'Contract',
		copytrade: 'Copy',
		financial: 'financial management',
		margin: 'Super Leverage',
		otc: 'OTC',
		coupon: 'Coupon Center',
		bills: 'Bills',
		exchange: 'Exchange',
		mining: 'lock up to earn coins',
		game: 'mini game',
		chatroom: 'chat room',
		quicklyBuyCoins: 'Quick Buy Coins',
		defiMining: 'DeFi Mining',
		cloudMining: 'Cloud Miner',
		stock: 'stock',
		stock_info: 'No need to open a position, hold a position immediately',
		lockming: 'locked mining',
		transfer: "Coin transfer",
		ieo: "IEO",
		tradeQuery: "Trade Query",
		seconds: "Options",
		day: "day",
		minimum: 'start',
		dailyReturnRate: "Daily rate of return",
		more: "more",
		market: "Quote",
		tradingPair: 'trading pair',
		lastPrice: 'Latest Price',
		todayChange: 'Today change',
		transaction: 'Transaction',
		nft: 'NFT',
		invest: "Investment",
		download: 'download',
		app_text: 'Anytime, anywhere, online transaction',
	},
	setting: {
		my_qrcode: 'My QR code',
		mine: 'Mine',
		inviteDesc: 'Invite friends, rebate together',
		inviteDesc2: 'Enjoy rebates with friends',
		inviteNumber: 'Number of invites',
		inviteTransNumber: 'Number of people who have traded',
		myRebate: 'My rebate',
		bill: "Bill",
		wallet: "Wallet address",
		banks: "Bank Card Binding",
		banksInternational: "Bank Card Binding - International",
		addBank: "Add bank card",
		securitySettings: "Security Settings",
		coupon: "Coupon Center",
		systemNotification: "System Notification",
		defaultFiatCurrency: "Default Fiat Currency",
		language: "language",
		faq: "Frequently Asked Questions",
		operationalCompliance: "Operational Compliance",
		share: "Share",
		contactUs: "Contact Us",
		aboutUs: "About Us",
		avatar: 'avatar',
		nickname: 'nickname',
		signature: 'personal signature',
		grxx: "personal information",
		grxxOptions: {
			nickname: {
				title: 'nickname',
				placeholder: 'Please enter a nickname',
				desc: '2-10 characters, support Chinese and English, numbers',
			},
			signature: {
				title: 'personal signature',
				placeholder: 'Please enter a personalized signature',
				desc: '2-30 characters, support Chinese and English, numbers',
			}
		},
		xxtx: "Upload Avatar",
		photograph: 'take a picture',
		selectFromAlbum: 'Select from album',
		aqsz: "Security Settings",
		editLoginPassword: 'Modify login password',
		setPayPassword: "Set payment password",
		editPayPassword: "Modify payment password",
		bindPhone: "Bind phone",
		email: 'email',
		stayLogin: 'stay logged in',
		xgdlmm: "Modify login password",
		currentPassword: 'current login password',
		currentPasswordPlaceholder: 'Please enter the current login password',
		newPassword: "New login password",
		newPasswordPlaceholder: "Please enter a new login password",
		confirmNewPassword: "Confirm new password",
		confirmNewPasswordPlaceholder: "Please enter a new password to confirm",
		currentPayPassword: "Current Payment Password",
		currentPayPasswordPlaceholder: 'Please enter the current payment password',
		newPayPassword: "New Payment Password",
		newPayPasswordPlaceholder: "Please enter a new payment password",
		confirmNewPayPassword: "Confirm new payment password",
		confirmNewPayPasswordPlaceholder: "Please enter the confirmation new payment password",
		phoneVerificationCode: 'Phone verification code',
		editPhoneVerificationCode: "Please enter the phone verification code",
		getVerificationCode: 'Get verification code',
		lctyq: "Financial Management Experience Voucher",
		expired: "Expired",
		expiredTime: "Expiration Date",
		useImmediately: "Use Immediately",
		addNewAccount: "Add a new account",
		totayIncome: "Collection today",
		totalIncome: "Accumulated receipts",
		selectLang: "Please select language",
		selectCoinType: "Please select a currency",
		authentication: 'Authentication',
		authentication1: 'Basic authentication',
		authentication2: 'Advanced Authentication',
		authentication_text_1: 'Personal identity authentication',
		authentication_text_2: 'Permissions available after authentication:',
		authentication_text_3: 'Lv1.Basic Authentication',
		authentication_text_4: 'It can be reflected after authentication, the 24-hour limit is 200BTC',
		authentication_text_5: 'After authentication, fiat currency transactions can be performed, with a single limit of 2000USDT',
		authentication_text_6: 'Lv2. Advanced Authentication',
		authentication_text_7: 'Increase the credit limit, the 24-hour limit is 500BTC',
		authentication_text_8: 'Increase the fiat currency transaction limit, a single limit of 1000000USDT',
		authentication_text_9: 'Reminder: In order to protect the security of user funds, please complete the binding of email and phone first, otherwise your authentication will fail. ',
		goAudit: 'Go to authentication',
		noaudit: 'Not authenticated',
		auditing: 'authenticating',
		hasaudit: 'authenticated',
		idType: 'ID Type',
		idCard: 'ID Card',
		passport: 'Passport',
		driverLicense: 'Driver License',
		name: 'name',
		idcard: 'ID number',
		plsIptName: 'Please enter a name',
		plsIptCorrectIdcard: 'Please enter the correct ID number',
		uploadIdcardFront: 'Upload ID card front',
		uploadIdcardReverse: 'Upload the back of the ID card',
		emailVerificationCode: 'Email Verification Code',
		plsiptEmailCode: 'Please enter the email verification code',
		addWallet: 'Add wallet address',
		selectCurrency: 'Select currency',
		plsSelectCurrency: 'Please select a currency',
		plsIptAddress: 'Please enter the wallet address',
		walletAddress: 'wallet address',
		walletQrcode: 'Wallet QR code',
		plsIptWalletAddress: 'Please enter the wallet address',
		plsUploadWalletQrcode: 'Please upload the wallet QR code',
		confirmLogout: 'Are you sure you want to log out? ',
		nonickname: 'Please set a nickname',
		plsInputMobile: "Please enter the mobile phone number",
		alipay_account: 'ABA number',
		real_name: 'real name',
		bank_account: 'bank card number',
		bank_dizhi: 'bank address',
		bank_name: 'account opening bank',
		bank_network: 'bank network',
		swift_code: 'Swift',
		phone: 'Phone',
		wechat_account: 'cardholder address',
		wechat_nickname: 'SWIFT code',
		allNeed: 'All are required',
		phoneNumber : "cell-phone number",
		code : "Verification Code",
		sendCode : "Send verification code",
		plsIptCode : "Please enter the verification code",
		withdrawPassword: 'Withdraw Password',
		confirmWithdrawPassword: 'Confirm Withdraw Password',
		plsInputWithdrawPassword: 'Please enter withdraw password',
		plsInputConfirmWithdrawPassword: 'Please confirm withdraw password',
		withdrawPasswordPlaceholder: 'Please enter 6-16 digit withdraw password',
		confirmWithdrawPasswordPlaceholder: 'Please enter withdraw password again',
		updateWithdrawPassword: 'Update Withdraw Password',
		oldWithdrawPassword: 'Old Withdraw Password',
		newWithdrawPassword: 'New Withdraw Password',
		plsInputOldWithdrawPassword: 'Please enter old withdraw password',
		plsInputNewWithdrawPassword: 'Please enter new withdraw password',
		withdrawPasswordNotMatch: 'Withdraw passwords do not match',
		withdrawPasswordLengthError: 'Withdraw password length should be 6-16 characters',
		withdrawPasswordRequired: 'Please set withdraw password',
		withdrawPasswordVerifyFailed: 'Withdraw password verification failed',
		needRealAuth: "Real-name authentication must be completed before trading",
		realAuthPending: "Real-name authentication is under review, please wait for completion",
		score: "Score"
	},
	transaction: {
		actualPL: 'Actual PL',
		favorites: "Optional",
		futures: "Contract",
		coins: "Coins",
		stock: "stock",
		tradingPair: 'trading pair',
		lastPrice: 'Latest Price',
		todayChange: 'Today change',
		searchPlaceholder: 'Search for the currency/stock name/symbol you care about',
		hotSearch: 'Hot Search',
		recentView: 'Recently viewed',
		dayHigh: "Today's High",
		dayLow: "Today's low",
		optional: 'optional',
		buy: 'buy',
		sell: 'sell',
		entrustPendingOrder: 'Entrust pending order',
		addOptionalSuccess: 'Add optional success',
		delOptionalSuccess: 'Delete optional success',
		seconds: "Options",
		home: "Home",
		orderConfirm: "Order Confirmation",
		currentPrice: 'current price',
		direction: "direction",
		selectTime: "Select expiration time",
		number: "Number",
		balance: 'account balance',
		plsIptCrtNumber: 'Please enter the correct number',
		buyPrice: 'buy price',
		expectedPL: "Expected profit and loss",
		continueTrade: "Continue to trade",
		secondsPosition: 'Option position',
		position: 'holding',
		history: 'History',
		orderTimes: 'Order duration',
		pl: "profit and loss",
		sellTime: "Sell Time",
		buy: 'buy',
		sell: 'sell',
		price: 'price',
		time: "time",
		optional: 'optional',
		buy: 'buy',
		sell: 'sell',
		long: "do more",
		short: "Short",
		entrustPendingOrder: 'Entrust pending order',
		addOptionalSuccess: 'Add optional success',
		delOptionalSuccess: 'Delete optional success',
		price: "price",
		amount: "Amount",
		marketPrice: "Market Price",
		limitPrice: "Limit Price",
		number: "Number of lots to trade",
		margin: "Margin",
		handlingFee: "handling fee",
		balance: "Balance",
		multiple: "Multiple",
		recharge: 'recharge',
		plsIptCommissionPrice: 'Please enter the commission price',
		tradingPair: "Trading Pair",
		direction: "direction",
		plsIptCrtPrice: 'Please enter the correct price',
		position: "Position",
		time: "time",
		turnover: "transaction volume",
		operation: "Operation",
		type: "Type",
		cover: 'Close position',
		confirmCover: 'Confirm to close the position? ',
		confirmSelfHold: 'Are you sure to switch to self-sustaining? ',
		selfHolding: 'self-holding',
		transSelfHold: 'Turn self-sustaining',
		delegateList: "Delegate List",
		riskRate: "Risk Rate",
		totalPandL: 'Total profit of the position',
		oneClickCover: "One-click close position",
		open: "Open position price",
		targetProfitPrice: "Take Profit Price",
		updatePrice: "Current Price",
		stopLossPrice: "Stop Loss Price",
		overnightMoney: "Overnight Fee",
		openTime: "Open time",
		lots: "hand",
		setProfitLoss: "Set Take Profit and Stop Loss",
		expectedProfit: "Expected Profit",
		expectedLoss: "Expected loss",
		coverAll: "Close all positions",
		onlyCoverMany: "Only cover long orders",
		onlyCoverEmpty: "Only cover empty orders",
		numbers: 'Number',
		coinBuyPrice: 'Trade at the current best price',
		jine: 'amount',
		p_jine: 'Please enter the amount',
		p_price: 'Please enter the price',
		p_confirm_sj: 'Confirm at market price',
		p_confirm_xj_1: 'Confirm at the current price',
		p_confirm_ma: '? ',
		status: "Status",
		complete: "Completed",
		doing: "in progress",
		currentEntrust: "Current Entrust",
		allEntrust: "All Entrust",
		entrust: "Entrust",
		entrustTime: "Entrust Time",
		all: "all",
		buyUp: "Buy more",
		buyDown: "Buy less",
		jye:'Transaction Amount',
	},
	fund: {
		overview: 'Overview',
		balance: 'balance',
		futures: 'contract',
		deposit: 'Deposit',
		mining: 'Lock up to earn coins',
		stock: 'stock',
		margin: 'Super Leverage',
		yesterdayEarnings: `Yesterday's earnings`,
		yieldAtMaturily: 'Yield at maturity',
		totalkRevenue: 'Total revenue',
		currentProfit: 'Current profit and loss',
		profitRatio: 'Profit ratio',
		receive: 'recharge/receive money',
		transfer: 'Withdrawal/Transfer',
		financial: 'financial financial management',
		valuation: 'valuation',
		currentEarnings: 'Current earnings',
		yesterdayIncome: `Yesterday's income`,
		crypto: 'digital currency',
		fiat: 'fiat currency',
		hideSmallCurrency: 'Hide Small Currency',
		available: 'available',
		freeze: 'freeze',
		equivalent: 'equivalent',
		usdtm: 'U standard',
		coinm: 'coin standard',
		exchange: "Coin",
		leverage: 'contract',
		fiat: 'fiat currency',
		second: 'Option',
		convert: "Convert total assets",
		convert2: "Asset Conversion",
		record: "Account change record",
		availableQuota: "Available Quota",
		locked: "Locked",
		converted: "converted",
		account: "Account",
		financialRecords: "Financial Records",
		number: "Number",
		record2: "record",
		time: "time",
		transfer: "Coin Transfer",
		from: "from",
		to: "to",
		transferNumber: "Transfer Number",
		youGet: "you will get",
		exchangeRate: "Exchange Rate",
		handlingFee: "handling fee",
		fundTransfer: "Fund Transfer",
		insufficientBalance: "Insufficient balance",
		needHandlingFee: "Additional handling fee",
		plsIptCrtNumber: 'Please enter the correct number',
		c_transfer: "Are you sure you want to transfer?",
		selectCurrency: "Select Currency",
		DIGICCY: 'Digital Currency',
		saveQrcode: 'Save QR code',
		copyAddress: 'Copy link address',
		plsTrans: 'Please transfer to this address',
		rechargeNumer: "Number of recharges",
		paymentVoucher: "Payment Voucher",
		plsIptRechargeNumer: "Please enter the recharge amount",
		plsUploadPaymentVoucher: "Please upload payment voucher",
		plsIptCrtAmount: 'Please enter the correct amount',
		withdraw: "Withdrawal/Transfer",
		currencyName: "Currency name",
		transactionInfo: "Transaction Account",
		withdraw2: 'Withdrawal',
		canUse: 'available',
		all: 'all',
		plsIptWithdrawNumber: 'Please enter the withdrawal amount',
		withdrawNumber: 'withdrawal amount',
		remark: 'Remark',
		handlingFee: 'handling',
		canGetNumber: 'Number of accounts received',
		leastNumber: 'Minimum withdrawal amount',
		coinType: 'currency',
		address: 'Address',
		date: 'date',
		withdrawAddress: 'Withdrawal address',
		plsSelectWithdrawAddress: 'Please select the withdrawal address',
		withdrawToCard: 'mentioned bank card',
		withdrawToCardInternational: 'international',
		withdrawToAddress: 'mentioned wallet address',
		card: 'bank card',
		addCard: 'Add bank card',
		plsAddCard: 'Please add a bank card',
		plsInputWithdrawPasswordForWithdraw: 'Please enter withdraw password for verification'
	},
	ieo: {
		subscribe: "Subscribe",
		ing: "in progress",
		done: "Done",
		applySubscription: "Apply Subscription",
		remaining: "Remaining",
		lockPeriod: "Lock Period",
		endTime: "End Time",
		subscribeCurrency: "Subscription currency",
		issuancePrice: "Issue Price",
		totalIssuance: "Total Issuance",
		whitePaper: "White Paper",
		subscriptionCycle: "Subscription Cycle",
		warmUp: "Warm Up",
		website: "official website",
		finish: "finish",
		projectDetails: "Project Details",
		subscriptionConfirmation: "Subscription Confirmation",
		p_applicationsNumber: "Enter the number of subscriptions",
		needToPay: "Need to Pay",
		currentBalance: "Current Balance",
		subscriptionTime: "Subscription Time",
		currency: "Currency",
		applicationsNumber: "Number of applications",
		passesNumber: "Number of passes",
		timeToMarket: "Time to Market",
		day: "day",
		total: "Total",
		subscribed: 'Subscribed',
		shengou: "Subscription",
		insufficientBalance: "Insufficient balance",
		p_number: "Please enter the number",
		mySubscribe: "My Subscription"
	},
	lockming: {
		lockming: 'Locked mining',
		fundsUnderCustody: "Funds in custody",
		entrustedOrders: "Entrusted Orders",
		estimatedTodayIncome: "Estimated today's rate of return",
		cumulativeIncome: "Cumulative Income",
		ordersInCustody: "Orders in Custody",
		lockedPositionsToEarnCoins: "Locked to earn coins",
		lockedPositions: "Locked",
		minimumSingleTransaction: "Minimum single transaction",
		dailyYield: "Daily Yield",
		lockUpPeriod: "Lockup Period",
		acquisition: "Acquisition",
		recentDays: "Recent Days",
		dividendTime: "Dividend time",
		escrowFunds: "Escrow Funds",
		redemptionInAdvance: "Early Redemption",
		estimatedIncome: "Estimated Income",
		availableAssets: "Available Assets",
		investmentAmountObtained: "Invested Amount Obtained",
		all: "all",
		l_alert_1: "Mining keeps going",
		l_alert_2: "Lock-up mining is to make mining income in the platform's mining pool by hosting usdt to the platform's super-computing mining machine",
		features: "Features",
		onDemand: "On demand",
		dividendPeriod: "Dividend period",
		issuedDaily: "Issued daily",
		currentInterest: "current interest",
		feature_1: "100% capital security<br />Guarantee",
		feature_2: "Holiday revenue<br />Uninterrupted",
		feature_3: "Value of the day<br />After successful deposit",
		forInstance: "For Instance",
		incomeCalculation: "Income Calculation",
		forInstanceDesc: "Members lock up 10000U on the platform and choose financial products with a cycle of 5 days and a daily output of 0.3% - 0.4% of the locked amount. The daily output is as follows: <br/>Minimum: 10000U * 0.3% = 30U <br/>Maximum: 10000U * 0.4% = 40u <br/>That is, you can get 150u ~ 200u income after 5 days. The income is distributed every day, and the distributed income can be accessed at any time. After the lock principal expires, it will be automatically transferred to your capital account.",
		aboutLiquidatedDamages: "About liquidated damages",
		aboutLiquidatedDamagesContent: "If you want to transfer out the unexpired principal, there will be liquidated damages. Liquidated damages = Default settlement proportion * remaining days * lock up quantity<br/>For example: the default settlement proportion of the locked warehouse mining is 0.4%, the remaining three days are due, and the number of locked warehouses is 1000, then the liquidated damages = 0.4% * 3 * 1000 = 12U, and the actual returned principal is 1000u-12u = 988u",
		day: "day",
		daily: "Daily",
		returnOnExpiration: "Expiration return",
		get: "get",
		numberOfCoinsDeposited: "Number of coins deposited",
		subscribe: "Subscribe",
		insufficientBalance: "Insufficient balance",
		p_number: "Please enter the number",
		leastSingle: "Single at least",
		lockedPositionList: "Locked Position List",
		inProgress: "In Progress",
		redeemed: "redeemed",
		expiryTime: "Expiration Time",
		lockUpTime: "Lock Up Time",
		earlyRedemptionPenalty: "Early Redemption Penalty",
		redemption: 'redemption',
		c_redemption: "Are you sure about early redemption?",
		joinNow: "Join Now"
	},
	invest: {
		invest: 'financial management',
		section: 'Section',
		perPrice: 'Single price',
		apr: "Annualized rate of return",
		linkedReferencePrice: "Linked Reference Price",
		holdingDays: "Holding Period",
		maturityDate: "Maturity Date",
		remainingShares: "Remaining Shares",
		progress: "Current progress",
		action: "action",
		purchase: 'purchase',
		myPosition: 'My finances',
		buyShares: 'Buy Shares',
		estimatedIncome: "Estimated Income",
		day: 'day',
		share: 'Shares',
		fail: 'failed',
		purchased: 'purchased',
		settlement: 'Settling',
		success: 'completed',
		all: "all",
		name: 'name',
		remain: 'remaining',
		instruction: "Product Description",
		help: 'help',
		shuoming_1: '1. This product is a non-guaranteed wealth management product. Market fluctuations may lead to loss of principal. Please invest with caution.',
		shuoming_2: '2. Payment collection and settlement rules:',
		shuoming_3: 'If settlement price＜pegged price, BTC will be settled after expiration; settlement amount=investment amount*(1+days*annualized rate of return/365).',
		shuoming_4: 'If the settlement price ≥ the pegged price, USDT will be settled after expiration; settlement amount = investment amount * pegged price * (1 + days * annualized rate of return / 365).',
		shuoming_5: '3. The actual annualized rate of return changes with the market in real time, please refer to the actual purchase transaction.',
		shuoming_6: '4. The investment amount is calculated in real time with the market, please refer to the actual purchase transaction.',
		shuoming_7: '5. After the product is purchased, it can be viewed on the My Coin Holding page. After the expiration, the payment will be automatically issued to the spot account.',
		error_1: 'Please enter the purchase quantity',
		error_2: 'The purchase quantity should be an integer',
		confirmPurchase: 'Are you sure to purchase this financial product? ',
		purchaseSuccess: 'Purchase successful!',
	},
	follow: {
		follow: 'Copy',
		traderList: 'Trader List',
		totalProfit: 'Total profit and loss',
		correctRate: 'Total accuracy',
		positionValuation: "Position Valuation",
		estimatedProfitToday: "Estimated Profit Today",
		rateOfReturn: "Yield",
		becomeATrader: "Become a trader",
		traderList: "Trader List",
		onlyShowsThePosition: "Only ShowsThePosition",
		theDataIsUpdatedEveryHour: "Data is updated every hour",
		following: "Documenting",
		follow: "Documentary",
		confirmFollow: 'Are you sure to follow? ',
		confirmCancel: 'Are you sure to cancel following? ',
		followTrader: 'Follow the trader',
		editCopy: "Edit Follow",
		beforeCopy: "Please read carefully before choosing to follow",
		copyAgreement: "Follow the agreement",
		copyType: "Follow mode",
		copyType1: "Fixed Multiple Follow",
		copyType2: "Fixed Lot Follow",
		copyAlert1: "No matter how many orders the trader places, you will follow by the selected fixed multiple.",
		copyAlert2: "(When the proportional lot is > 1 and not an integer, round up to two decimal places)",
		copyMultiple: "Follow the multiple",
		cancelCopy: "Cancel Follow",
		currentFollowerNumber: "Current Follower Number",
		totalProfitAndLoss: "Total Profit and Loss",
		totalReturn: "Total Return",
		accuracy: "Accuracy",
		profitableOrders: "profitable orders",
		successfulShortTrades: "Successful short trade",
		successfulLongTrades: "Successfully long trade",
		yesterdaysTradingStatus: "Yesterday's trading status",
		currentPositions: "Current Positions",
		totalOrders: "Total Orders",
		remain: 'remaining',
		distanceDue: "distance due",
		days: "days",
		deadline: "Deadline",
		subscription: "Subscription",
		followUsers: "Follow users",
		followAmount: "Follow Amount",
		followEarnings: "Follow Earnings",
		cancelFollow: "Cancel Follow",
		currentCopy: "Current Copy",
		historicalCopy: "Historical Transactions",
		myTrader: "My Trader",
		tradingPair: 'trading pair',
		open: 'Opening price',
		close: 'Close price',
		earnings: 'earnings',
		lots: 'lots',
		time: 'Transaction time',
		buyIn: 'buy',
		buyOut: 'sell',
		direction: 'direction'
	},
	financial: {
		product: 'Product',
		position: 'hold',
		financial: 'financial',
		lockming: 'Locked mining',
		ieo: "IEO",
		invest: "Investment and financial management",
		section: 'Section',
		apr: "Annualized rate of return",
		linkedReferencePrice: "Linked Reference Price",
		holdingDays: "Holding Period",
		day: 'day',
		lockPeriod: "Lock Period",
		ing: "in progress",
		done: "Done",
		applySubscription: "Apply Subscription",
		remaining: "Remaining",
		total: "Total",
		subscribed: 'Subscribed',
		mining: 'Lock up to earn coins',
		minimum: 'start',
		dailyReturnRate: "Daily Return Rate",
	},
	nft: {
		nft: 'NFT',
		artwork: 'artwork',
		artist: 'artist',
		artworkList: 'Artwork List',
		artistList: 'Artist List',
		comprehensiveSorting: 'Comprehensive sorting',
		newest: 'latest',
		hottest: 'hottest',
		mostWorks: 'The most works',
		mostPopular: 'Most Popular',
		record: 'Purchase record',
		recommendArtist: 'Recommended artist',
		buy: 'buy',
		artistHomepage: 'Artist Homepage',
		works: 'works',
		sold: 'sold',
		fans: 'fans',
		allWorks: 'All Works',
		notSell: 'Not sold',
		myCollect: 'My Collection',
		collect: "collected",
		own: "hold",
		placeABid: 'bid',
		purchase: 'purchase',
		open: 'After opening',
		image: 'image',
		gif: 'animation',
		audio: 'audio',
		video: 'video',
		creator: 'author',
		currentBid: 'Latest bid',
		price: 'price',
		d: 'day',
		h: 'hour',
		m: 'minutes',
		s: 'seconds',
		sold: 'closed',
		place_alert: 'The item has not reached the selling time or has ended',
		createdBy: 'author',
		place_alert2: "Your bid must be higher than",
		note: 'Important',
		CURRENT_BID: 'Latest bid',
		place_alert3: 'Participate in the auction, you need to pay',
		place_alert4: 'The deposit, when the auction ends, the deposit will be returned to your account. ',
		place_alert6: 'After the bid deadline, if your bid is successful, you will have one day to pay the final price. If payment is overdue, your security deposit will be deducted. ',
		confirmBid: 'Confirm bid',
		DESCRIPTION: 'Description',
		ADDRESS: 'Address',
		copy: 'copy',
		per_increase: 'Every increase in price',
		currency_type: 'Currency type',
		pay_type: 'Purchase method',
		pay_type_2: 'Sales method',
		normal: 'normal',
		auction: 'Auction',
		confirmAuction: 'Are you sure you want to participate in the auction for this item?',
		bindBox: 'blind box',
		sell_status: 'Sell status',
		hasStart: 'has started',
		willStart: 'Not started',
		artwork_type: 'Item type',
		reset: 'reset',
		confirm: 'OK',
		place_alert5: 'Please enter a keyword',
		collection: 'collected',
		byCollection: 'Collected',
		artworks: 'works',
		goCollect: 'Go to collection',
		price: 'price',
		nonickname: 'No nickname is set',
		cjdjs: 'Bid Deadline',
		cjkssj: 'Bid start time',
		cjjzsj: 'Bid Deadline',
		gotosee: 'Go to the market and see',
		position: 'hold',
		resell: 'resell',
		buyPrice: 'buy price',
		confirmResell: 'Confirm resell',
		wrongPrice: 'Please choose the correct price',
		bidPlacedBy: 'Bidder',
		placeRecord: 'Bid Record',
		confirmPurchase: 'Confirm purchase',
		artistList: 'Artist List',
		hasOpen: 'Open',
		noOpen: 'Not open',
		clickOpen: 'Open blind box',
		confirmOpen: 'Confirm to open the blind box? ',
		margin: 'Margin',
		wrongPrice: 'Please enter the correct price',
		wrongPerIncrease: 'Please enter the correct increase price',
		wrongTime: 'End time should be later than end time',
		confirmResell2: 'Once the product is resold, it cannot be withdrawn. Are you sure you want to resell the product? ',
		reselling: 'Reselling',
		message: 'Message notification',
		confirmPay: 'Are you sure you want to pay for this item? ',
		zfdjs: 'Payment countdown',
		payTime: 'Payment time',
		hasRead: 'read',
		lastPrice: 'Last transaction price',
		isPay: 'paid',
		isExpired: 'Expired',
		rarity: 'rarity'
	},
	tradeQuery: {
		title: "Trade Query",
		tradingPair: "Trading Pair",
		direction: "Direction",
		betSeconds: "Bet Duration",
		betAmount: "Bet Amount",
		currentStatus: "Current Status",
		buyUp: "Buy Up",
		buyDown: "Buy Down",
		seconds: "seconds",
		profit: "Profit",
		loss: "Loss",
		draw: "Draw",
		trading: "Trading",
		noData: "No data available",
		loading: "Loading...",
		refresh: "Refresh"
	},
	contact: {
		contactUs: 'Contact Us'
	}
}

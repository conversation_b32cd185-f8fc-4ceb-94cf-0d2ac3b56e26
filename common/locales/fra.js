export default {
	nav: ['Accueil', 'Marchés', 'Commerce', 'Finance', 'Actifs'],
	common: {
		search: "Recherche",
		confirm: "C'est sûr.",
		cancel: "Annulation",
		submit: "Présentation",
		logout: "Sortie",
		all: "Tous",
		edit: "Édition",
		delete: "Supprimer",
		reset: "Réinitialiser",
		filter: "Filtrage",
		login: "Connexion",
		register: "Inscription",
		email: "E - mail",
		mobile: "Numéro de téléphone portable",
		account: "Compte",
		password: "Mot de passe",
		passwordPlaceholder: "Veuillez saisir le mot de passe",
		noaccount: "Pas de compte?",
		go: "Vas - y.",
		forgetPassword: "Mot de passe oublié",
		confirmPassword: "Confirmer le mot de passe",
		invitecode: "Code d'invitation",
		hasaccount: "Vous avez déjà un compte?",
		mobilecodePlaceholder: "Veuillez saisir le Code de vérification du téléphone",
		emailcodePlaceholder: "Veuillez saisir le Code de vérification de la boîte aux lettres",
		send: "Envoyer un code de vérification",
		selectArea: "Sélection des régions",
		audit: "Audit",
		underAudit: "En cours de vérification",
		auditFailure: "Échec de la vérification",
		untie: "Dégroupage",
		buy: "Acheter",
		sell: "Vendre",
		plsLogin: "Veuillez vous connecter.",
		plsInputUsername: "Veuillez saisir un compte",
		plsInputRePassword: "Veuillez saisir le mot de passe de confirmation",
		plsInputInviteCode: "Veuillez saisir le Code d'invitation",
		plsInputCode: "Veuillez saisir le Code de vérification",
		plsInputMobile: "Veuillez saisir le numéro de téléphone",
		plsInputEmail: "Veuillez saisir une boîte aux lettres",
		login_success: "Connexion réussie",
		login_fail: "Échec de la connexion",
		reg_success: "Inscription réussie",
		reg_fail: "Échec de la connexion",
		pwdInconsistent: "Les mots de passe sont incohérents deux fois",
		hint: "Conseils",
		loading: "Chargement en cours",
		success: "Succès",
		plsInput: "Veuillez entrer",
		click: "Cliquez sur",
		quotation: "Marché",
		edit: "Édition",
		second: "Secondes",
		specialChart: "Les caractères spéciaux ne sont pas autorisés",
		open: "Prix d'ouverture",
		close: "Prix de clôture",
		high: "Prix maximum",
		low: "Prix le plus bas",
		customerService: "Contactez le service à la clientèle",
		rememberPassword: "Rappelez - vous le mot de passe.",
		newPassword: "Nouveau mot de passe",
		pwdMoreThen6: "La longueur du mot de passe doit être supérieure à six chiffres",
		codeLength6: "La longueur du Code de vérification doit être de six chiffres",
		rePwdMoreThen6: "La longueur du mot de passe de confirmation doit être supérieure à six chiffres",
		pls: "S'il vous plaît.",
		saveSuccess: "Sauvegardé dans l'album système",
		nextStep: "Prochaines étapes",
		log: "Enregistrement",
		paramsWrong: "Erreur de paramètre",
		functionLoading: "Développement fonctionnel en cours",
		DIGICCY: "Monnaie numérique",
		quickly: "Rapide",
		more: "Plus",
		hasNoData: "Plus de données.",
		audioError: "Erreur dans le fichier audio",
		toLong: "Caractère trop long",
		selectLang : "Sélectionner la langue",
	},
	home: {
		contactUs: 'Contactez',
		borrowing: 'Emprunt',
		borrowing_entry: 'entrée de prêt',
		repayment_entry: 'écriture de remboursement de prêt',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: "Total des actifs convertis",
		recharge: "Recharge",
		withdraw: "Retrait",
		stock: "Stock",
		futures: "Contrats",
		copytrade: "Suivi",
		financial: "Gestion financière",
		margin: "Super levier",
		otc: "OTC",
		coupon: "Centre de cartes",
		bills: "Facturation",
		exchange: "Échanges",
		mining: "exploitation minière",
		game: "Petits jeux",
		chatroom: "Chat Room",
		quicklyBuyCoins: "Acheter de l'argent rapidement",
		defiMining: "Defi Mining",
		cloudMining: "Machine à mine de nuages",
		stock: "Stock",
		stock_info: "Position immédiate sans ouverture",
		lockming: "Mine lock - out",
		transfer: "Transfert de pièces",
		ieo: "Ieo",
		tradeQuery: "Requête de Trading",
		seconds: "Options",
		day: "Oh, mon Dieu.",
		minimum: "Lève - toi.",
		dailyReturnRate: "Rendement quotidien",
		more: "Plus",
		market: "Marché",
		tradingPair: "- Oui.",
		lastPrice: "Dernier prix",
		todayChange: "Les gains et les pertes d'aujourd'hui",
		transaction: "Commerce",
		nft: "NFT",
		invest: "Investir",
		download: "Télécharger",
		app_text: "Commerce en ligne n'importe quand, n'importe où",
	},
	setting: {
		my_qrcode: 'Mon code QR',
		mine: 'Mine',
		inviteDesc: "Invitez vos amis à faire des rabais",
		inviteDesc2: "Profitez du rabais avec vos amis",
		inviteNumber: "Nombre d & apos; invitations",
		inviteTransNumber: "Nombre de personnes échangées",
		myRebate: "Mon remboursement",
		bill: "Facturation",
		wallet: "Adresse du portefeuille",
		banks: "Liaison par carte bancaire",
		banksInternational: "Liaison par carte bancaire - International",
		addBank: "Ajouter une carte bancaire",
		securitySettings: "Paramètres de sécurité",
		coupon: "Centre de cartes",
		systemNotification: "Notification du système",
		defaultFiatCurrency: "Monnaie légale par défaut",
		language: "Langues",
		faq: "Questions fréquemment posées",
		operationalCompliance: "Conformité opérationnelle",
		share: "Partager",
		contactUs: "Contactez - nous",
		aboutUs: "À propos de nous",
		avatar: "Portrait",
		nickname: "Un surnom.",
		signature: "Signature personnelle",
		grxx: "Renseignements personnels",
		grxxOptions: {
			nickname: {
				title: "Un surnom.",
				placeholder: "Veuillez saisir un surnom",
				desc: "2 - 10 caractères, support chinois et anglais, chiffres",
			},
			signature: {
				title: "Signature personnelle",
				placeholder: "Veuillez saisir votre signature personnelle",
				desc: "2 - 30 caractères, support chinois, anglais et numérique",
			}
		},
		xxtx: "Télécharger l'image de tête",
		photograph: "Prendre des photos",
		selectFromAlbum: "Sélectionner dans l'album",
		aqsz: "Paramètres de sécurité",
		editLoginPassword: "Modifier le mot de passe de connexion",
		setPayPassword: "Définir le mot de passe de paiement",
		editPayPassword: "Modifier le mot de passe de paiement",
		bindPhone: "Lier le téléphone",
		email: "E - mail",
		stayLogin: "Rester connecté",
		xgdlmm: "Modifier le mot de passe de connexion",
		currentPassword: "Mot de passe de connexion actuel",
		currentPasswordPlaceholder: "Veuillez saisir le mot de passe de connexion actuel",
		newPassword: "Nouveau mot de passe de connexion",
		newPasswordPlaceholder: "Veuillez saisir un nouveau mot de passe de connexion",
		confirmNewPassword: "Confirmer le nouveau mot de passe",
		confirmNewPasswordPlaceholder: "Veuillez entrer pour confirmer le nouveau mot de passe",
		currentPayPassword: "Mot de passe de paiement actuel",
		currentPayPasswordPlaceholder: "Veuillez saisir le mot de passe de paiement actuel",
		newPayPassword: "Nouveau mot de passe de paiement",
		newPayPasswordPlaceholder: "Veuillez saisir un nouveau mot de passe de paiement",
		confirmNewPayPassword: "Confirmer le nouveau mot de passe de paiement",
		confirmNewPayPasswordPlaceholder: "Veuillez saisir le nouveau mot de passe de paiement pour confirmer",
		phoneVerificationCode: "Code de vérification du téléphone mobile",
		editPhoneVerificationCode: "Veuillez saisir le Code de vérification du téléphone",
		getVerificationCode: "Obtenir le Code de vérification",
		lctyq: "Certificat d'expérience financière",
		expired: "Échec",
		expiredTime: "Date d'expiration",
		useImmediately: "À utiliser immédiatement",
		addNewAccount: "Ajouter un nouveau compte",
		totayIncome: "Collection d'aujourd'hui",
		totalIncome: "Recouvrement cumulé",
		selectLang: "Veuillez sélectionner une langue",
		selectCoinType: "Veuillez sélectionner la devise",
		authentication: "Authentification de l'identité",
		authentication1: "Certification de base",
		authentication2: "Certification avancée",
		authentication_text_1: "Authentification de l'identité personnelle",
		authentication_text_2: "Permissions disponibles après la certification:",
		authentication_text_3: "LV1. Certification de base",
		authentication_text_4: "Après certification, la limite de 24 heures est de 200 BTC",
		authentication_text_5: "Les opérations en monnaie française peuvent être effectuées après l'authentification, avec une limite unique de 2 000 USD",
		authentication_text_6: "LV2. Certification avancée",
		authentication_text_7: "Augmentation de la limite de présentation, limite de 24 heures 500btc",
		authentication_text_8: "Augmentation de la limite de transaction en monnaie française, limite unique de 10000000 USD",
		authentication_text_9: "Conseil chaleureux: pour protéger la sécurité des fonds des utilisateurs, veuillez d'abord compléter la liaison de la boîte aux lettres et du téléphone, sinon votre authentification ne passera pas.",
		goAudit: "Pour certifier",
		noaudit: "Non certifié",
		auditing: "Certification en cours",
		hasaudit: "Certifié",
		idType: 'Type de certificat',
		idCard: 'Carte d\'identité',
		passport: 'Passeport',
		driverLicense: 'Permis de conduire',
		name: "Nom (s)",
		idcard: "Numéro d'identification",
		plsIptName: "Veuillez saisir un nom",
		plsIptCorrectIdcard: "Veuillez saisir le bon numéro d'identification",
		uploadIdcardFront: "Télécharger l'avant de la carte d'identité",
		uploadIdcardReverse: "Télécharger la carte d'identité au verso",
		emailVerificationCode: "Code de vérification de la boîte aux lettres",
		plsiptEmailCode: "Veuillez saisir le Code de vérification de la boîte aux lettres",
		addWallet: "Ajouter une adresse de portefeuille",
		selectCurrency: "Sélectionner la devise",
		plsSelectCurrency: "Veuillez sélectionner la devise",
		plsIptAddress: "Veuillez saisir l'adresse du portefeuille",
		walletAddress: "Adresse du portefeuille",
		walletQrcode: "Code QR du portefeuille",
		plsIptWalletAddress: "Veuillez saisir l'adresse du portefeuille",
		plsUploadWalletQrcode: "Veuillez télécharger le Code QR du portefeuille",
		confirmLogout: "Tu es sûr de vouloir quitter la connexion?",
		nonickname: "Veuillez définir un surnom",
		plsInputMobile: "Veuillez saisir le numéro de téléphone",
		alipay_account: "No Aba",
		real_name: "Nom réel",
		bank_account: "Numéro de carte bancaire",
		bank_dizhi: "Adresse de la Banque",
		bank_name: "Banque de dépôt",
		bank_network: 'Ouverture du compte',
		swift_code: 'SWIFT',
		phone: 'Numéro de téléphone',
		wechat_account: "Adresse du titulaire de la carte",
		wechat_nickname: "Code Swift",
		allNeed: "Tous requis",
		phoneNumber: "Numéro de téléphone portable",
		code: "Code de vérification",
		sendCode: "Envoyer un code de vérification",
		plsIptCode: "Veuillez saisir le Code de vérification",
		withdrawPassword: 'Mot de passe de retrait',
		confirmWithdrawPassword: 'Confirmer le mot de passe de retrait',
		plsInputWithdrawPassword: 'Veuillez saisir le mot de passe de retrait',
		plsInputConfirmWithdrawPassword: 'Veuillez confirmer le mot de passe de retrait',
		withdrawPasswordPlaceholder: 'Veuillez saisir un mot de passe de retrait de 6-16 chiffres',
		confirmWithdrawPasswordPlaceholder: 'Veuillez saisir à nouveau le mot de passe de retrait',
		updateWithdrawPassword: 'Mettre à jour le mot de passe de retrait',
		oldWithdrawPassword: 'Ancien mot de passe de retrait',
		newWithdrawPassword: 'Nouveau mot de passe de retrait',
		plsInputOldWithdrawPassword: 'Veuillez saisir l\'ancien mot de passe de retrait',
		plsInputNewWithdrawPassword: 'Veuillez saisir le nouveau mot de passe de retrait',
		withdrawPasswordNotMatch: 'Les mots de passe de retrait ne correspondent pas',
		withdrawPasswordLengthError: 'La longueur du mot de passe de retrait doit être de 6-16 caractères',
		withdrawPasswordRequired: 'Veuillez définir un mot de passe de retrait',
		withdrawPasswordVerifyFailed: 'Échec de la vérification du mot de passe de retrait',
		score: "Score"
	},
	transaction: {
		actualPL: 'Actual PL',
		favorites: "Auto - Sélection",
		futures: "Contrats",
		coins: "Monnaie",
		stock: "Stock",
		tradingPair: "- Oui.",
		lastPrice: "Dernier prix",
		todayChange: "Les gains et les pertes d'aujourd'hui",
		searchPlaceholder: "Rechercher la devise / le nom / le Code de l'action qui vous intéresse",
		hotSearch: "Recherche populaire",
		recentView: "Vue récente",
		dayHigh: "Aujourd'hui",
		dayLow: "Faible aujourd'hui",
		optional: "Auto - Sélection",
		buy: "Acheter",
		sell: "Vendre",
		entrustPendingOrder: "Liste des délégués",
		addOptionalSuccess: "Ajout de l'auto - sélection réussi",
		delOptionalSuccess: "Suppression de l'auto - sélection réussie",
		seconds: "Options",
		home: "Page d'accueil",
		orderConfirm: "Confirmation de commande",
		currentPrice: "Prix courants",
		direction: "Orientation",
		selectTime: "Sélectionner la date d'expiration",
		number: "Nombre",
		balance: "Solde du compte",
		plsIptCrtNumber: "Veuillez saisir la quantité correcte",
		buyPrice: "Prix d'achat",
		expectedPL: "Résultat escompté",
		continueTrade: "Continuez.",
		secondsPosition: "Position d'option",
		position: "Oui.",
		history: "Historique",
		orderTimes: "Durée de la commande",
		pl: "Profits et pertes",
		sellTime: "Temps de vente",
		buy: "Acheter",
		sell: "Vendre",
		price: "Prix",
		time: "Temps",
		optional: "Auto - Sélection",
		buy: "Acheter",
		sell: "Vendre",
		long: "Plus fort.",
		short: "Court - circuiter",
		entrustPendingOrder: "Liste des délégués",
		addOptionalSuccess: "Ajout de l'auto - sélection réussi",
		delOptionalSuccess: "Suppression de l'auto - sélection réussie",
		price: "Prix",
		amount: "Nombre",
		marketPrice: "Prix du marché",
		limitPrice: "Limite de prix",
		number: "Nombre de commerçants",
		margin: "Dépôt de garantie",
		handlingFee: "Frais de manutention",
		balance: "Solde",
		multiple: "Multiple",
		recharge: "Recharge",
		plsIptCommissionPrice: "Veuillez saisir le prix délégué",
		tradingPair: "- Oui.",
		direction: "Orientation",
		plsIptCrtPrice: "Veuillez saisir le prix correct",
		position: "Position",
		time: "Temps",
		turnover: "Montant de la transaction",
		operation: "Fonctionnement",
		type: "Type",
		cover: "Fermeture de la position",
		confirmCover: "Confirmer la fermeture?",
		confirmSelfHold: "Tu es sûr de vouloir te changer?",
		selfHolding: "Auto - préservation",
		transSelfHold: "Prise en charge",
		delegateList: "Liste des délégués",
		riskRate: "Taux de risque",
		totalPandL: "Total des gains de position",
		oneClickCover: "Fermeture éclair",
		open: "Prix d'ouverture",
		targetProfitPrice: "Prix de clôture",
		updatePrice: "Prix courants",
		stopLossPrice: "Prix stop loss",
		overnightMoney: "Frais de nuit",
		openTime: "Heure d'ouverture",
		lots: "Les mains.",
		setProfitLoss: "Réglage de l'arrêt du gain et de la perte",
		expectedProfit: "Bénéfice escompté",
		expectedLoss: "Pertes prévues",
		coverAll: "Clôture complète",
		onlyCoverMany: "Juste un plat.",
		onlyCoverEmpty: "Feuille plate seulement",
		numbers: "Nombre",
		coinBuyPrice: "Trading at current best price",
		jine: "Montant",
		p_jine: "Veuillez entrer le montant",
		p_price: "Veuillez entrer le prix",
		p_confirm_sj: "Déterminer au prix du marché",
		p_confirm_xj_1: "Déterminer au prix courant",
		p_confirm_ma: "C'est ça?",
		status: "Statut",
		complete: "Terminé",
		doing: "En cours",
		currentEntrust: "Mandat actuel",
		allEntrust: "Toutes les délégations",
		entrust: "Mandat",
		entrustTime: "Temps de délégation",
		all: "Tous",
		buyUp: "Acheter plus",
		buyDown: "Acheter moins",
		jye:'Montant de la transaction',
	},
	fund: {
		overview: "Aperçu général",
		balance: "Solde",
		futures: "Contrats",
		deposit: "Dépôts",
		mining: "exploitation minière",
		stock: "Stock",
		margin: "Super levier",
		yesterdayEarnings: "Gains d'hier",
		yieldAtMaturily: "Produit à l'échéance",
		totalkRevenue: "Total des recettes",
		currentProfit: "Gains et pertes courants",
		profitRatio: "Taux de rentabilité",
		receive: "Recharge / collecte",
		transfer: "Retrait / transfert",
		financial: "Gestion financière",
		valuation: "Évaluation",
		currentEarnings: "Recettes courantes",
		yesterdayIncome: "Gains d'hier",
		crypto: "Monnaie numérique",
		fiat: "Monnaie française",
		hideSmallCurrency: "Cacher la petite monnaie",
		available: "Disponible",
		freeze: "Gel",
		equivalent: "Réduction",
		usdtm: "U - standard",
		coinm: "Monnaie standard",
		exchange: "Monnaie",
		leverage: "Contrats",
		fiat: "Monnaie française",
		second: "Options",
		convert: "Total des actifs convertis",
		convert2: "Conversion des actifs",
		record: "Enregistrement du changement de compte",
		availableQuota: "Montant disponible",
		locked: "Verrouillé.",
		converted: "Réduction",
		account: "Comptes",
		financialRecords: "Comptes financiers",
		number: "Nombre",
		record2: "Enregistrement",
		time: "Temps",
		transfer: "Transfert de pièces",
		from: "De",
		to: "à",
		transferNumber: "Nombre de transferts",
		youGet: "Vous aurez",
		exchangeRate: "Taux de change",
		handlingFee: "Frais de manutention",
		fundTransfer: "Transfert de fonds",
		insufficientBalance: "Solde insuffisant",
		needHandlingFee: "Frais de manutention supplémentaires",
		plsIptCrtNumber: "Veuillez saisir la quantité correcte",
		c_transfer: "Tu es sûr de tourner?",
		selectCurrency: "Sélectionner la devise",
		DIGICCY: "Monnaie numérique",
		saveQrcode: "Enregistrer le Code QR",
		copyAddress: "Copier l'adresse du lien",
		plsTrans: "Veuillez retourner à cette adresse",
		rechargeNumer: "Nombre de recharges",
		paymentVoucher: "Certificat de paiement",
		plsIptRechargeNumer: "Veuillez saisir la quantité de recharge",
		plsUploadPaymentVoucher: "Veuillez télécharger le bon de paiement",
		plsIptCrtAmount: "Veuillez saisir le montant correct",
		withdraw: "Retrait / transfert",
		currencyName: "Nom de la monnaie",
		transactionInfo: "Compte de transaction",
		withdraw2: "Retrait",
		canUse: "Disponible",
		all: "Tous",
		plsIptWithdrawNumber: "Veuillez entrer la quantité de retrait",
		withdrawNumber: "Montant retiré",
		remark: "Remarques",
		handlingFee: "Frais de manutention",
		canGetNumber: "Quantité reçue",
		leastNumber: "Quantité minimale de retrait",
		coinType: "Monnaie",
		address: "Adresse",
		date: "Date",
		withdrawAddress: "Adresse de retrait",
		plsSelectWithdrawAddress: "Veuillez sélectionner l'adresse de retrait de monnaie",
		withdrawToCard: "Référence à la carte bancaire",
		withdrawToCardInternational: 'International',
		withdrawToAddress: "Mentionner l'adresse du portefeuille",
		card: "Carte bancaire",
		addCard: "Ajouter une carte bancaire",
		plsAddCard: "Veuillez ajouter une carte bancaire",
		plsInputWithdrawPasswordForWithdraw: 'Veuillez saisir le mot de passe de retrait pour vérification',
	},
	ieo: {
		subscribe: "Abonnement",
		ing: "En cours",
		done: "Terminé.",
		applySubscription: "Demande d'abonnement",
		remaining: "Reste",
		lockPeriod: "Cycle de verrouillage",
		endTime: "Fin",
		subscribeCurrency: "Monnaie d'abonnement",
		issuancePrice: "Prix d'émission",
		totalIssuance: "Total des émissions",
		whitePaper: "Livre blanc",
		subscriptionCycle: "Cycle de souscription",
		warmUp: "Préchauffage",
		website: "Site officiel",
		finish: "Fin",
		projectDetails: "Détails du projet",
		subscriptionConfirmation: "Confirmation de l'abonnement",
		p_applicationsNumber: "Saisissez le nombre d'abonnements",
		needToPay: "À payer",
		currentBalance: "Solde actuel",
		subscriptionTime: "Date d'abonnement",
		currency: "Monnaie",
		applicationsNumber: "Nombre de demandes",
		passesNumber: "Nombre de passages",
		timeToMarket: "Date de mise sur le marché",
		day: "Oh, mon Dieu.",
		total: "Total",
		subscribed: "Souscrite",
		shengou: "Abonnement",
		insufficientBalance: "Solde insuffisant",
		p_number: "Veuillez entrer la quantité",
		mySubscribe: "Mes abonnements",
	},
	lockming: {
		lockming: "Mine lock - out",
		fundsUnderCustody: "Fonds gérés",
		entrustedOrders: "Ordre de délégation",
		estimatedTodayIncome: "Rendement prévu aujourd'hui",
		cumulativeIncome: "Recettes cumulées",
		ordersInCustody: "Commandes en garde",
		lockedPositionsToEarnCoins: "exploitation minière",
		lockedPositions: "Verrouillage",
		minimumSingleTransaction: "Minimum par stylo",
		dailyYield: "Rendement quotidien",
		lockUpPeriod: "Cycle de verrouillage",
		acquisition: "Accès",
		recentDays: "Les derniers jours",
		dividendTime: "Date de versement des dividendes",
		escrowFunds: "Fonds détenus en fiducie",
		redemptionInAdvance: "Remboursement anticipé",
		estimatedIncome: "Recettes prévues",
		availableAssets: "Actifs disponibles",
		investmentAmountObtained: "Montant de l'investissement obtenu",
		all: "Tous",
		l_alert_1: "L'exploitation minière continue",
		l_alert_2: "L'exploitation de la mine lock - out est le revenu de l'exploitation minière dans le bassin de la plate - forme en confiant l'usdt à la plate - forme supercalculator",
		features: "Faits saillants du produit",
		onDemand: "Sur demande",
		dividendPeriod: "Période de versement des dividendes",
		issuedDaily: "Distribution quotidienne",
		currentInterest: "Intérêts courants",
		feature_1: "100% sécurité des fonds<br>Garantie",
		feature_2: "Gains de vacances<br>Non-Rupture",
		feature_3: "À compter de la date du dépôt réussi",
		forInstance: "Par exemple,",
		incomeCalculation: "Calcul des recettes",
		forInstanceDesc: "Les membres verrouillent la position de 10 000 u sur la plate - forme, sélectionnent les produits de gestion financière avec une période de 5 jours et une production quotidienne de 0,3% - 0,4% du montant verrouillé, puis la production quotidienne est la suivante:<br/>minimum: 10 000 u * 0,3% = 30 U<br/>maximum: 10 000 u * 0,4% = 40 u<br/>C'est - à - dire que vous pouvez obtenir 150 u ~ 200 u de revenus après 5 jours. Les revenus sont distribués quotidiennement et les revenus distribués peuvent être retirés à tout moment. Après l'expiration du principal verrouillé, ils seront automatiquement transférés à votre compte de fonds.",
		aboutLiquidatedDamages: "À propos des dommages - intérêts liquidés",
		aboutLiquidatedDamagesContent: "Si vous souhaitez transférer le principal non échu, des dommages - intérêts liquidés se produiront, c'est - à - dire la proportion de règlement par défaut * Les jours restants * Le nombre de positions verrouillées.<br/>exemple: si la proportion de règlement par défaut de l'exploitation minière de l'entrepôt verrouillé est de 0,4%, les 3 jours restants expirent et le nombre d'entrepôts verrouillés est de 1000, alors les dommages - intérêts liquidés = 0,4% * 3 * 1000 = 12u, et le principal effectivement remboursé est de 1000u - 12u = 988u",
		joinNow: "J'y vais.",
		day: "Oh, mon Dieu.",
		daily: "Tous les jours",
		returnOnExpiration: "Retour dû",
		get: "Accès",
		numberOfCoinsDeposited: "Nombre de pièces en stock",
		subscribe: "Abonnement",
		insufficientBalance: "Solde insuffisant",
		p_number: "Veuillez entrer la quantité",
		leastSingle: "Minimum par stylo",
		lockedPositionList: "Liste des serrures",
		inProgress: "En cours",
		redeemed: "Racheté",
		expiryTime: "Échéance",
		lockUpTime: "Temps de verrouillage",
		earlyRedemptionPenalty: "Pénalité pour remboursement anticipé",
		redemption: "Remboursement",
		c_redemption: "Êtes - vous sûr d'un remboursement anticipé?",
	},
	invest: {
		invest: "Gestion financière",
		section: "Section spécialisée",
		perPrice: "Prix unitaire",
		apr: "Rendement annualisé",
		linkedReferencePrice: "Prix de référence indexé",
		holdingDays: "Période de détention",
		maturityDate: "Date d'échéance",
		remainingShares: "Part restante",
		progress: "Progrès actuels",
		action: "Fonctionnement",
		purchase: "Achats",
		myPosition: "Ma gestion financière",
		buyShares: "Part des achats",
		estimatedIncome: "Recettes prévues",
		day: "Oh, mon Dieu.",
		share: "Part",
		fail: "Échec",
		purchased: "Acheté",
		settlementing: "En cours de règlement",
		success: "Terminé",
		all: "Tous",
		name: "Nom",
		remain: "Reste",
		instruction: "Description du produit",
		help: "Aide",
		shuoming_1: '1. Ce produit est un produit de gestion de patrimoine sans capital garanti. Les fluctuations du marché peuvent entraîner une perte de capital. Veuillez investir avec prudence.',
		shuoming_2: `2. Règles d'encaissement et de règlement des paiements :`,
		shuoming_3: `Si prix de règlement＜prix indexé, le BTC sera réglé après l'expiration ; montant du règlement=montant de l'investissement*(1+jours*taux de rendement annualisé/365).`,
		shuoming_4: `Si le prix de règlement est supérieur ou égal au prix indexé, l'USDT sera réglé après l'expiration ; montant du règlement = montant de l'investissement * prix indexé * (1+ jours * taux de rendement annualisé/365).`,
		shuoming_5: `3. Le taux de rendement annualisé réel change avec le marché en temps réel, veuillez vous référer à la transaction d'achat réelle.`,
		shuoming_6: `4. Le montant de l'investissement est calculé en temps réel avec le marché, veuillez vous référer à la transaction d'achat réelle.`,
		shuoming_7: `5. Une fois le produit acheté, il peut être consulté sur la page My Coin Holding.Après l'expiration, le paiement sera automatiquement émis sur le compte spot.`,
		error_1: "Veuillez entrer la quantité achetée",
		error_2: "La quantité achetée doit être un entier",
		confirmPurchase: "Êtes - vous sûr d'acheter ce produit financier?",
		purchaseSuccess: "Achat réussi!",
	},
	follow: {
		follow: "Suivi",
		traderList: "Liste des commerçants",
		totalProfit: "Total des profits et pertes",
		correctRate: "Précision totale",
		positionValuation: "Évaluation des positions",
		estimatedProfitToday: "Prévisions de recettes pour aujourd'hui",
		rateOfReturn: "Taux de rendement",
		becomeATrader: "Devenir un commerçant",
		traderList: "Liste des commerçants",
		onlyShowsThePosition: "Afficher uniquement les emplacements",
		theDataIsUpdatedEveryHour: "Les données sont mises à jour toutes les heures",
		following: "Suivi",
		follow: "Suivi",
		confirmFollow: "Tu es sûr de suivre?",
		confirmCancel: "Tu es sûr d'annuler le suivi?",
		followTrader: "Suivre les commerçants",
		editCopy: "Modifier la suite",
		beforeCopy: "Lisez attentivement avant de choisir de suivre",
		copyAgreement: "Suivre l'Accord",
		copyType: "Mode de suivi",
		copyType1: "Multiple fixe suivant",
		copyType2: "Nombre fixe de mains suivant",
		copyAlert1: "Quel que soit le nombre de commandes passées par le commerçant, vous suivez un multiple fixe de votre choix.",
		copyAlert2: "(lorsque le nombre proportionnel de mains est supérieur à 1 et qu'il n'est pas un entier, l'arrondi conserve la valeur à deux décimales près)",
		copyMultiple: "Suivre les multiples",
		cancelCopy: "Annuler le suivi",
		currentFollowerNumber: "Nombre actuel de suiveurs",
		totalProfitAndLoss: "Total des profits et pertes",
		totalReturn: "Rendement total",
		accuracy: "Précision",
		profitableOrders: "Commandes rentables",
		successfulShortTrades: "Négociation à découvert réussie",
		successfulLongTrades: "Faire plusieurs transactions avec succès",
		yesterdaysTradingStatus: "Conditions commerciales d'hier",
		currentPositions: "Position actuelle",
		totalOrders: "Total des commandes",
		remain: "Reste",
		distanceDue: "Échéance de la distance",
		days: "Oh, mon Dieu.",
		deadline: "Durée",
		subscription: "Abonnement",
		followUsers: "Suivre les utilisateurs",
		followAmount: "Montant suivant",
		followEarnings: "Suivre les gains",
		cancelFollow: "Annuler le suivi",
		currentCopy: "Suivi actuel",
		historicalCopy: "Transactions historiques",
		myTrader: "Mon trader",
		tradingPair: "- Oui.",
		open: "Prix d'ouverture",
		close: "Prix de clôture",
		earnings: "Recettes",
		lots: "Nombre de mains",
		time: "Heure de la transaction",
		buyIn: "Acheter",
		buyOut: "Vendre",
		direction: "Orientation",
	},
	financial: {
		product: "Produits",
		position: "Détention",
		financial: "Finances",
		lockming: "Mine lock - out",
		ieo: "Ieo",
		invest: "Investir",
		section: "Section spécialisée",
		apr: "Rendement annualisé",
		linkedReferencePrice: "Prix de référence indexé",
		holdingDays: "Période de détention",
		day: "Oh, mon Dieu.",
		lockPeriod: "Cycle de verrouillage",
		ing: "En cours",
		done: "Terminé.",
		applySubscription: "Demande d'abonnement",
		remaining: "Reste",
		total: "Total",
		subscribed: "Souscrite",
		mining: "exploitation minière",
		minimum: "Lève - toi.",
		dailyReturnRate: "Rendement quotidien",
	},
	nft: {
		nft: "NFT",
		artwork: "Art.",
		artist: "Artistes",
		artworkList: "Liste des oeuvres d'art",
		artistList: "Liste des artistes",
		comprehensiveSorting: "Classement général",
		newest: "Dernière mise à jour",
		hotest: "Le plus chaud",
		mostWorks: "Le plus grand nombre d'oeuvres",
		mostPopular: "Les plus populaires",
		record: "Historique des achats",
		recommendArtist: "Artistes recommandés",
		buy: "Achats",
		artistHomepage: "Page d'accueil de l'artiste",
		works: "Travaux",
		saled: "Vendu",
		fans: "Fans",
		allWorks: "Tous les travaux",
		notSell: "Non vendu",
		myCollect: "Ma collection",
		collect: "Collection",
		own: "Détention",
		placeABid: "Offres",
		purchase: "Achats",
		open: "Ouverture postérieure",
		image: "Photos",
		gif: "Figure mobile",
		audio: "Audio",
		video: "Vidéo",
		creator: "Auteur",
		currentBid: "Dernière offre",
		price: "Prix",
		d: "Oh, mon Dieu.",
		h: "Heure",
		m: "Points",
		s: "Secondes",
		saled: "Terminé.",
		place_alert: "Article non vendu ou terminé",
		createdBy: "Auteur",
		place_alert2: "Votre offre doit être supérieure à",
		note: "Important",
		CURRENT_BID: "Dernière offre",
		place_alert3: "Participer à l'appel d'offres, vous devez payer",
		place_alert4: "À la fin de l'appel d'offres, le dépôt sera retourné à votre compte.",
		place_alert6: "Clôture des soumissions",
		confirmBid: "Confirmer l'offre",
		DESCRIPTION: "Description",
		ADDRESS: "Adresse",
		copy: "Copier",
		per_increase: "Chaque augmentation",
		currency_type: "Type de monnaie",
		pay_type: "Mode d'achat",
		pay_type_2: "Mode de vente",
		normal: "Fréquent",
		auction: "Appel d'offres",
		confirmAuction: "Êtes - vous sûr de participer à l'appel d'offres pour cet article?",
		bindBox: "Boîte aveugle",
		sell_status: "État des ventes",
		hasStart: "A commencé",
		willStart: "Non commencé",
		artwork_type: "Type d'article",
		reset: "Réinitialiser",
		confirm: "C'est sûr.",
		place_alert5: "Veuillez saisir un mot clé",
		collection: "Collecté",
		byCollection: "Collecté",
		artworks: "Travaux",
		goCollect: "Aller à la collection",
		price: "Prix",
		nonickname: "Aucun surnom défini",
		cjdjs: "Date limite de soumission des offres",
		cjkssj: "Heure de début de l'offre",
		cjjzsj: "Date limite de soumission des offres",
		gotosee: "Allez au marché.",
		position: "Détention",
		resell: "Revente",
		buyPrice: "Prix d'achat",
		confirmResell: "Confirmation de la revente",
		wrongPrice: "Veuillez sélectionner le bon prix",
		bidPlacedBy: "Soumissionnaire",
		placeRecord: "Historique des offres",
		confirPurchase: "Confirmer l'achat",
		artistList: "Liste des artistes",
		hasOpen: "Ouvert",
		noOpen: "Non ouvert",
		clickOpen: "Ouvrir la boîte aveugle",
		confirmOpen: "Tu es sûr d'ouvrir la boîte aveugle?",
		margin: "Dépôt de garantie",
		wrongPrice: "Veuillez saisir le prix correct",
		wrongPerIncrease: "Veuillez saisir le Supplément correct",
		wrongTime: "L'heure de fin doit être postérieure à l'heure de fin",
		confirmResell2: "Une fois la marchandise revendue, irrévocable, êtes - vous sûr de vouloir la revendre?",
		reselling: "En cours de revente",
		message: "Notification des messages",
		confirmPay: "Êtes - vous sûr de vouloir payer pour cet article?",
		zfdjs: "Compte à rebours des paiements",
		payTime: "Délai de paiement",
		hasRead: "Lire",
		lastPrice: "Prix de transaction final",
		isPay: "Payé",
		isExpired: "Expiré",
		rarity: "Rareté",
		place_alert6: `Après la date limite d'enchère, si votre enchère est retenue, vous aurez un jour pour payer le prix final. Si le paiement est en retard, votre dépôt de garantie sera déduit.`,
	}
}

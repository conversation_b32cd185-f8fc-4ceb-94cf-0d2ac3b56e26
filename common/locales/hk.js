export default {
	nav: ['首頁', '行情', '交易', '金融', '資產'],
	common: {
		search: '搜索',
		confirm: "確定",
		cancel: "取 消",
		submit: "提 交",
		logout: '退 出',
		all: "全 部",
		edit: "編 輯",
		delete: "刪 除",
		reset: "重 置",
		filter: "篩 選",
		login: "登 錄",
		register: "注 冊",
		email: "郵箱",
		mobile: "手機號",
		account: "用戶名",
		password: "密碼",
		passwordPlaceholder: "請輸入密碼",
		noaccount: "沒有賬號？",
		go: "去",
		forgetPassword: "忘記密碼",
		confirmPassword: "確認密碼",
		invitecode: "邀請碼",
		hasaccount: "已有賬號？",
		mobilecodePlaceholder: "請輸入手機驗證碼",
		emailcodePlaceholder: "請輸入郵箱驗證碼",
		send: "發送驗證碼",
		selectArea: "地區選擇",
		audit: "審核",
		underAudit: "審核中",
		auditFailure: "審核失敗",
		untie: "解綁",
		buy: "買",
		sell: "賣",
		plsLogin: "請先登錄",
		plsInputUsername: "請輸入賬戶",
		plsInputRePassword: "請輸入確認密碼",
		plsInputInviteCode: "請輸入邀請碼",
		plsInputCode: "請輸入驗證碼",
		plsInputMobile: "請輸入手機號",
		plsInputEmail: "請輸入郵箱",
		login_success: "登錄成功",
		login_fail: "登錄失敗",
		reg_success: "註冊成功",
		reg_fail: "登錄失敗",
		pwdInconsistent: "兩次密碼不一致",
		hint: "提示",
		loading: '加載中',
		success: "成功",
		plsInput: "請輸入",
		click: "點擊",
		quotation: "行情",
		edit: "編輯",
		second: '秒',
		specialChart: '不允許包含特殊字符',
		open: '開盤價',
		close: '收盤價',
		high: '最高價',
		low: '最低價',
		customerService: '聯繫客服',
		rememberPassword: '記住密碼',
		newPassword: '新密碼',
		pwdMoreThen6: '密碼的長度應大於六位',
		codeLength6: '驗證碼長度應為六位',
		rePwdMoreThen6: '確認密碼的長度應大於六位',
		pls: '請',
		saveSuccess: "已保存至系統相冊",
		nextStep: "下一步",
		log: '記錄',
		paramsWrong: "參數錯誤",
		functionLoading: "功能開發中",
		DIGICCY: "數字貨幣",
		quickly: "快速",
		more: "更多",
		hasNoData: '沒有更多數據了',
		audioError: '音頻文件出錯',
		toLong: '字符過長',
		selectLang : "選擇語言",
	},
	home: {
		contactUs: '在線客服',
		borrowing: '借貸',
		borrowing_entry: '借貸入口',
		repayment_entry: '還貸入口',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: '總資產折合',
		recharge: '充值',
		withdraw: '提現',
		stock: '股票',
		futures: '合約',
		copytrade: '跟單',
		financial: '金融理財',
		margin: '超級槓桿',
		otc: 'OTC',
		coupon: '卡券中心',
		bills: '賬單',
		exchange: '匯兌',
		mining: '鎖倉賺幣',
		game: '小遊戲',
		chatroom: '聊天室',
		quicklyBuyCoins: '快捷買幣',
		defiMining: 'DeFi挖礦',
		cloudMining: '雲礦機',
		stock: '股票',
		stock_info: '無需開倉，立即持倉',
		lockming: '鎖倉挖礦',
		transfer: "幣幣劃轉",
		ieo: "IEO",
		tradeQuery: "交易查詢",
		seconds: "期權",
		day: "天",
		minimum: '起',
		dailyReturnRate: "日收益率",
		more: "更多",
		market: "行情",
		tradingPair: '交易對',
		lastPrice: '最新價',
		todayChange: '今日漲跌幅',
		transaction: '交易',
		nft: 'NFT',
		invest: "投資理財",
		download: '下載',
		app_text: '隨時隨地，在線交易'
	},
	setting: {
		my_qrcode: '我的二維碼',
		mine: '我的',
		inviteDesc: '邀請好友，共同返利',
		inviteDesc2: '與好友共同享受返利',
		inviteNumber: '邀請人數',
		inviteTransNumber: '已交易人數',
		myRebate: '我的返利',
		bill: "賬單",
		wallet: "錢包地址",
		banks: "銀行卡綁定",
		banksInternational: "銀行卡綁定『國際』",
		addBank: "添加銀行卡",
		securitySettings: "安全設置",
		coupon: "卡券中心",
		systemNotification: "系統通知",
		defaultFiatCurrency: "默認法幣",
		language: "語言",
		faq: "常見問題",
		operationalCompliance: "經營合規",
		share: "分享",
		contactUs: "聯繫我們",
		aboutUs: "關於我們",
		avatar: '頭像',
		nickname: '暱稱',
		signature: '個性簽名',
		grxx: "個人信息",
		grxxOptions: {
			nickname: {
				title: '暱稱',
				placeholder: '請輸入暱稱',
				desc: '2-10個字符，支持中英文、數字',
			},
			signature: {
				title: '個性簽名',
				placeholder: '請輸入個性簽名',
				desc: '2-30個字符，支持中英文、數字',
			}
		},
		xxtx: "上傳頭像",
		photograph: '拍照',
		selectFromAlbum: '從相冊選擇',
		aqsz: "安全設置",
		editLoginPassword: '修改登錄密碼',
		setPayPassword: "設置支付密碼",
		editPayPassword: "修改支付密碼",
		bindPhone: "綁定手機",
		email: '郵箱',
		stayLogin: '保持登錄',
		xgdlmm: "修改登錄密碼",
		currentPassword: '當前登錄密碼',
		currentPasswordPlaceholder: '請輸入當前登錄密碼',
		newPassword: "新登錄密碼",
		newPasswordPlaceholder: "請輸入新登錄密碼",
		confirmNewPassword: "確認新密碼",
		confirmNewPasswordPlaceholder: "請輸入確認新密碼",
		currentPayPassword: "當前支付密碼",
		currentPayPasswordPlaceholder: '請輸入當前支付密碼',
		newPayPassword: "新支付密碼",
		newPayPasswordPlaceholder: "請輸入新支付密碼",
		confirmNewPayPassword: "確認新支付密碼",
		confirmNewPayPasswordPlaceholder: "請輸入確認新支付密碼",
		phoneVerificationCode: '手機驗證碼',
		editPhoneVerificationCode: "請輸入手機驗證碼",
		getVerificationCode: '獲取驗證碼',
		lctyq: "理財體驗券",
		expired: "已失效",
		expiredTime: "失效日期",
		useImmediately: "立即使用",
		addNewAccount: "添加新賬號",
		totayIncome: "今日收款",
		totalIncome: "累計收款",
		selectLang: "請選擇語言",
		selectCoinType: "請選擇幣種",
		authentication: '身份認證',
		authentication1: '基礎認證',
		authentication2: '高級認證',
		authentication_text_1: '個人身份認證',
		authentication_text_2: '完成認證後可獲得的權限：',
		authentication_text_3: 'Lv1.基礎認證',
		authentication_text_4: '認證後可以體現，24小時限額200BTC',
		authentication_text_5: '認證後可以進行法幣交易，單筆限額2000USDT',
		authentication_text_6: 'Lv2.高級認證',
		authentication_text_7: '增加體現額度，24小時限額500BTC',
		authentication_text_8: '增加法幣交易額度，單筆限額1000000USDT',
		authentication_text_9: '溫馨提示：為保護用戶資金安全，請先完成郵箱和電話的綁定，否則您的認證將無法通過。 ',
		goAudit: '去認證',
		noaudit: '未認證',
		auditing: '認證中',
		hasaudit: '已認證',
		idType: '證件類型',
		idCard: '身份證',
		passport: '護照',
		driverLicense: '駕駛證',
		name: '姓名',
		idcard: '證件號碼',
		plsIptName: '請輸入姓名',
		plsIptCorrectIdcard: '請輸入正確的身份證號',
		uploadIdcardFront: '上傳證件正面',
		uploadIdcardReverse: '上傳證件背面',
		emailVerificationCode: '郵箱驗證碼',
		plsiptEmailCode: '請輸入郵箱驗證碼',
		addWallet: '添加錢包地址',
		selectCurrency: '選擇幣種',
		plsSelectCurrency: '請選擇幣種',
		plsIptAddress: '請輸入錢包地址',
		walletAddress: '錢包地址',
		walletQrcode: '錢包二維碼',
		plsIptWalletAddress: '請輸入錢包地址',
		plsUploadWalletQrcode: '請上傳錢包二維碼',
		confirmLogout: '確定退出登錄嗎？ ',
		nonickname: '請設置暱稱',
		plsInputMobile: "請輸入手機號",
		alipay_account: 'ABA號',
		real_name: '真實姓名',
		bank_account: '銀行卡號',
		bank_dizhi: '開戶省市',
		bank_name: '開戶行',
		bank_network: '開戶網點',
		swift_code: '國際支付代碼',
		phone: '聯絡電話',
		wechat_account: '持卡人地址',
		wechat_nickname: 'SWIFT代碼',
		allNeed: '所有均為必填項',
		phoneNumber : "手機號",
		code : "驗證碼",
		sendCode : "發送驗證碼",
		plsIptCode : "請輸入驗證碼",
		withdrawPassword: '提現密碼',
		confirmWithdrawPassword: '確認提現密碼',
		plsInputWithdrawPassword: '請輸入提現密碼',
		plsInputConfirmWithdrawPassword: '請輸入確認提現密碼',
		withdrawPasswordPlaceholder: '請輸入6-16位提現密碼',
		confirmWithdrawPasswordPlaceholder: '請再次輸入提現密碼',
		updateWithdrawPassword: '修改提現密碼',
		oldWithdrawPassword: '原提現密碼',
		newWithdrawPassword: '新提現密碼',
		plsInputOldWithdrawPassword: '請輸入原提現密碼',
		plsInputNewWithdrawPassword: '請輸入新提現密碼',
		withdrawPasswordNotMatch: '兩次輸入的提現密碼不一致',
		withdrawPasswordLengthError: '提現密碼長度應為6-16位',
		withdrawPasswordRequired: '請設置提現密碼',
		withdrawPasswordVerifyFailed: '提現密碼驗證失敗',
		needRealAuth: "交易前需要完成實名認證",
		realAuthPending: "實名認證審核中，請等待審核完成",
		score: "信用分"
	},
	transaction: {
		actualPL: '實際盈虧',
		favorites: "自選",
		futures: "合約",
		coins: "幣幣",
		stock: "股票",
		tradingPair: '交易對',
		lastPrice: '最新價',
		todayChange: '今日漲跌幅',
		searchPlaceholder: '搜索您關心的幣種/股票名稱/代碼',
		hotSearch: '熱門搜索',
		recentView: '最近查看',
		dayHigh: "今日高",
		dayLow: "今日低",
		optional: '自選',
		buy: '買入',
		sell: '賣出',
		entrustPendingOrder: '委託掛單',
		addOptionalSuccess: '添加自選成功',
		delOptionalSuccess: '刪除自選成功',
		seconds: "期權",
		home: "首頁",
		orderConfirm: "訂單確認",
		currentPrice: '當前價',
		direction: "方向",
		selectTime: "選擇到期時間",
		number: "數量",
		balance: '賬戶餘額',
		plsIptCrtNumber: '請輸入正確的數量',
		buyPrice: '購買價',
		expectedPL: "預計盈虧",
		continueTrade: "繼續交易",
		secondsPosition: '期權持倉',
		position: '在持',
		history: '歷史',
		orderTimes: '訂單時長',
		pl: "盈虧",
		sellTime: "賣出時間",
		buy: '買入',
		sell: '賣出',
		price: '價格',
		time: "時間",
		optional: '自選',
		buy: '買入',
		sell: '賣出',
		long: "做多",
		short: "做空",
		entrustPendingOrder: '委託掛單',
		addOptionalSuccess: '添加自選成功',
		delOptionalSuccess: '刪除自選成功',
		price: "價格",
		amount: "數量",
		marketPrice: "市價",
		limitPrice: "限價",
		number: "交易手數",
		margin: "保證金",
		handlingFee: "手續費",
		balance: "餘額",
		multiple: "倍數",
		recharge: '充值',
		plsIptCommissionPrice: '請輸入委託價格',
		tradingPair: "交易對",
		direction: "方向",
		plsIptCrtPrice: '請輸入正確的價格',
		position: "持倉",
		time: "時間",
		turnover: "交易額",
		operation: "操作",
		type: "類型",
		cover: '平倉',
		confirmCover: '確認平倉嗎？ ',
		confirmSelfHold: '確定轉自持嗎？ ',
		selfHolding: '自持',
		transSelfHold: '轉自持',
		delegateList: "委託列表",
		riskRate: "風險率",
		totalPandL: '持倉總收益',
		oneClickCover: "一鍵平倉",
		open: "開倉價",
		targetProfitPrice: "止盈價",
		updatePrice: "當前價",
		stopLossPrice: "止損價",
		overnightMoney: "隔夜費",
		openTime: "開倉時間",
		lots: "手",
		setProfitLoss: "設置止盈止損",
		expectedProfit: "預期盈利",
		expectedLoss: "預期虧損",
		coverAll: "全部平倉",
		onlyCoverMany: "只平多單",
		onlyCoverEmpty: "只平空單",
		numbers: '數量',
		coinBuyPrice: '以當前最優價格交易',
		jine: '金額',
		p_jine: '請輸入金額',
		p_price: '請輸入價格',
		p_confirm_sj: '確定以市價',
		p_confirm_xj_1: '確定以現價',
		p_confirm_ma: '嗎？ ',
		status: "狀態",
		complete: "已完成",
		doing: "進行中",
		currentEntrust: "當前委託",
		allEntrust: "全部委託",
		entrust: "委託",
		entrustTime: "委託時間",
		all: "全部",
		buyUp: "買多",
		buyDown: "買少",
		jye:'交易額',
	},
	fund: {
		overview: '總覽',
		balance: '餘額',
		futures: '合約',
		deposit: '存款',
		mining: '鎖倉賺幣',
		stock: '股票',
		margin: '超級槓桿',
		yesterdayEarnings: '昨日收益',
		yieldAtMaturily: '到期收益',
		totalkRevenue: '總收益',
		currentProfit: '當前盈虧',
		profitRatio: '盈虧率',
		receive: '充值/收款',
		transfer: '提現/轉賬',
		financial: '金融理財',
		valuation: '估值',
		currentEarnings: '當前收益',
		yesterdayIncome: '昨日收益',
		crypto: '數字貨幣',
		fiat: '法幣',
		hideSmallCurrency: '隱藏小額幣種',
		available: '可用',
		freeze: '凍結',
		equivalent: '折合',
		usdtm: 'U本位',
		coinm: '幣本位',
		exchange: "幣幣",
		leverage: '合約',
		fiat: '法幣',
		second: '期權',
		convert: "總資產折合",
		convert2: "資產折合",
		record: "帳變記錄",
		availableQuota: "可用額度",
		locked: "鎖定",
		converted: "折合",
		account: "賬戶",
		financialRecords: "財務記錄",
		number: "數量",
		record2: "記錄",
		time: "時間",
		transfer: "幣幣劃轉",
		from: "從",
		to: "至",
		transferNumber: "劃轉數量",
		youGet: "你將得到",
		exchangeRate: "兌換匯率",
		handlingFee: "手續費",
		fundTransfer: "資金劃轉",
		insufficientBalance: "餘額不足",
		needHandlingFee: "另需手續費",
		plsIptCrtNumber: '請輸入正確的數量',
		c_transfer: "確定劃轉嗎？",
		selectCurrency: "選擇幣種",
		DIGICCY: '數字貨幣',
		saveQrcode: '保存二維碼',
		copyAddress: '複製鏈接地址',
		plsTrans: '請往此地址劃轉',
		rechargeNumer: "充值數量",
		paymentVoucher: "支付憑證",
		plsIptRechargeNumer: "請輸入充值數量",
		plsUploadPaymentVoucher: "請上傳支付憑證",
		plsIptCrtAmount: '請輸入正確的金額',
		withdraw: "提現/轉賬",
		currencyName: "幣種名稱",
		transactionInfo: "交易賬戶",
		withdraw2: '提現',
		canUse: '可用',
		all: '全部',
		plsIptWithdrawNumber: '請輸入提現數量',
		withdrawNumber: '提現數量',
		remark: '備註',
		handlingFee: '手續費',
		canGetNumber: '到賬數量',
		leastNumber: '最低提現數量',
		coinType: '幣種',
		address: '地址',
		date: '日期',
		withdrawAddress: '提幣地址',
		plsSelectWithdrawAddress: '請選擇提幣地址',
		withdrawToCard: '提到銀行卡',
		withdrawToCardInternational: '提到國際銀行卡',
		withdrawToAddress: '提到錢包地址',
		card: '銀行卡',
		addCard: '添加銀行卡',
		plsAddCard: '請添加銀行卡',
		plsInputWithdrawPasswordForWithdraw: '請輸入提現密碼進行驗證'
	},
	ieo: {
		subscribe: "認購",
		ing: "進行中",
		done: "已結束",
		applySubscription: "申請認購",
		remaining: "剩餘",
		lockPeriod: "鎖倉週期",
		endTime: "結束時間",
		subscribeCurrency: "認購幣種",
		issuancePrice: "發行價格",
		totalIssuance: "發行總量",
		whitePaper: "白皮書",
		subscriptionCycle: "申購週期",
		warmUp: "預熱",
		website: "官網",
		finish: "結束",
		projectDetails: "項目詳情",
		subscriptionConfirmation: "認購確認",
		p_applicationsNumber: "輸入認購數量",
		needToPay: "需要支付",
		currentBalance: "當前餘額",
		subscriptionTime: "申購時間",
		currency: "幣種",
		applicationsNumber: "申請數量",
		passesNumber: "通過數量",
		timeToMarket: "上市時間",
		day: "天",
		total: "總量",
		subscribed: '已認購',
		shengou: "申購",
		insufficientBalance: "餘額不足",
		p_number: "請輸入數量",
		mySubscribe: "我的認購"
	},
	lockming: {
		lockming: '鎖倉挖礦',
		fundsUnderCustody: "正在託管的資金",
		entrustedOrders: "委託訂單",
		estimatedTodayIncome: "預計今日收益率",
		cumulativeIncome: "累計收益",
		ordersInCustody: "託管中的訂單",
		lockedPositionsToEarnCoins: "鎖倉賺幣",
		lockedPositions: "鎖倉",
		minimumSingleTransaction: "單筆最少",
		dailyYield: "日收益率",
		lockUpPeriod: "鎖倉週期",
		acquisition: "獲得",
		recentDays: "近日",
		dividendTime: "派息時間",
		escrowFunds: "託管資金",
		redemptionInAdvance: "提前贖回",
		estimatedIncome: "預計收益",
		availableAssets: "可用資產",
		investmentAmountObtained: "投資金額獲得",
		all: "全部",
		l_alert_1: "挖礦轉不停",
		l_alert_2: "鎖倉挖礦是通過將usdt託管給平台超算力礦機在平台礦池中進行挖礦收益",
		features: "產品亮點",
		onDemand: "隨存隨取",
		dividendPeriod: "派息週期",
		issuedDaily: "每日下發",
		currentInterest: "活期利息",
		feature_1: "100%資金安全<br>保障",
		feature_2: "節假日收益<br>不簡斷",
		feature_3: "成功存入後<br>當天起息",
		forInstance: "舉個例子",
		incomeCalculation: "收益計算",
		forInstanceDesc: "會員在平台鎖倉10000U，選擇了週期為5天，日產出為鎖倉金額的0.3%-0.4%的理財產品，則每天產出如下：<br />最低：10000U*0.3%=30U<br />最高：10000U*0.4%=40U<br />即5天後可以獲得150U~200U的收益，收益每日下發，下發的收益可隨時存取，鎖倉本金到期後，自動轉入您的資金賬戶。",
		aboutLiquidatedDamages: "關於違約金",
		aboutLiquidatedDamagesContent: "若您希望轉出未到期的本金，則會產生違約金，違約金=違約結算比例*剩餘天數*鎖倉數量。<br />舉例：該鎖倉挖礦的違約結算比例為0.4%，剩餘3天到期，鎖倉數量為1000，則違約金=0.4%*3*1000=12U，實際退還本金為1000U-12U=988U",
		joinNow: "我要參加",
		day: "天",
		daily: "每日",
		returnOnExpiration: "到期返回",
		get: "獲得",
		numberOfCoinsDeposited: "存幣數量",
		subscribe: "認購",
		insufficientBalance: "餘額不足",
		p_number: "請輸入數量",
		leastSingle: "單筆最少",
		lockedPositionList: "鎖倉列表",
		inProgress: "進行中",
		redeemed: "已贖回",
		expiryTime: "到期時間",
		lockUpTime: "鎖倉時間",
		earlyRedemptionPenalty: "提前贖回違約金",
		redemption: '贖回',
		c_redemption: "確定提前贖回嗎？"
	},
	invest: {
		invest: '理財',
		section: '專區',
		perPrice: '單份價格',
		apr: "年化收益率",
		linkedReferencePrice: "掛鉤參考價",
		holdingDays: "持倉期限",
		maturityDate: "到期日",
		remainingShares: "剩餘份額",
		progress: "當前進度",
		action: "操作",
		purchase: '購買',
		myPosition: '我的理財',
		buyShares: '買入份額',
		estimatedIncome: "預計收益",
		day: '天',
		share: '份',
		fail: '失敗',
		purchased: '已買入',
		settlementing: '結算中',
		success: '已完成',
		all: "全部",
		name: '名稱',
		remain: '剩餘',
		instruction: "產品說明",
		help: '幫助',
		shuoming_1: '1.本產品為非保本理財產品，市場波動可能導致本金損失，請謹慎投資.',
		shuoming_2: '2.回款結算規則：',
		shuoming_3: '如果結算價＜掛鉤價，到期後結算BTC；結算數量=投資金額*(1+天數*年化收益率/365).',
		shuoming_4: '如果結算價≥掛鉤價，到期後結算USDT；結算數量=投資金額*掛鉤價*(1+天數*年化收益率/365）.',
		shuoming_5: '3.實際年化收益率隨市場實時變動，請以實際買入成交為準.',
		shuoming_6: '4.投資金額隨市場實時折算得出，請以實際買入成交為準.',
		shuoming_7: '5.產品購買後可在我的持幣頁面進行查看，到期結束後回款會自動發放至現貨賬戶.',
		error_1: '請輸入購買數量',
		error_2: '購買數量應為整數',
		confirmPurchase: '確定購買該理財產品嗎？ ',
		purchaseSuccess: '購買成功!',
	},
	follow: {
		follow: '跟單',
		traderList: '交易員列表',
		totalProfit: '總盈虧',
		correctRate: '總準確率',
		positionValuation: "持倉估值",
		estimatedProfitToday: "今日預計收益",
		rateOfReturn: "收益率",
		becomeATrader: "成為交易員",
		traderList: "交易員列表",
		onlyShowsThePosition: "僅顯示有位置",
		theDataIsUpdatedEveryHour: "數據每小時更新一次",
		following: "跟單中",
		follow: "跟單",
		confirmFollow: '確定跟隨嗎？ ',
		confirmCancel: '確定取消跟隨？ ',
		followTrader: '跟隨交易員',
		editCopy: "編輯跟隨",
		beforeCopy: "選擇跟隨之前請仔細閱讀",
		copyAgreement: "跟隨協議",
		copyType: "跟隨方式",
		copyType1: "固定倍數跟隨",
		copyType2: "固定手數跟隨",
		copyAlert1: "無論交易員下單多少，您都按選擇的固定倍數跟隨。",
		copyAlert2: "(當比例手數>1且不為整數時，四捨五入保留兩位小數取值）",
		copyMultiple: "跟隨倍數",
		cancelCopy: "取消跟隨",
		currentFollowerNumber: "當前跟隨人數",
		totalProfitAndLoss: "總盈虧",
		totalReturn: "總收益率",
		accuracy: "準確率",
		profitableOrders: "盈利訂單",
		successfulShortTrades: "成功做空交易",
		successfulLongTrades: "成功做多交易",
		yesterdaysTradingStatus: "昨日交易狀況",
		currentPositions: "當前持倉",
		totalOrders: "總訂單",
		remain: '剩餘',
		distanceDue: "距離到期",
		days: "天",
		deadline: "期限",
		subscription: "申購",
		followUsers: "跟隨用戶",
		followAmount: "跟隨金額",
		followEarnings: "跟隨收益",
		cancelFollow: "取消跟隨",
		currentCopy: "當前跟單",
		historicalCopy: "歷史交易",
		myTrader: "我的交易員",
		tradingPair: '交易對',
		open: '開倉價',
		close: '平倉價',
		earnings: '收益',
		lots: '手數',
		time: '交易時間',
		buyIn: '買入',
		buyOut: '賣出',
		direction: '方向'
	},
	financial: {
		product: '產品',
		position: '持有',
		financial: '金融',
		lockming: '鎖倉挖礦',
		ieo: "IEO",
		invest: "投資理財",
		section: '專區',
		apr: "年化收益率",
		linkedReferencePrice: "掛鉤參考價",
		holdingDays: "持倉期限",
		day: '天',
		lockPeriod: "鎖倉週期",
		ing: "進行中",
		done: "已結束",
		applySubscription: "申請認購",
		remaining: "剩餘",
		total: "總量",
		subscribed: '已認購',
		mining: '鎖倉賺幣',
		minimum: '起',
		dailyReturnRate: "日收益率",
	},
	nft: {
		nft: 'NFT',
		artwork: '藝術品',
		artist: '藝術家',
		artworkList: '藝術品列表',
		artistList: '藝術家列表',
		comprehensiveSorting: '綜合排序',
		newest: '最新的',
		hotest: '最熱的',
		mostWorks: '作品最多',
		mostPopular: '最受歡迎',
		record: '購買記錄',
		recommendArtist: '推薦的藝術家',
		buy: '購買',
		artistHomepage: '藝術家主頁',
		works: '作品',
		saled: '已售',
		fans: '粉絲',
		allWorks: '全部作品',
		notSell: '未售出',
		myCollect: '我的收藏',
		collect: "收藏的",
		own: "持有",
		placeABid: '出價',
		purchase: '購買',
		open: '後開放',
		image: '圖片',
		gif: '動圖',
		audio: '音頻',
		video: '視頻',
		creator: '作者',
		currentBid: '最新出價',
		price: '價格',
		d: '天',
		h: '時',
		m: '分',
		s: '秒',
		saled: '已結束',
		place_alert: '物品未到售賣時間或已結束',
		createdBy: '作者',
		place_alert2: "您的出價必須高於",
		note: '重要',
		CURRENT_BID: '最新出價',
		place_alert3: '參與競拍，您需要支付',
		place_alert4: '的保證金，競拍結束時，保證金將返還至您的賬戶。 ',
		place_alert6: '出價時間截止後,若您競拍成功，您將有一天的時間支付最終價格。若支付超時，則會扣除您的保證金。 ',
		confirmBid: '確認出價',
		DESCRIPTION: '描述',
		ADDRESS: '地址',
		copy: '複製',
		per_increase: '每次加價',
		currency_type: '貨幣類型',
		pay_type: '購買方式',
		pay_type_2: '售賣方式',
		normal: '普通',
		auction: '競拍',
		confirmAuction: '確定參與競拍該商品嗎?',
		bindBox: '盲盒',
		sell_status: '售賣狀態',
		hasStart: '已開始',
		willStart: '未開始',
		artwork_type: '物品類型',
		reset: '重置',
		confirm: '確定',
		place_alert5: '請輸入關鍵詞',
		collection: '已收藏',
		byCollection: '被收藏',
		artworks: '作品',
		goCollect: '去收藏',
		price: '價格',
		nonickname: '未設置暱稱',
		cjdjs: '出價截止時間',
		cjkssj: '出價開始時間',
		cjjzsj: '出價截止時間',
		gotosee: '快去市場看看吧',
		position: '持有',
		resell: '轉賣',
		buyPrice: '買入價',
		confirmResell: '確認轉賣',
		wrongPrice: '請選擇正確的價格',
		bidPlacedBy: '出價人',
		placeRecord: '出價記錄',
		confirPurchase: '確認購買',
		artistList: '藝術家列表',
		hasOpen: '已開啟',
		noOpen: '未開啟',
		clickOpen: '開啟盲盒',
		confirmOpen: '確認開啟盲盒嗎？ ',
		margin: '保證金',
		wrongPrice: '請輸入正確的價格',
		wrongPerIncrease: '請輸入正確的加價',
		wrongTime: '結束時間應晚於結束時間',
		confirmResell2: '商品一旦轉賣，不可撤回，確定要轉賣該商品嗎？ ',
		reselling: '轉賣中',
		message: '消息通知',
		confirmPay: '確定支付購買該商品嗎？ ',
		zfdjs: '支付倒計時',
		payTime: '支付時間',
		hasRead: '已讀',
		lastPrice: '最終成交價',
		isPay: '已支付',
		isExpired: '已過期',
		rarity: '稀有度'
	},
	tradeQuery: {
		title: "交易查詢",
		tradingPair: "交易對",
		direction: "交易方向",
		betSeconds: "下注秒數",
		betAmount: "下注金額",
		currentStatus: "當前狀態",
		buyUp: "買漲",
		buyDown: "買跌",
		seconds: "秒",
		profit: "盈利",
		loss: "虧損",
		draw: "平局",
		trading: "交易中",
		noData: "暫無數據",
		loading: "加載中...",
		refresh: "刷新"
	},
	contact: {
		contactUs: '在線客服'
	}
}

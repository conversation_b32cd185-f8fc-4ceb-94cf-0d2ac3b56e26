export default {
	nav: ['ホーム','市場','貿易','金融','資産'],
	common: {
		search: "検索",
		confirm: "を選択します。",
		cancel: "キャンセル",
		submit: "送信",
		logout: "終了",
		all: "すべて",
		edit: "編集",
		delete: "削除",
		reset: "リセット",
		filter: "フィルタ",
		login: "ログイン",
		register: "登録",
		email: "メールボックス",
		mobile: "携帯番号",
		account: "アカウント",
		password: "パスワード",
		passwordPlaceholder: "パスワードを入力してください",
		noaccount: "アカウントはありませんか?",
		go: "行く",
		forgetPassword: "パスワードを忘れる",
		confirmPassword: "パスワードの確認",
		invitecode: "招待コード",
		hasaccount: "アカウントはありますか?",
		mobilecodePlaceholder: "携帯電話の認証コードを入力してください",
		emailcodePlaceholder: "メールボックスの認証コードを入力してください",
		send: "認証コードの送信",
		selectArea: "地域の選択",
		audit: "レビュー",
		underAudit: "レビュー中",
		auditFailure: "レビューに失敗しました",
		untie: "縛りを解く",
		buy: "買う",
		sell: "売る",
		plsLogin: "先にログインしてください",
		plsInputUsername: "口座を入力してください",
		plsInputRePassword: "確認パスワードを入力してください",
		plsInputInviteCode: "招待コードを入力してください",
		plsInputCode: "認証コードを入力してください",
		plsInputMobile: "携帯番号を入力してください",
		plsInputEmail: "メールボックスを入力してください",
		login_success: "ログイン成功",
		login_fail: "ログイン失敗",
		reg_success: "登録成功",
		reg_fail: "ログイン失敗",
		pwdInconsistent: "2回のパスワードが一致しません",
		hint: "ヒント",
		loading: "ロード中",
		success: "成功",
		plsInput: "入力してください",
		click: "クリック",
		quotation: "相場",
		edit: "編集",
		second: "秒",
		specialChart: "特殊文字を含めることはできません",
		open: "寄り付き相場",
		close: "終値",
		high: "最高値",
		low: "最低価格",
		customerService: "カスタマーサービスに連絡する",
		rememberPassword: "パスワードを覚える",
		newPassword: "新しいパスワード",
		pwdMoreThen6: "パスワードの長さは6ビットより大きくなければなりません",
		codeLength6: "検証コードの長さは6ビットであるべきである",
		rePwdMoreThen6: "パスワードの長さが6桁以上であることを確認します",
		pls: "どうぞ",
		saveSuccess: "システムアルバムに保存",
		nextStep: "次のステップ",
		log: "きろく",
		paramsWrong: "パラメータエラー",
		functionLoading: "機能開発中",
		DIGICCY: "数値通貨",
		quickly: "すばやく",
		more: "その他",
		hasNoData: "これ以上データはありません",
		audioError: "オーディオファイルエラー",
		toLong: "文字が長すぎる",
		selectLang : "言語の選択",
	},
	home: {
		contactUs: 'お問い合わせ',
		borrowing: '借りる',
		borrowing_entry: 'ローンエントリー',
		repayment_entry: 'ローン返済エントリ',
		title: "Tebbit BTG",
		totalAssetsEquivaleng: "総資産換算",
		recharge: "チャージ",
		withdraw: "現金を引き出す",
		stock: "株式",
		futures: "契約書",
		copytrade: "について行く",
		financial: "金融財テク",
		margin: "スーパーレバー",
		otc: "OTC",
		coupon: "カードセンター",
		bills: "請求書",
		exchange: "為替レート",
		mining: "マイニング",
		game: "ミニゲーム",
		chatroom: "チャットルーム",
		quicklyBuyCoins: "クイックコイン",
		defiMining: "DeFi掘削",
		cloudMining: "うんこうき",
		stock: "株式",
		stock_info: "倉庫を開く必要がなく,直ちに倉庫を持つ",
		lockming: "ロックボーリング",
		transfer: "コイン振り替え",
		ieo: "IEO",
		seconds: "オプション",
		day: "ああ",
		minimum: "起",
		dailyReturnRate: "日収益率",
		more: "その他",
		market: "相場",
		tradingPair: "トランザクションペア",
		lastPrice: "最新価格",
		todayChange: "きょうは下げ幅が上がる",
		transaction: "取引",
		nft: "NFT",
		invest: "投資財テク",
		download: "ダウンロード",
		app_text: "オンライン取引",
	},
	setting: {
		my_qrcode: '私のQRコード',
		mine: 'Mine',
		inviteDesc: "友人を誘って共に利益を返す",
		inviteDesc2: "友人と共に返利を享受する",
		inviteNumber: "招待人数",
		inviteTransNumber: "取引済人数",
		myRebate: "私の返利",
		bill: "請求書",
		wallet: "財布の住所",
		banks: "銀行カードバインド",
		banksInternational: "銀行カードバインド【國際】",
		addBank: "銀行カードの追加",
		securitySettings: "セキュリティ設定",
		coupon: "カードセンター",
		systemNotification: "システム通知",
		defaultFiatCurrency: "デフォルト通貨",
		language: "言語",
		faq: "よくある質問",
		operationalCompliance: "経営コンプライアンス",
		share: "分かち合う",
		contactUs: "お問い合わせ",
		aboutUs: "私たちについて",
		avatar: "アイコン",
		nickname: "ニックネーム",
		signature: "パーソナル署名",
		grxx: "個人情報",
		grxxOptions: {
			nickname: {
				title: "ニックネーム",
				placeholder: "ニックネームを入力してください",
				desc: "2～10文字、中国語、英語、数字をサポート",
			},
			signature: {
				title: "パーソナル署名",
				placeholder: "パーソナル署名を入力してください",
				desc: "2～30文字、中国語、英語、数字をサポート",
			}
		},
		xxtx: "アイコンをアップロード",
		photograph: "写真をとる",
		selectFromAlbum: "アルバムから選択",
		aqsz: "セキュリティ設定",
		editLoginPassword: "ログインパスワードの変更",
		setPayPassword: "支払パスワードの設定",
		editPayPassword: "支払パスワードの変更",
		bindPhone: "携帯を縛る",
		email: "メールボックス",
		stayLogin: "ログインの保持",
		xgdlmm: "ログインパスワードの変更",
		currentPassword: "現在のログインパスワード",
		currentPasswordPlaceholder: "現在のログインパスワードを入力してください",
		newPassword: "新しいログインパスワード",
		newPasswordPlaceholder: "新しいログインパスワードを入力してください",
		confirmNewPassword: "新しいパスワードの確認",
		confirmNewPasswordPlaceholder: "新しいパスワードの確認を入力してください",
		currentPayPassword: "現在の支払いパスワード",
		currentPayPasswordPlaceholder: "現在の支払いパスワードを入力してください",
		newPayPassword: "新しい支払いパスワード",
		newPayPasswordPlaceholder: "新しい支払いパスワードを入力してください",
		confirmNewPayPassword: "新しい支払いパスワードの確認",
		confirmNewPayPasswordPlaceholder: "新しい支払い確認パスワードを入力してください",
		phoneVerificationCode: "携帯認証コード",
		editPhoneVerificationCode: "携帯電話の認証コードを入力してください",
		getVerificationCode: "認証コードの取得",
		lctyq: "財テク体験券",
		expired: "無効",
		expiredTime: "失効日",
		useImmediately: "今すぐ使用",
		addNewAccount: "新しいアカウントを追加",
		totayIncome: "本日入金",
		totalIncome: "累計入金",
		selectLang: "言語を選択してください",
		selectCoinType: "通貨を選択してください",
		authentication: "資格認定",
		authentication1: "基礎認証",
		authentication2: "高度な資格認定",
		authentication_text_1: "個人認証",
		authentication_text_2: "資格認定の完了後に取得できる権限:",
		authentication_text_3: "Lv1.基礎認証",
		authentication_text_4: "認証後、24時間限定200 BTC",
		authentication_text_5: "認証後はフランスドル取引が可能で、単筆限度2000 USDT",
		authentication_text_6: "Lv2.高度な資格認定",
		authentication_text_7: "体現額を増やし、24時間限度額500 BTC",
		authentication_text_8: "フランスドルの取引額を増やし、単一限度額100000 USDT",
		authentication_text_9: "お知らせ:ユーザーの資金の安全を守るために、メールボックスと電話のバインドを完了してください。そうしないと、認証に合格できません。",
		goAudit: "認証を取る",
		noaudit: "資格認定なし",
		auditing: "認証中",
		hasaudit: "認定済み",
		idType: '証明書の種類',
		idCard: '身分証明書',
		passport: 'パスポート',
		driverLicense: '運転免許証',
		name: "名前",
		idcard: "身分証明書番号",
		plsIptName: "名前を入力してください",
		plsIptCorrectIdcard: "正しいID番号を入力してください",
		uploadIdcardFront: "身分証明書のアップロード正面",
		uploadIdcardReverse: "身分証明書の裏面をアップロード",
		emailVerificationCode: "メールボックス認証コード",
		plsiptEmailCode: "メールボックスの認証コードを入力してください",
		addWallet: "ウォレットアドレスの追加",
		selectCurrency: "通貨の選択",
		plsSelectCurrency: "通貨を選択してください",
		plsIptAddress: "財布の住所を入力してください",
		walletAddress: "財布の住所",
		walletQrcode: "財布のQRコード",
		plsIptWalletAddress: "財布の住所を入力してください",
		plsUploadWalletQrcode: "財布のQRコードをアップロードしてください",
		confirmLogout: "ログインを終了しますか?",
		nonickname: "ニックネームを設定してください",
		plsInputMobile: "携帯番号を入力してください",
		alipay_account: "ABA号",
		real_name: "実名",
		bank_account: "銀行カード番号",
		bank_dizhi: "省市を",
		bank_name: "口座開設銀行",
		bank_network: '口座開設サイト',
		swift_code: 'SWIFT',
		phone: '連絡先電話',
		wechat_account: "カード所持者住所",
		wechat_nickname: "SWIFTコード",
		allNeed: "すべて必須",
		phoneNumber: "携帯番号",
		code: "検証コード",
		sendCode: "認証コードの送信",
		plsIptCode: "認証コードを入力してください",
		withdrawPassword: '出金パスワード',
		confirmWithdrawPassword: '出金パスワードの確認',
		plsInputWithdrawPassword: '出金パスワードを入力してください',
		plsInputConfirmWithdrawPassword: '出金パスワードを確認してください',
		withdrawPasswordPlaceholder: '6-16桁の出金パスワードを入力してください',
		confirmWithdrawPasswordPlaceholder: '出金パスワードを再度入力してください',
		updateWithdrawPassword: '出金パスワードの更新',
		oldWithdrawPassword: '古い出金パスワード',
		newWithdrawPassword: '新しい出金パスワード',
		plsInputOldWithdrawPassword: '古い出金パスワードを入力してください',
		plsInputNewWithdrawPassword: '新しい出金パスワードを入力してください',
		withdrawPasswordNotMatch: '出金パスワードが一致しません',
		withdrawPasswordLengthError: '出金パスワードの長さは6-16文字である必要があります',
		withdrawPasswordRequired: '出金パスワードを設定してください',
		withdrawPasswordVerifyFailed: '出金パスワードの確認に失敗しました',
		score: "Score"
	},
	transaction: {
		actualPL: 'Actual PL',
		favorites: "じこせんたく",
		futures: "契約書",
		coins: "貨幣",
		stock: "株式",
		tradingPair: "トランザクションペア",
		lastPrice: "最新価格",
		todayChange: "きょうは下げ幅が上がる",
		searchPlaceholder: "関心のある通貨/株名/コードの検索",
		hotSearch: "人気検索",
		recentView: "最近の表示",
		dayHigh: "今日は高いです。",
		dayLow: "今日は低い",
		optional: "じこせんたく",
		buy: "買い入れる",
		sell: "売りに出す",
		entrustPendingOrder: "委託書留",
		addOptionalSuccess: "自己選択の追加に成功しました",
		delOptionalSuccess: "選択項目の削除に成功しました",
		seconds: "オプション",
		home: "トップページ",
		orderConfirm: "オーダーの確認",
		currentPrice: "現在の価格",
		direction: "方向",
		selectTime: "有効期限の選択",
		number: "数量",
		balance: "口座残高",
		plsIptCrtNumber: "正しい数量を入力してください",
		buyPrice: "購入価格",
		expectedPL: "損益予想",
		continueTrade: "取引を続ける",
		secondsPosition: "オプション倉庫",
		position: "もちこたえる",
		history: "歴史",
		orderTimes: "オーダー時間",
		pl: "損益",
		sellTime: "売り時間",
		buy: "買い入れる",
		sell: "売りに出す",
		price: "価格",
		time: "時間",
		optional: "じこせんたく",
		buy: "買い入れる",
		sell: "売りに出す",
		long: "たくさん作る",
		short: "暇を作る",
		entrustPendingOrder: "委託書留",
		addOptionalSuccess: "自己選択の追加に成功しました",
		delOptionalSuccess: "選択項目の削除に成功しました",
		price: "価格",
		amount: "数量",
		marketPrice: "市価",
		limitPrice: "値段を限る",
		number: "トレーダー数",
		margin: "保証金",
		handlingFee: "手数料",
		balance: "残高",
		multiple: "ツールバーの",
		recharge: "チャージ",
		plsIptCommissionPrice: "委託価格を入力してください",
		tradingPair: "トランザクションペア",
		direction: "方向",
		plsIptCrtPrice: "正しい価格を入力してください",
		position: "倉庫を持つ",
		time: "時間",
		turnover: "取引額",
		operation: "操作",
		type: "を選択します。",
		cover: "平倉",
		confirmCover: "平倉を確認しますか?",
		confirmSelfHold: "本当に自持しますか?",
		selfHolding: "自業自得",
		transSelfHold: "じゆうを回す",
		delegateList: "委任リスト",
		riskRate: "リスク率",
		totalPandL: "持倉総収益",
		oneClickCover: "ワンタッチ平倉",
		open: "倉庫価格",
		targetProfitPrice: "せいじょうか",
		updatePrice: "現在の価格",
		stopLossPrice: "ていしそんか",
		overnightMoney: "隔夜料",
		openTime: "開倉時間",
		lots: "手",
		setProfitLoss: "セットストップロス",
		expectedProfit: "予想収益",
		expectedLoss: "予想損失",
		coverAll: "すべて平倉",
		onlyCoverMany: "平多単のみ",
		onlyCoverEmpty: "平空単のみ",
		numbers: "数量",
		coinBuyPrice: "現在の最適価格で取引する",
		jine: "金額",
		p_jine: "金額を入力してください",
		p_price: "価格を入力してください",
		p_confirm_sj: "市価で確定する",
		p_confirm_xj_1: "現価で確定する",
		p_confirm_ma: "そうですか。",
		status: "≪ステータス｜Status｜emdw≫",
		complete: "完了",
		doing: "進行中",
		currentEntrust: "現在の委任",
		allEntrust: "すべて委任",
		entrust: "依頼",
		entrustTime: "委任時間",
		all: "すべて",
		buyUp: "たくさん買う",
		buyDown: "買いが少ない",
		jye:'取引金額',
	},
	fund: {
		overview: "総覧",
		balance: "残高",
		futures: "契約書",
		deposit: "預金",
		mining: "マイニング",
		stock: "株式",
		margin: "スーパーレバー",
		yesterdayEarnings: "昨日の収益",
		yieldAtMaturily: "満期収益",
		totalkRevenue: "総収益",
		currentProfit: "現在の損益",
		profitRatio: "損益率",
		receive: "チャージ/入金",
		transfer: "現金引き出し/振込",
		financial: "金融財テク",
		valuation: "評価",
		currentEarnings: "現在の収益",
		yesterdayIncome: "昨日の収益",
		crypto: "数値通貨",
		fiat: "フランス通貨",
		hideSmallCurrency: "小口通貨の非表示",
		available: "使用可能",
		freeze: "フリーズ",
		equivalent: "折りたたみ",
		usdtm: "U本位",
		coinm: "貨幣本位",
		exchange: "貨幣",
		leverage: "契約書",
		fiat: "フランス通貨",
		second: "オプション",
		convert: "総資産換算",
		convert2: "資産換算",
		record: "ちょうへんきろく",
		availableQuota: "使用可能額",
		locked: "ロック",
		converted: "折りたたみ",
		account: "アカウント",
		financialRecords: "財務記録",
		number: "数量",
		record2: "きろく",
		time: "時間",
		transfer: "コイン振り替え",
		from: "から",
		to: "対象",
		transferNumber: "回転数",
		youGet: "君は手に入れる",
		exchangeRate: "為替レート",
		handlingFee: "手数料",
		fundTransfer: "資金の振り替え",
		insufficientBalance: "残高不足",
		needHandlingFee: "手数料がかかる",
		plsIptCrtNumber: "正しい数量を入力してください",
		c_transfer: "本当に回転しますか?",
		selectCurrency: "通貨の選択",
		DIGICCY: "数値通貨",
		saveQrcode: "QRコードの保存",
		copyAddress: "リンクアドレスのコピー",
		plsTrans: "この住所に振り替えてください",
		rechargeNumer: "チャージ数量",
		paymentVoucher: "支払証憑",
		plsIptRechargeNumer: "チャージ数量を入力してください",
		plsUploadPaymentVoucher: "支払伝票をアップロードしてください",
		plsIptCrtAmount: "正しい金額を入力してください",
		withdraw: "現金引き出し/振込",
		currencyName: "通貨名",
		transactionInfo: "取引口座",
		withdraw2: "現金を引き出す",
		canUse: "使用可能",
		all: "すべて",
		plsIptWithdrawNumber: "引き出し数量を入力してください",
		withdrawNumber: "現金引き出し数量",
		remark: "コメント",
		handlingFee: "手数料",
		canGetNumber: "入金数量",
		leastNumber: "最低引当数量",
		coinType: "通貨",
		address: "アドレス",
		date: "日付",
		withdrawAddress: "コイン・アドレス",
		plsSelectWithdrawAddress: "コインアドレスを選択してください",
		withdrawToCard: "銀行カードといえば",
		withdrawToCardInternational: 'International',
		withdrawToAddress: "財布の住所に言及する",
		card: "銀行カード",
		addCard: "銀行カードの追加",
		plsAddCard: "銀行カードを追加してください",
		plsInputWithdrawPasswordForWithdraw: '確認のため出金パスワードを入力してください',
	},
	ieo: {
		subscribe: "購入する",
		ing: "進行中",
		done: "終了",
		applySubscription: "受注申請",
		remaining: "残り",
		lockPeriod: "ロックサイクル",
		endTime: "終了時間",
		subscribeCurrency: "購入通貨",
		issuancePrice: "発行価格",
		totalIssuance: "発行合計",
		whitePaper: "白書",
		subscriptionCycle: "購買サイクル",
		warmUp: "よねつ",
		website: "公式サイト",
		finish: "の最後の部分",
		projectDetails: "プロジェクトの詳細",
		subscriptionConfirmation: "購入確認",
		p_applicationsNumber: "受注数量の入力",
		needToPay: "支払いが必要です",
		currentBalance: "現在の残高",
		subscriptionTime: "購買依頼時間",
		currency: "通貨",
		applicationsNumber: "購買依頼数量",
		passesNumber: "通過数",
		timeToMarket: "発売時間",
		day: "ああ",
		total: "合計",
		subscribed: "購入済み",
		shengou: "申し込み",
		insufficientBalance: "残高不足",
		p_number: "数量を入力してください",
		mySubscribe: "私の注文",
	},
	lockming: {
		lockming: "ロックボーリング",
		fundsUnderCustody: "預託中の資金",
		entrustedOrders: "委託オーダー",
		estimatedTodayIncome: "今日予想収益率",
		cumulativeIncome: "累計収益",
		ordersInCustody: "管理中のオーダー",
		lockedPositionsToEarnCoins: "マイニング",
		lockedPositions: "ロックボックス",
		minimumSingleTransaction: "シングルペンが最小",
		dailyYield: "日収益率",
		lockUpPeriod: "ロックサイクル",
		acquisition: "取得",
		recentDays: "近日",
		dividendTime: "休み時間",
		escrowFunds: "管理資金",
		redemptionInAdvance: "繰り上げて買い戻す",
		estimatedIncome: "予想収益",
		availableAssets: "使用可能な資産",
		investmentAmountObtained: "投資金額獲得",
		all: "すべて",
		l_alert_1: "鉱業がひっきりなしに進む",
		l_alert_2: "ロック倉庫掘削はusdtをプラットフォーム超算力鉱機に託管することによってプラットフォーム鉱池で掘削収益を行う。",
		features: "製品のハイライト",
		onDemand: "随存随取",
		dividendPeriod: "配当期間",
		issuedDaily: "毎日",
		currentInterest: "普通利子",
		feature_1: "100%資金安全<br>保障",
		feature_2: "休日収益<br>は簡潔ではない",
		feature_3: "預け入れに成功した後<br>当日から金利を上げる",
		forInstance: "例を挙げる",
		incomeCalculation: "収益計算",
		forInstanceDesc: "会員はプラットフォームで100,000 Uをロックして、周期が5日で、日産はロック金額の0.3%-0.4%の財テク製品を出して、毎日以下のように産出します:<br/>最低:100,000 U*0.3%=30 U<br/>最高:10000 U*0.4%=40 U<br/>つまり5日後に150 U~200 Uの収益を得ることができて、収益は毎日下発して、下発の収益はいつでもアクセスすることができて、ロック元金が期限切れになった後、自動的にあなたの資金口座に振り込みます。",
		aboutLiquidatedDamages: "違約金について",
		aboutLiquidatedDamagesContent: "未満期の元金を転出したい場合は、違約金、違約金=違約決済比率*残存日数*ロック数量が発生します。<br/>例:このロック掘削の違約決算割合は0.4%で、残りの3日間は期限切れで、ロック数量は1000で、違約金=0.4%*3*1000=12 Uで、実際に元金を返却するのは1000 U-12 U=988 Uである。",
		joinNow: "私は参加します。",
		day: "ああ",
		daily: "毎日",
		returnOnExpiration: "期限切れリターン",
		get: "取得",
		numberOfCoinsDeposited: "コイン保管数量",
		subscribe: "購入する",
		insufficientBalance: "残高不足",
		p_number: "数量を入力してください",
		leastSingle: "シングルペンが最小",
		lockedPositionList: "ロックリスト",
		inProgress: "進行中",
		redeemed: "償還済み",
		expiryTime: "有効期限",
		lockUpTime: "ロックタイム",
		earlyRedemptionPenalty: "違約金を繰り上げる",
		redemption: "身代金を払う",
		c_redemption: "本当に事前に身代金を払うのですか?",
	},
	invest: {
		invest: "財テク",
		section: "エリア",
		perPrice: "単品価格",
		apr: "年化収益率",
		linkedReferencePrice: "フック基準価格",
		holdingDays: "倉庫保有期間",
		maturityDate: "期日",
		remainingShares: "残りのシェア",
		progress: "現在の進行状況",
		action: "操作",
		purchase: "購入",
		myPosition: "私の財テク",
		buyShares: "購入シェア",
		estimatedIncome: "予想収益",
		day: "ああ",
		share: "分",
		fail: "失敗",
		purchased: "購入済み",
		settlementing: "決算中",
		success: "完了",
		all: "すべて",
		name: "名前",
		remain: "残り",
		instruction: "製品の説明",
		help: "ヘルプ",
		shuoming_1: '1.この商品は、非本人保証のウェルスマネジメント商品です。市場の変動により元本が失われる可能性がありますので、ご注意ください。',
		shuoming_2: '2.支払いの回収と決済のルール：',
		shuoming_3: `決済価格<ペッグ価格の場合、BTCは満了後に決済されます;決済金額=投資金額*（1+日*年間収益率/365）。`,
		shuoming_4: `決済価格が固定価格以上の場合、USDTは満了後に決済されます。決済金額=投資額*固定価格*（1+日*年間収益率/365）。`,
		shuoming_5: '3.実際の年間収益率は、市場によってリアルタイムで変化します。実際の購入取引を参照してください。',
		shuoming_6: '4.投資額は市場に合わせてリアルタイムで計算されますので、実際の購入取引をご参照ください。',
		shuoming_7: '5.購入後、マイコインホールディングページで確認できます。有効期限が切れると、スポットアカウントに自動的に支払いが行われます。',
		error_1: "購入数量を入力してください",
		error_2: "購入数量は整数",
		confirmPurchase: "この財テク製品を購入しますか?",
		purchaseSuccess: "購入成功！",
	},
	follow: {
		follow: "について行く",
		traderList: "トレーダーリスト",
		totalProfit: "総損益",
		correctRate: "そうごうせいど",
		positionValuation: "在庫評価",
		estimatedProfitToday: "今日の予想収益",
		rateOfReturn: "収益率",
		becomeATrader: "トレーダーになる",
		traderList: "トレーダーリスト",
		onlyShowsThePosition: "位置のみ表示",
		theDataIsUpdatedEveryHour: "データは1時間ごとに更新されます",
		following: "フォロー中",
		follow: "について行く",
		confirmFollow: "本当にフォローしますか?",
		confirmCancel: "追従をキャンセルしますか?",
		followTrader: "トレーダーに従う",
		editCopy: "追従の編集",
		beforeCopy: "フォローする前によく読んでください",
		copyAgreement: "プロトコルに従う",
		copyType: "フォローモード",
		copyType1: "固定倍数追従",
		copyType2: "固定手数追従",
		copyAlert1: "トレーダーがいくら注文しても、選択した固定倍数でフォローします。",
		copyAlert2: "(比例手数>1で整数でない場合、四捨五入は2桁の小数値を保持する)",
		copyMultiple: "追従倍数",
		cancelCopy: "フォローを解除",
		currentFollowerNumber: "現在のフォロー数",
		totalProfitAndLoss: "総損益",
		totalReturn: "総収益率",
		accuracy: "せいど",
		profitableOrders: "収益受注",
		successfulShortTrades: "空の取引に成功した",
		successfulLongTrades: "マルチトランザクションに成功",
		yesterdaysTradingStatus: "昨日の取引状況",
		currentPositions: "現在の倉庫",
		totalOrders: "総注文",
		remain: "残り",
		distanceDue: "距離の有効期限",
		days: "ああ",
		deadline: "期間",
		subscription: "申し込み",
		followUsers: "ユーザーに従う",
		followAmount: "追従金額",
		followEarnings: "収益に従う",
		cancelFollow: "フォローを解除",
		currentCopy: "現在の請求書",
		historicalCopy: "履歴取引",
		myTrader: "私のトレーダー",
		tradingPair: "トランザクションペア",
		open: "倉庫価格",
		close: "平倉価",
		earnings: "収益",
		lots: "手の数",
		time: "取引時間",
		buyIn: "買い入れる",
		buyOut: "売りに出す",
		direction: "方向",
	},
	financial: {
		product: "製品",
		position: "所有する",
		financial: "金融",
		lockming: "ロックボーリング",
		ieo: "IEO",
		invest: "投資財テク",
		section: "エリア",
		apr: "年化収益率",
		linkedReferencePrice: "フック基準価格",
		holdingDays: "倉庫保有期間",
		day: "ああ",
		lockPeriod: "ロックサイクル",
		ing: "進行中",
		done: "終了",
		applySubscription: "受注申請",
		remaining: "残り",
		total: "合計",
		subscribed: "購入済み",
		mining: "マイニング",
		minimum: "起",
		dailyReturnRate: "日収益率",
	},
	nft: {
		nft: "NFT",
		artwork: "芸術品",
		artist: "芸術家",
		artworkList: "芸術品リスト",
		artistList: "アーティストリスト",
		comprehensiveSorting: "総合ソート",
		newest: "最新の",
		hotest: "いちばん暑い",
		mostWorks: "作品がいちばん多い",
		mostPopular: "いちばん人気がある",
		record: "購入履歴",
		recommendArtist: "おすすめアーティスト",
		buy: "購入",
		artistHomepage: "アーティストのホームページ",
		works: "作品",
		saled: "販売済み",
		fans: "ファン人",
		allWorks: "全作品",
		notSell: "未販売",
		myCollect: "私のコレクション",
		collect: "コレクション",
		own: "所有する",
		placeABid: "値段をつける",
		purchase: "購入",
		open: "バックオープン",
		image: "画像",
		gif: "動図",
		audio: "オーディオ",
		video: "ビデオ",
		creator: "作成者",
		currentBid: "最新価格",
		price: "価格",
		d: "ああ",
		h: "時",
		m: "分",
		s: "秒",
		saled: "終了",
		place_alert: "品物が販売時間になっていないか、終了しています。",
		createdBy: "作成者",
		place_alert2: "あなたの価格は高くなければなりません。",
		note: "重要",
		CURRENT_BID: "最新価格",
		place_alert3: "オークションに参加するには、お支払いが必要です",
		place_alert4: "の保証金は、競売終了時に保証金が口座に返却されます。",
		place_alert6: '入札締切後、落札された場合は1日以内に最終代金をお支払いいただきます。 支払いが遅れた場合、保証金が差し引かれます。',
		confirmBid: "価格の確認",
		DESCRIPTION: "説明",
		ADDRESS: "アドレス",
		copy: "コピー",
		per_increase: "1回あたりの値上げ",
		currency_type: "通貨タイプ",
		pay_type: "購入方法",
		pay_type_2: "販売方法",
		normal: "通常",
		auction: "競売にかける",
		confirmAuction: "この商品の競売に参加しますか?",
		bindBox: "ブラインドボックス",
		sell_status: "販売ステータス",
		hasStart: "開始済み",
		willStart: "開始していません",
		artwork_type: "アイテムの種類",
		reset: "リセット",
		confirm: "を選択します。",
		place_alert5: "キーワードを入力してください",
		collection: "コレクション済み",
		byCollection: "コレクションされる",
		artworks: "作品",
		goCollect: "コレクションに行く",
		price: "価格",
		nonickname: "ニックネームが設定されていません",
		cjdjs: "オファー締切時間",
		cjkssj: "価格設定開始時間",
		cjjzsj: "オファー締切時間",
		gotosee: "早く市場に行ってみよう",
		position: "所有する",
		resell: "転売する",
		buyPrice: "買い入れ価格",
		confirmResell: "転売確認",
		wrongPrice: "正しい価格をお選びください",
		bidPlacedBy: "値切る人",
		placeRecord: "価格記録",
		confirPurchase: "購入の確認",
		artistList: "アーティストリスト",
		hasOpen: "オン",
		noOpen: "オープンしていません",
		clickOpen: "ブラインドボックスを開く",
		confirmOpen: "ブラインドボックスが開いていることを確認しますか?",
		margin: "保証金",
		wrongPrice: "正しい価格を入力してください",
		wrongPerIncrease: "正しい値上げを入力してください",
		wrongTime: "終了時間は終了時間より遅くなければなりません",
		confirmResell2: "商品が転売されると取り下げることはできませんが、本当に転売しますか?",
		reselling: "転売中",
		message: "メッセージ通知",
		confirmPay: "この商品の購入は確実に支払いますか?",
		zfdjs: "支払いカウントダウン",
		payTime: "支払い時間",
		hasRead: "既読",
		lastPrice: "最終成約価格",
		isPay: "支払済",
		isExpired: "期限切れ",
		rarity: "希少度",
	}
}

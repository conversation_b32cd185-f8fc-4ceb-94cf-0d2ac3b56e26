export default {
	nav: ['첫 페이지', '시세', '트레이드', '금융', '자산'],
	common: {
		search: "수색하다",
		confirm: "확정하다",
		cancel: "취소",
		submit: "커밋",
		logout: "퇴장하다",
		all: "모조리",
		edit: "편집자",
		delete: "삭제",
		reset: "재설정",
		filter: "선별하다",
		login: "로그인",
		register: "등록!",
		email: "우편함",
		mobile: "휴대폰 번호",
		account: "계정",
		password: "비밀 번호",
		passwordPlaceholder: "암호를 입력하십시오.",
		noaccount: "계좌번호 없어요?",
		go: "가다",
		forgetPassword: "암호 잊음",
		confirmPassword: "암호 확인",
		invitecode: "초대 코드",
		hasaccount: "계정이 있습니까?",
		mobilecodePlaceholder: "휴대폰 인증번호를 입력하십시오.",
		emailcodePlaceholder: "메일박스 인증번호를 입력하십시오.",
		send: "인증 코드 보내기",
		selectArea: "지역 선택",
		audit: "심사",
		underAudit: "심사 중",
		auditFailure: "감사 실패",
		untie: "묶음을 풀다",
		buy: "사다",
		sell: "팔다",
		plsLogin: "먼저 로그인하십시오.",
		plsInputUsername: "계정 입력",
		plsInputRePassword: "확인 암호를 입력하십시오.",
		plsInputInviteCode: "초대 번호를 입력하십시오.",
		plsInputCode: "인증번호를 입력하십시오.",
		plsInputMobile: "핸드폰 번호를 입력하세요.",
		plsInputEmail: "메일박스 입력",
		login_success: "로그인 성공",
		login_fail: "로그인 실패",
		reg_success: "등록 성공",
		reg_fail: "로그인 실패",
		pwdInconsistent: "비밀번호 불일치",
		hint: "프롬프트",
		loading: "로드 중",
		success: "성취",
		plsInput: "입력하십시오.",
		click: "클릭",
		quotation: "시세",
		edit: "편집자",
		second: "초 단위",
		specialChart: "특수 문자는 허용되지 않습니다.",
		open: "개장 가격",
		close: "마감가",
		high: "최고가",
		low: "최저가",
		customerService: "고객센터에 연락하다",
		rememberPassword: "암호 기억하기",
		newPassword: "새 암호",
		pwdMoreThen6: "암호의 길이는 6자리 이상이어야 한다",
		codeLength6: "인증 코드 길이는 6비트여야 합니다.",
		rePwdMoreThen6: "비밀번호 확인 길이는 6자리 이상이어야 합니다",
		pls: "초대하다",
		saveSuccess: "시스템 포토 앨범에 저장됨",
		nextStep: "다음 단계",
		log: "레코드",
		paramsWrong: "매개변수 오류",
		functionLoading: "기능 개발 중",
		DIGICCY: "디지털 화폐",
		quickly: "쾌속",
		more: "더.",
		hasNoData: "더 이상 데이터가 없어요.",
		audioError: "오디오 파일 오류",
		toLong: "문자가 너무 깁니다.",
		selectLang : "언어 선택",
	},
	home: {
		contactUs: '연락처',
		borrowing: '차용',
		borrowing_entry: '대출 항목',
		repayment_entry: '대출 상환 입력',
		title: "Tebbit BTG",
		totalAssetsEquivaleng: "총자산 환산",
		recharge: "충전하다",
		withdraw: "현금을 인출하다",
		stock: "주식",
		futures: "계약",
		copytrade: "활용단어참조",
		financial: "금융 재태크",
		margin: "슈퍼 레버",
		otc: "OTC",
		coupon: "카드 센터",
		bills: "계산서",
		exchange: "환어음",
		mining: "채광",
		game: "작은 놀이",
		chatroom: "채팅방",
		quicklyBuyCoins: "단축 구매 화폐",
		defiMining: "DeFi 채굴",
		cloudMining: "운광기",
		stock: "주식",
		stock_info: "창고를 개설할 필요 없이 즉시 창고를 관리하다.",
		lockming: "채광",
		transfer: "화폐",
		ieo: "IEO",
		seconds: "옵션",
		day: "일",
		minimum: "시작하다",
		dailyReturnRate: "일수익률",
		more: "더.",
		market: "시세",
		tradingPair: "교역",
		lastPrice: "최신 가격",
		todayChange: "오늘 상승폭락",
		transaction: "트레이드",
		nft: "NFT",
		invest: "투자 재테크",
		download: "다운로드",
		app_text: "언제 어디서나 온라인 거래",
	},
	setting: {
		my_qrcode: '내 QR 코드',
		mine: 'Mine',
		inviteDesc: "친구 초대, 공동 이윤 반환",
		inviteDesc2: "친구와 함께 이윤 반환을 누리다",
		inviteNumber: "초청 인원수",
		inviteTransNumber: "거래 인원수",
		myRebate: "나의 이윤 반환",
		bill: "계산서",
		wallet: "지갑 주소",
		banks: "은행 카드 바인딩",
		banksInternational: "은행 카드 바인딩 (인터내셔널)",
		addBank: "카드 추가",
		securitySettings: "보안 설정",
		coupon: "카드 센터",
		systemNotification: "시스템 알림",
		defaultFiatCurrency: "기본 법정 화폐",
		language: "언어",
		faq: "FAQ",
		operationalCompliance: "경영 규범",
		share: "나누다",
		contactUs: "연락처",
		aboutUs: "우리",
		avatar: "두상",
		nickname: "애칭",
		signature: "개인 서명",
		grxx: "개인 정보",
		grxxOptions: {
			nickname: {
				title: "애칭",
				placeholder: "닉네임을 입력하세요.",
				desc: "2-10자, 중국어, 숫자 지원",
			},
			signature: {
				title: "애칭",
				placeholder: "닉네임을 입력하세요.",
				desc: "2-10자, 중국어, 숫자 지원",
			}
		},
		xxtx: "이미지 업로드",
		photograph: "사진을 찍다",
		selectFromAlbum: "앨범 선택",
		aqsz: "보안 설정",
		editLoginPassword: "로그인 암호 수정",
		setPayPassword: "결제 암호 설정",
		editPayPassword: "결제 암호 수정",
		bindPhone: "휴대폰 바인딩",
		email: "우편함",
		stayLogin: "로그인 유지",
		xgdlmm: "로그인 암호 수정",
		currentPassword: "현재 로그인 암호",
		currentPasswordPlaceholder: "현재 로그인 암호를 입력하십시오.",
		newPassword: "새 로그인 암호",
		newPasswordPlaceholder: "새 로그인 암호를 입력하십시오.",
		confirmNewPassword: "새 암호 확인",
		confirmNewPasswordPlaceholder: "새 암호 확인 을 입력하십시오.",
		currentPayPassword: "현재 지불 암호",
		currentPayPasswordPlaceholder: "현재 결제 암호를 입력하십시오.",
		newPayPassword: "새 결제 암호",
		newPayPasswordPlaceholder: "새 결제 암호를 입력하십시오.",
		confirmNewPayPassword: "새 결제 암호 확인",
		confirmNewPayPasswordPlaceholder: "새 결제 암호 확인 을 입력하십시오.",
		phoneVerificationCode: "핸드폰 인증번호",
		editPhoneVerificationCode: "휴대폰 인증번호를 입력하십시오.",
		getVerificationCode: "인증 코드 가져오기",
		lctyq: "재테크 체험권",
		expired: "무효",
		expiredTime: "실효 일자",
		useImmediately: "즉시 사용",
		addNewAccount: "새 계정 추가",
		totayIncome: "금일 수금",
		totalIncome: "누적 수금",
		selectLang: "언어 선택",
		selectCoinType: "화폐종류를 선택하세요",
		authentication: "인증",
		authentication1: "기본 인증",
		authentication2: "고급 인증",
		authentication_text_1: "개인 인증",
		authentication_text_2: "인증 완료 후 획득할 수 있는 권한:",
		authentication_text_3: "Lv1.기본 인증",
		authentication_text_4: "24시간 한도 200BTC 인증 후 제공",
		authentication_text_5: "인증 후 법정 통화 거래 가능, 1회 2000USDT",
		authentication_text_6: "Lv2.고급 인증",
		authentication_text_7: "구현 한도 증가, 24시간 한도 500BTC",
		authentication_text_8: "법정 통화 거래 금액 증가, 1회 금액 1000000USDT",
		authentication_text_9: "알림: 사용자의 자금 안전을 보호하기 위해 먼저 메일박스와 전화의 연결을 완료하십시오. 그렇지 않으면 인증을 통과할 수 없습니다.",
		goAudit: "인증",
		noaudit: "인증되지 않음",
		auditing: "인증 중",
		hasaudit: "인증됨",
		idType: '증명서 유형',
		idCard: '신분증',
		passport: '여권.',
		driverLicense: '운전면허증',
		name: "성함/성명",
		idcard: "주민등록번호",
		plsIptName: "이름을 입력하십시오.",
		plsIptCorrectIdcard: "정확한 주민등록번호를 입력하세요.",
		uploadIdcardFront: "신분증 정면 업로드",
		uploadIdcardReverse: "신분증 뒷면을 올리다",
		emailVerificationCode: "메일박스 인증번호",
		plsiptEmailCode: "메일박스 인증번호를 입력하십시오.",
		addWallet: "지갑 주소 추가",
		selectCurrency: "화폐의 종류를 선택하다",
		plsSelectCurrency: "화폐종류를 선택하세요",
		plsIptAddress: "지갑 주소를 입력하십시오.",
		walletAddress: "지갑 주소",
		walletQrcode: "지갑 QR코드",
		plsIptWalletAddress: "지갑 주소를 입력하십시오.",
		plsUploadWalletQrcode: "지갑 QR코드 올려주세요.",
		confirmLogout: "로그인을 종료하시겠습니까?",
		nonickname: "닉네임을 설정하세요.",
		plsInputMobile: "핸드폰 번호를 입력하세요.",
		alipay_account: "ABA호",
		real_name: "실명",
		bank_account: "은행 카드 번호",
		bank_dizhi: "구좌를 개설하다.",
		bank_name: "계좌 개설 은행",
		bank_network: '계좌를 개설하다.',
		swift_code: 'SWIFT',
		phone: '연락처',
		wechat_account: "카드 소유자 주소",
		wechat_nickname: "SWIFT 코드",
		allNeed: "모두 필수",
		phoneNumber: "휴대폰 번호",
		code: "인증 코드",
		sendCode: "인증 코드 보내기",
		plsIptCode: "인증번호를 입력하십시오.",
		withdrawPassword: '출금 비밀번호',
		confirmWithdrawPassword: '출금 비밀번호 확인',
		plsInputWithdrawPassword: '출금 비밀번호를 입력하세요',
		plsInputConfirmWithdrawPassword: '출금 비밀번호를 확인하세요',
		withdrawPasswordPlaceholder: '6-16자리 출금 비밀번호를 입력하세요',
		confirmWithdrawPasswordPlaceholder: '출금 비밀번호를 다시 입력하세요',
		updateWithdrawPassword: '출금 비밀번호 업데이트',
		oldWithdrawPassword: '기존 출금 비밀번호',
		newWithdrawPassword: '새 출금 비밀번호',
		plsInputOldWithdrawPassword: '기존 출금 비밀번호를 입력하세요',
		plsInputNewWithdrawPassword: '새 출금 비밀번호를 입력하세요',
		withdrawPasswordNotMatch: '출금 비밀번호가 일치하지 않습니다',
		withdrawPasswordLengthError: '출금 비밀번호 길이는 6-16자여야 합니다',
		withdrawPasswordRequired: '출금 비밀번호를 설정하세요',
		withdrawPasswordVerifyFailed: '출금 비밀번호 확인 실패',
		score: 'Score'
	},
	transaction: {
		actualPL: 'Actual PL',
		favorites: "스스로 선택하다",
		futures: "계약",
		coins: "화폐",
		stock: "주식",
		tradingPair: "교역",
		lastPrice: "최신 가격",
		todayChange: "오늘 상승폭락",
		searchPlaceholder: "관심 통화/주식 이름/코드 검색",
		hotSearch: "인기 검색",
		recentView: "최근 보기",
		dayHigh: "오늘",
		dayLow: "오늘 낮다",
		optional: "스스로 선택하다",
		buy: "매입",
		sell: "팔다",
		entrustPendingOrder: "위탁 등기",
		addOptionalSuccess: "사용자 선택 추가 성공",
		delOptionalSuccess: "선택한 항목 삭제 성공",
		seconds: "옵션",
		home: "첫 페이지",
		orderConfirm: "주문 확인",
		currentPrice: "현재 가격",
		direction: "방향",
		selectTime: "만료 날짜 선택",
		number: "수량",
		balance: "계좌 잔액",
		plsIptCrtNumber: "정확한 수량을 입력하세요",
		buyPrice: "구매 가격",
		expectedPL: "손익을 예상하다",
		continueTrade: "거래를 계속하다",
		secondsPosition: "기간권을 가지고 창고를 관리하다.",
		position: "주재하다",
		history: "역사.",
		orderTimes: "주문 시간",
		pl: "손익",
		sellTime: "판매 시간",
		buy: "매입",
		sell: "팔다",
		price: "가격.",
		time: "타임",
		optional: "스스로 선택하다",
		buy: "매입",
		sell: "팔다",
		long: "많이 하다",
		short: "헛수고하다",
		entrustPendingOrder: "위탁 등기",
		addOptionalSuccess: "사용자 선택 추가 성공",
		delOptionalSuccess: "선택한 항목 삭제 성공",
		price: "가격.",
		amount: "수량",
		marketPrice: "시가",
		limitPrice: "가격을 제한하다",
		number: "거래수",
		margin: "보증금",
		handlingFee: "수수료",
		balance: "잔액",
		multiple: "배수",
		recharge: "충전하다",
		plsIptCommissionPrice: "위탁 가격을 입력하세요",
		tradingPair: "교역",
		direction: "방향",
		plsIptCrtPrice: "정확한 가격을 입력하십시오.",
		position: "창고를 관리하다",
		time: "타임",
		turnover: "거래액",
		operation: "조작하다",
		type: "타입",
		cover: "창고를 정리하다",
		confirmCover: "창고를 정리하시겠습니까?",
		confirmSelfHold: "자제 전환이 확실합니까?",
		selfHolding: "자제하다",
		transSelfHold: "자제하다",
		delegateList: "위임 목록",
		riskRate: "위험률",
		totalPandL: "창고 보유 총수익",
		oneClickCover: "원클릭 창고 정리",
		open: "창고 개설 가격",
		targetProfitPrice: "이윤 정지 가격",
		updatePrice: "현재 가격",
		stopLossPrice: "파손 정지 가격",
		overnightMoney: "하룻밤 묵는 비용",
		openTime: "창고 개설 시간",
		lots: "손.",
		setProfitLoss: "손실 방지를 설정하다.",
		expectedProfit: "예상 이익",
		expectedLoss: "예상 손실",
		coverAll: "전부 창고를 정리하다.",
		onlyCoverMany: "단지",
		onlyCoverEmpty: "비다",
		numbers: "수량",
		coinBuyPrice: "현재 최상의 가격으로 거래하다",
		jine: "금액",
		p_jine: "금액을 입력하세요",
		p_price: "가격을 입력하세요.",
		p_confirm_sj: "시가로 확정하다",
		p_confirm_xj_1: "확정",
		p_confirm_ma: "이것 괜찮아요?",
		status: "컨디션",
		complete: "완료됨",
		doing: "진행 중",
		currentEntrust: "당면 의뢰",
		allEntrust: "전탁",
		entrust: "위임",
		entrustTime: "위탁 시간",
		all: "모조리",
		buyUp: "많이 사다",
		buyDown: "적게 사다",
		jye:'거래 금액',
	},
	fund: {
		overview: "총람하다",
		balance: "잔액",
		futures: "계약",
		deposit: "예금하다",
		mining: "채광",
		stock: "주식",
		margin: "슈퍼 레버",
		yesterdayEarnings: "어제 수익",
		yieldAtMaturily: "만기 수익",
		totalkRevenue: "총수익",
		currentProfit: "당면한 손익",
		profitRatio: "손익률",
		receive: "충전/수금",
		transfer: "현금 인출 / 이체",
		financial: "금융 재태크",
		valuation: "평가",
		currentEarnings: "현재 이익",
		yesterdayIncome: "어제 수익",
		crypto: "디지털 화폐",
		fiat: "법정 화폐",
		hideSmallCurrency: "소액 화폐의 종류를 숨기다",
		available: "사용 가능",
		freeze: "얼어붙다",
		equivalent: "환산하다",
		usdtm: "U 본위",
		coinm: "화폐본위",
		exchange: "화폐",
		leverage: "계약",
		fiat: "법정 화폐",
		second: "옵션",
		convert: "총자산 환산",
		convert2: "자산 환산",
		record: "장부 변경 기록",
		availableQuota: "가용 한도",
		locked: "잠기다",
		converted: "환산하다",
		account: "계정",
		financialRecords: "재무 기록",
		number: "수량",
		record2: "레코드",
		time: "타임",
		transfer: "화폐",
		from: "에서",
		to: "대상",
		transferNumber: "회전 수량",
		youGet: "너는 곧 얻게 될 것이다",
		exchangeRate: "환율",
		handlingFee: "수수료",
		fundTransfer: "자금 이체",
		insufficientBalance: "잔액이 부족하다",
		needHandlingFee: "별도의 수수료가 필요하다",
		plsIptCrtNumber: "정확한 수량을 입력하세요",
		c_transfer: "돌리시겠습니까?",
		selectCurrency: "화폐의 종류를 선택하다",
		DIGICCY: "디지털 화폐",
		saveQrcode: "QR코드 저장",
		copyAddress: "링크 주소 복사",
		plsTrans: "이 주소로 그어 주십시오",
		rechargeNumer: "충전 수량",
		paymentVoucher: "지불 증명서",
		plsIptRechargeNumer: "충전 수량을 입력하세요",
		plsUploadPaymentVoucher: "지불 증명서를 올려주세요.",
		plsIptCrtAmount: "정확한 금액을 입력하세요",
		withdraw: "현금 인출 / 이체",
		currencyName: "화폐명칭",
		transactionInfo: "거래 계좌",
		withdraw2: "현금을 인출하다",
		canUse: "사용 가능",
		all: "모조리",
		plsIptWithdrawNumber: "현금인출 수량을 입력하세요",
		withdrawNumber: "현금 인출 수량",
		remark: "비고",
		handlingFee: "수수료",
		canGetNumber: "입금 수량",
		leastNumber: "최저 현금 인출 수량",
		coinType: "화폐의 종류",
		address: "소재지",
		date: "일자",
		withdrawAddress: "인출 주소",
		plsSelectWithdrawAddress: "인출 주소를 선택하세요.",
		withdrawToCard: "은행 카드 언급",
		withdrawToCardInternation: "인터내셔널",
		withdrawToAddress: "지갑 주소 언급",
		card: "은행 카드",
		addCard: "카드 추가",
		plsAddCard: "카드 추가",
		plsInputWithdrawPasswordForWithdraw: '확인을 위해 출금 비밀번호를 입력하세요',
	},
	ieo: {
		subscribe: "청약하다",
		ing: "진행 중",
		done: "종료됨",
		applySubscription: "청약을 신청하다",
		remaining: "잉여",
		lockPeriod: "창고 잠금 주기",
		endTime: "종료 시간",
		subscribeCurrency: "구매를 승인한 화폐의 종류.",
		issuancePrice: "발행 가격",
		totalIssuance: "발행 총량",
		whitePaper: "백서",
		subscriptionCycle: "요청 주기",
		warmUp: "예열",
		website: "공식 홈페이지",
		finish: "끝맺다",
		projectDetails: "프로젝트 상세 정보",
		subscriptionConfirmation: "청약 확인",
		p_applicationsNumber: "청약 수량을 입력하다",
		needToPay: "지불이 필요하다",
		currentBalance: "현재 잔액",
		subscriptionTime: "요청 시간",
		currency: "화폐의 종류",
		applicationsNumber: "신청 수량",
		passesNumber: "통과 수량",
		timeToMarket: "출시 일자",
		day: "일",
		total: "총량",
		subscribed: "이미 청약했다",
		shengou: "구입 신청을 하다",
		insufficientBalance: "잔액이 부족하다",
		p_number: "수량을 입력하세요",
		mySubscribe: "나의 청약",
	},
	lockming: {
		lockming: "채광",
		fundsUnderCustody: "위탁 관리 중인 자금",
		entrustedOrders: "위탁 주문",
		estimatedTodayIncome: "예상 오늘 수익률",
		cumulativeIncome: "누적 이익",
		ordersInCustody: "주문 관리",
		lockedPositionsToEarnCoins: "채광",
		lockedPositions: "창고를 잠그다",
		minimumSingleTransaction: "한 획이 가장 적다",
		dailyYield: "일수익률",
		lockUpPeriod: "창고 잠금 주기",
		acquisition: "얻다",
		recentDays: "근일",
		dividendTime: "이자 할당 시간",
		escrowFunds: "자금을 위탁 관리하다.",
		redemptionInAdvance: "앞당겨 되찾다",
		estimatedIncome: "예상 수익",
		availableAssets: "가용 자산",
		investmentAmountObtained: "투자 금액 획득",
		all: "모조리",
		l_alert_1: "광산을 파는데 쉴 새 없이 굴러간다.",
		l_alert_2: "창고를 잠그고 채굴하는 것은 usdt를 플랫폼에 위탁하여 초산력 광기를 플랫폼 광지에서 채굴 수익을 얻는 것이다",
		features: "제품 특징",
		onDemand: "저장 및 인출",
		dividendPeriod: "이자 할당 주기",
		issuedDaily: "매일 발송하다",
		currentInterest: "당좌 이자",
		feature_1: "100% 자금 안전<br>보장",
		feature_2: "공휴일 수익<br>간단명료하지 않음",
		feature_3: "성공 예금 후 <br>당일부터 이자를 지불합니다",
		forInstance: "예를 들다",
		incomeCalculation: "수익 계산",
		forInstanceDesc: "회원은 플랫폼에서 10000U를 잠그고 주기가 5일이고 일산은 잠금 금액의 0.3%-0.4%의 재태크 상품을 매일 생산한다. <br/> 최저: 10000U*0.3%=30U<br/> 최고: 10000U*0.4%=40U<br/> 즉 5일 후에 150U~200U의 수익을 얻을 수 있고 수익은 매일 하향 발송되며 다음 수익은 수시로 입출할 수 있다. 잠금 원금이 만기가 되면 자동으로 당신의 자금 계좌로 이체한다.",
		aboutLiquidatedDamages: "위약금",
		aboutLiquidatedDamagesContent: "만약에 귀하가 만기가 되지 않은 원금을 이체하기를 원하신다면 위약금, 위약금=위약결제비율*남은 일수*재고수량이 발생합니다.<Br/> 예: 이 잠금창고에서 광물을 캐는 위약 결산 비율은 0.4%이고 남은 3일이 만기가 되면 잠금창고 수량이 1000이면 위약금=0.4%*3*1000=12U, 실제 원금 반환은 1000U-12U=988U",
		joinNow: "나는 참가할 것이다",
		day: "일",
		daily: "날마다",
		returnOnExpiration: "만료 시 반환",
		get: "얻다",
		numberOfCoinsDeposited: "예금 수량",
		subscribe: "청약하다",
		insufficientBalance: "잔액이 부족하다",
		p_number: "수량을 입력하세요",
		leastSingle: "한 획이 가장 적다",
		lockedPositionList: "재고 정리 목록",
		inProgress: "진행 중",
		redeemed: "이미 되찾았다",
		expiryTime: "만료 시간",
		lockUpTime: "재고 정리 시간",
		earlyRedemptionPenalty: "위약금을 앞당겨 상환하다.",
		redemption: "되찾다",
		c_redemption: "앞당겨 상환하시겠습니까?",
	},
	invest: {
		invest: "재정관리",
		section: "특별 구역",
		perPrice: "1인분 가격",
		apr: "연간수익률",
		linkedReferencePrice: "연계 참조 가격",
		holdingDays: "창고 보유 기한",
		maturityDate: "만료 날짜",
		remainingShares: "잉여 몫",
		progress: "현재 진행 상태",
		action: "조작하다",
		purchase: "구입",
		myPosition: "나의 재테크",
		buyShares: "매입 몫",
		estimatedIncome: "예상 수익",
		day: "일",
		share: "몫",
		fail: "패하다",
		purchased: "매입",
		settlementing: "결산 중",
		success: "완료됨",
		all: "모조리",
		name: "이름:",
		remain: "잉여",
		instruction: "제품 설명",
		help: "돕다.",
		shuoming_1: "1. 본 제품은 비보본 재태크 상품으로 시장의 변동이 원금 손실을 초래할 수 있으므로 신중하게 투자하십시오.",
		shuoming_2: "2. 환불 결제 규칙:",
		shuoming_3: "정산가<연계가격>이면 만기가 되면 BTC를 정산합니다.결제수량=투자금액*(1+일수*연간수익률/365).",
		shuoming_4: "결제가 ≥ 연계가격이면 만기가 되면 USDT를 결제한다.결제수량=투자금액*연계가격*(1+일수*연간수익률/365).",
		shuoming_5: "3. 실제 연간 수익률은 시장의 실시간 변동에 따라 실제 매입 거래를 기준으로 한다.",
		shuoming_6: "4. 투자 금액은 시장에 따라 실시간으로 환산할 수 있으니 실제 매입 거래를 기준으로 하십시오.",
		shuoming_7: "5. 제품을 구매한 후 나의 지폐 페이지에서 확인할 수 있으며, 만기가 끝난 후에 환금은 자동으로 현물계좌로 발송됩니다.",
		error_1: "구매 수량 입력",
		error_2: "구매 수량은 정수여야 한다",
		confirmPurchase: "이 재테크 상품을 구매하시겠습니까?",
		purchaseSuccess: "구매 성공!",
	},
	follow: {
		follow: "활용단어참조",
		traderList: "거래원 목록",
		totalProfit: "총손익",
		correctRate: "총 정확도",
		positionValuation: "창고 보유 평가",
		estimatedProfitToday: "오늘 예상 수익",
		rateOfReturn: "수익률",
		becomeATrader: "거래원이 되다",
		traderList: "거래원 목록",
		onlyShowsThePosition: "위치만 표시",
		theDataIsUpdatedEveryHour: "시간당 데이터 업데이트",
		following: "주문 중",
		follow: "활용단어참조",
		confirmFollow: "따르시겠습니까?",
		confirmCancel: "따르기를 취소하시겠습니까?",
		followTrader: "교역원을 따르다",
		editCopy: "편집 따르기",
		beforeCopy: "따라가기를 선택하기 전에 자세히 읽어주세요.",
		copyAgreement: "계약 따르기",
		copyType: "수행 방식",
		copyType1: "고정 배수 따르기",
		copyType2: "붙다",
		copyAlert1: "거래원이 얼마를 주문하든지 간에 당신은 선택한 고정 배수에 따라 따라가세요.",
		copyAlert2: "(축척 수가 >1이고 정수가 아닌 경우 반올림은 두 소수점 값을 유지합니다.)",
		copyMultiple: "배수를 따르다",
		cancelCopy: "따르기 취소",
		currentFollowerNumber: "현재 수행 인원수",
		totalProfitAndLoss: "총손익",
		totalReturn: "총수익률",
		accuracy: "정확도",
		profitableOrders: "이익 주문",
		successfulShortTrades: "공매매에 성공하다",
		successfulLongTrades: "다교역에 성공하다",
		yesterdaysTradingStatus: "어제 교역 상황",
		currentPositions: "현재 보유",
		totalOrders: "총 주문",
		remain: "잉여",
		distanceDue: "거리 만료",
		days: "일",
		deadline: "기일",
		subscription: "구입 신청을 하다",
		followUsers: "사용자 따라가기",
		followAmount: "수행 금액",
		followEarnings: "수익을 따르다",
		cancelFollow: "따르기 취소",
		currentCopy: "현재",
		historicalCopy: "역사 교역",
		myTrader: "나의 거래원",
		tradingPair: "교역",
		open: "창고 개설 가격",
		close: "적정가격",
		earnings: "수익",
		lots: "수단",
		time: "거래 시간",
		buyIn: "매입",
		buyOut: "팔다",
		direction: "방향",
	},
	financial: {
		product: "생산품",
		position: "지니다",
		financial: "금융",
		lockming: "채광",
		ieo: "IEO",
		invest: "투자 재테크",
		section: "특별 구역",
		apr: "연간수익률",
		linkedReferencePrice: "연계 참조 가격",
		holdingDays: "창고 보유 기한",
		day: "일",
		lockPeriod: "창고 잠금 주기",
		ing: "진행 중",
		done: "종료됨",
		applySubscription: "청약을 신청하다",
		remaining: "잉여",
		total: "총량",
		subscribed: "이미 청약했다",
		mining: "채광",
		minimum: "시작하다",
		dailyReturnRate: "일수익률",
	},
	nft: {
		nft: "NFT",
		artwork: "예술품",
		artist: "아티스트",
		artworkList: "미술품 목록",
		artistList: "아티스트 목록",
		comprehensiveSorting: "종합 정렬",
		newest: "최신",
		hotest: "가장 더웠어",
		mostWorks: "작품이 가장 많다",
		mostPopular: "가장 인기 있다",
		record: "구매 기록",
		recommendArtist: "추천하는 아티스트",
		buy: "구입",
		artistHomepage: "아티스트 홈 페이지",
		works: "작품.",
		saled: "이미 팔다",
		fans: "분홍색",
		allWorks: "전작",
		notSell: "미판매",
		myCollect: "나의 소장품",
		collect: "소장했어",
		own: "지니다",
		placeABid: "값을 매기다",
		purchase: "구입",
		open: "후 개방",
		image: "그림",
		gif: "그림",
		audio: "오디오",
		video: "비디오",
		creator: "작자",
		currentBid: "최신 가격",
		price: "가격.",
		d: "일",
		h: "시간",
		m: "분",
		s: "초 단위",
		saled: "종료됨",
		place_alert: "아이템 미판매 혹은 종료",
		createdBy: "작자",
		place_alert2: "당신의 가격은 반드시",
		note: "중요",
		CURRENT_BID: "최신 가격",
		place_alert3: "경매에 참여하려면 지불해야 합니다",
		place_alert4: "의 보증금, 경매가 끝날 때 보증금은 당신의 계좌로 반환됩니다.",
		place_alert6: "제시 시간이 마감된 후, 만약 당신이 경매에 성공한다면, 당신은 어느 날 최종 가격을 지불할 것입니다.만약 시간을 초과하여 지불한다면 당신의 보증금을 공제할 것입니다.",
		confirmBid: "확인 가격",
		DESCRIPTION: "묘사",
		ADDRESS: "소재지",
		copy: "복제하다",
		per_increase: "매번 값을 올리다",
		currency_type: "통화 유형",
		pay_type: "구입 방법",
		pay_type_2: "판매 방식",
		normal: "여간하다",
		auction: "경매",
		confirmAuction: "이 상품 경매에 참여하시겠습니까?",
		bindBox: "블라인드 박스",
		sell_status: "판매 상태",
		hasStart: "시작",
		willStart: "시작하지 않음",
		artwork_type: "품목 유형",
		reset: "재설정",
		confirm: "확정하다",
		place_alert5: "키워드 입력",
		collection: "모음",
		byCollection: "소장되다",
		artworks: "작품.",
		goCollect: "수장하다",
		price: "가격.",
		nonickname: "닉네임이 설정되지 않았습니다.",
		cjdjs: "가격 마감 시간",
		cjkssj: "입찰 시작 시간",
		cjjzsj: "가격 마감 시간",
		gotosee: "얼른 시장에 가보세요.",
		position: "지니다",
		resell: "전매하다",
		buyPrice: "매입 가격",
		confirmResell: "전매 확인",
		wrongPrice: "정확한 가격을 선택하세요.",
		bidPlacedBy: "값을 부르는 사람",
		placeRecord: "가격 기록",
		confirPurchase: "구매 확인",
		artistList: "아티스트 목록",
		hasOpen: "켜짐",
		noOpen: "오픈하지 않음",
		clickOpen: "블라인드 박스를 열다",
		confirmOpen: "블라인드 박스 오픈하시겠습니까?",
		margin: "보증금",
		wrongPrice: "정확한 가격을 입력하십시오.",
		wrongPerIncrease: "올바른 가격 인상을 입력하십시오.",
		wrongTime: "종료 시간이 종료 시간보다 늦어야 합니다.",
		confirmResell2: "상품이 일단 전매되면 철회할 수 없으니 이 상품을 전매하시겠습니까?",
		reselling: "전매 중",
		message: "메시지 알림",
		confirmPay: "이 상품 결제하고 구매하시겠습니까?",
		zfdjs: "지불 카운트다운",
		payTime: "지불 시간",
		hasRead: "읽음",
		lastPrice: "최종 낙찰가",
		isPay: "지불됨",
		isExpired: "만료됨",
		rarity: "희귀도",
	}
}

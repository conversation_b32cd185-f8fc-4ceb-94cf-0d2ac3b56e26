export default {
	nav: ['<PERSON><PERSON><PERSON>', 'Mercados', 'Negociar', 'Finanças', 'Ativos'],
	common: {
		search: 'Pesquisar',
		confirm: "OK",
		cancel: "Cancelar",
		submit: "Enviar",
		logout: 'sair',
		all: "todos",
		edit: "Editar",
		delete: "Excluir",
		reset: "Redefinir",
		filter: "Filtrar",
		login: "entrar",
		register: "registrar",
		email: "Email",
		mobile: "Número de telefone celular",
		account: "Conta",
		password: "<PERSON><PERSON>",
		passwordPlaceholder: "Por favor, digite sua senha",
		noaccount: "Não tem conta?",
		go: "ir",
		forgetPassword: "Esqueceu a Senha",
		confirmPassword: "Confirmar Senha",
		invitecode: "Código de Convite",
		hasaccount: "Já tem uma conta?",
		mobilecodePlaceholder: "Por favor, digite o código de verificação do celular",
		emailcodePlaceholder: "Por favor, digite o código de verificação do email",
		send: "Enviar código de verificação",
		selectArea: "Seleção de área",
		audit: "Auditoria",
		underAudit: "Em análise",
		auditFailure: "Auditoria falhou",
		untie: "desvincular",
		buy: "comprar",
		sell: "vender",
		plsLogin: "Por favor, faça login primeiro",
		plsInputUsername: "Por favor, digite uma conta",
		plsInputRePassword: "Por favor, digite a confirmação da senha",
		plsInputInviteCode: "Por favor, digite o código de convite",
		plsInputCode: "Por favor, digite o código de verificação",
		plsInputMobile: "Por favor, digite o número do celular",
		plsInputEmail: "Por favor, digite seu email",
		login_success: "Login bem-sucedido",
		login_fail: "Falha no login",
		reg_success: "Registro bem-sucedido",
		reg_fail: "Falha no login",
		pwdInconsistent: "As duas senhas são inconsistentes",
		hint: "dica",
		loading: 'Carregando',
		success: "Sucesso",
		plsInput: "Por favor, digite",
		click: "clique",
		quotation: "Cotação",
		edit: "Editar",
		second: 'segundo',
		specialChart: 'Caracteres especiais não são permitidos',
		open: 'Preço de abertura',
		close: 'Preço de fechamento',
		high: 'Preço mais alto',
		low: 'preço mais baixo',
		customerService: 'Contatar atendimento ao cliente',
		rememberPassword: 'Lembrar senha',
		newPassword: 'Nova senha',
		pwdMoreThen6: 'O comprimento da senha deve ser maior que seis dígitos',
		codeLength6: 'O comprimento do código de verificação deve ser de seis dígitos',
		rePwdMoreThen6: 'O comprimento da confirmação da senha deve ser maior que seis dígitos',
		pls: 'por favor',
		saveSuccess: "Salvo no álbum do sistema",
		nextStep: "Próximo",
		log: 'registro',
		paramsWrong: "Erro de parâmetro",
		functionLoading: "Função em desenvolvimento",
		DIGICCY: "Moeda Digital",
		quickly: "rápido",
		more: "mais",
		hasNoData: 'Não há mais dados',
		audioError: 'Erro no arquivo de áudio',
		toLong: 'Caractere muito longo',
		selectLang : "Selecionar idioma",
	},
	home: {
		contactUs: 'Fale Conosco',
		borrowing: 'Empréstimo',
		borrowing_entry: 'entrada de empréstimo',
		repayment_entry: 'entrada de pagamento de empréstimo',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: 'Equivalente de ativos totais',
		recharge: 'Recarregar',
		withdraw: 'Saque',
		stock: 'ações',
		futures: 'Contrato',
		copytrade: 'Copiar',
		financial: 'gestão financeira',
		margin: 'Super Alavancagem',
		otc: 'OTC',
		coupon: 'Centro de Cupons',
		bills: 'Contas',
		exchange: 'Câmbio',
		mining: 'bloquear para ganhar moedas',
		game: 'mini jogo',
		chatroom: 'sala de bate-papo',
		quicklyBuyCoins: 'Compra Rápida de Moedas',
		defiMining: 'Mineração DeFi',
		cloudMining: 'Minerador em Nuvem',
		stock: 'ações',
		stock_info: 'Não é necessário abrir uma posição, manter uma posição imediatamente',
		lockming: 'mineração bloqueada',
		transfer: "Transferência de moedas",
		ieo: "IEO",
		tradeQuery: "Consulta de Negociação",
		seconds: "Opções",
		day: "dia",
		minimum: 'começar',
		dailyReturnRate: "Taxa de retorno diária",
		more: "mais",
		market: "Cotação",
		tradingPair: 'par de negociação',
		lastPrice: 'Último Preço',
		todayChange: 'Mudança de hoje',
		transaction: 'Transação',
		nft: 'NFT',
		invest: "Investimento",
		download: 'baixar',
		app_text: 'A qualquer hora, em qualquer lugar, transação online',
		follow: 'Seguir',
	},
	setting: {
		my_qrcode: 'Meu código QR',
		mine: 'Meu',
		inviteDesc: 'Convide amigos, desconto juntos',
		inviteDesc2: 'Desfrute de descontos com amigos',
		inviteNumber: 'Número de convites',
		inviteTransNumber: 'Número de pessoas que negociaram',
		myRebate: 'Meu desconto',
		bill: "Conta",
		wallet: "Endereço da carteira",
		banks: "Vinculação de Cartão Bancário",
		banksInternational: "Vinculação de Cartão Bancário - Internacional",
		addBank: "Adicionar cartão bancário",
		securitySettings: "Configurações de Segurança",
		coupon: "Centro de Cupons",
		systemNotification: "Notificação do Sistema",
		defaultFiatCurrency: "Moeda Fiduciária Padrão",
		language: "idioma",
		faq: "Perguntas Frequentes",
		operationalCompliance: "Conformidade Operacional",
		share: "Compartilhar",
		contactUs: "Fale Conosco",
		aboutUs: "Sobre Nós",
		avatar: 'avatar',
		nickname: 'apelido',
		signature: 'assinatura pessoal',
		grxx: "informações pessoais",
		grxxOptions: {
			nickname: {
				title: 'apelido',
				placeholder: 'Por favor, digite um apelido',
				desc: '2-10 caracteres, suporte chinês e inglês, números',
			},
			signature: {
				title: 'assinatura pessoal',
				placeholder: 'Por favor, digite uma assinatura personalizada',
				desc: '2-30 caracteres, suporte chinês e inglês, números',
			}
		},
		xxtx: "Carregar Avatar",
		photograph: 'tirar uma foto',
		selectFromAlbum: 'Selecionar do álbum',
		aqsz: "Configurações de Segurança",
		editLoginPassword: 'Modificar senha de login',
		setPayPassword: "Definir senha de pagamento",
		editPayPassword: "Modificar senha de pagamento",
		bindPhone: "Vincular telefone",
		email: 'email',
		stayLogin: 'permanecer logado',
		xgdlmm: "Modificar senha de login",
		currentPassword: 'senha de login atual',
		currentPasswordPlaceholder: 'Por favor, digite a senha de login atual',
		newPassword: "Nova senha de login",
		newPasswordPlaceholder: "Por favor, digite uma nova senha de login",
		confirmNewPassword: "Confirmar nova senha",
		confirmNewPasswordPlaceholder: "Por favor, digite uma nova senha para confirmar",
		currentPayPassword: "Senha de Pagamento Atual",
		currentPayPasswordPlaceholder: 'Por favor, digite a senha de pagamento atual',
		newPayPassword: "Nova Senha de Pagamento",
		newPayPasswordPlaceholder: "Por favor, digite uma nova senha de pagamento",
		confirmNewPayPassword: "Confirmar nova senha de pagamento",
		confirmNewPayPasswordPlaceholder: "Por favor, digite a confirmação da nova senha de pagamento",
		phoneVerificationCode: 'Código de verificação do telefone',
		editPhoneVerificationCode: "Por favor, digite o código de verificação do telefone",
		getVerificationCode: 'Obter código de verificação',
		lctyq: "Voucher de Experiência de Gestão Financeira",
		expired: "Expirado",
		expiredTime: "Data de Expiração",
		useImmediately: "Usar Imediatamente",
		addNewAccount: "Adicionar uma nova conta",
		totayIncome: "Coleta hoje",
		totalIncome: "Recebimentos acumulados",
		selectLang: "Por favor, selecione o idioma",
		selectCoinType: "Por favor, selecione uma moeda",
		authentication: 'Autenticação',
		authentication1: 'Autenticação básica',
		authentication2: 'Autenticação Avançada',
		authentication_text_1: 'Autenticação de identidade pessoal',
		authentication_text_2: 'Permissões disponíveis após autenticação:',
		authentication_text_3: 'Lv1.Autenticação Básica',
		authentication_text_4: 'Pode ser refletido após autenticação, o limite de 24 horas é 200BTC',
		authentication_text_5: 'Após autenticação, transações de moeda fiduciária podem ser realizadas, com limite único de 2000USDT',
		authentication_text_6: 'Lv2. Autenticação Avançada',
		authentication_text_7: 'Aumentar o limite de crédito, o limite de 24 horas é 500BTC',
		authentication_text_8: 'Aumentar o limite de transação de moeda fiduciária, limite único de 1000000USDT',
		authentication_text_9: 'Lembrete: Para proteger a segurança dos fundos do usuário, complete primeiro a vinculação de email e telefone, caso contrário sua autenticação falhará. ',
		goAudit: 'Ir para autenticação',
		noaudit: 'Não autenticado',
		auditing: 'autenticando',
		hasaudit: 'autenticado',
		idType: 'Tipo de ID',
		idCard: 'Cartão de Identidade',
		passport: 'Passaporte',
		driverLicense: 'Carteira de Motorista',
		name: 'nome',
		idcard: 'número do ID',
		plsIptName: 'Por favor, digite um nome',
		plsIptCorrectIdcard: 'Por favor, digite o número de ID correto',
		uploadIdcardFront: 'Carregar frente do cartão de identidade',
		uploadIdcardReverse: 'Carregar verso do cartão de identidade',
		emailVerificationCode: 'Código de Verificação de Email',
		plsiptEmailCode: 'Por favor, digite o código de verificação de email',
		addWallet: 'Adicionar endereço da carteira',
		selectCurrency: 'Selecionar moeda',
		plsSelectCurrency: 'Por favor, selecione uma moeda',
		plsIptAddress: 'Por favor, digite o endereço da carteira',
		walletAddress: 'endereço da carteira',
		walletQrcode: 'Código QR da carteira',
		plsIptWalletAddress: 'Por favor, digite o endereço da carteira',
		plsUploadWalletQrcode: 'Por favor, carregue o código QR da carteira',
		confirmLogout: 'Tem certeza de que deseja sair? ',
		nonickname: 'Por favor, defina um apelido',
		plsInputMobile: "Por favor, digite o número do celular",
		alipay_account: 'número ABA',
		real_name: 'nome real',
		bank_account: 'número do cartão bancário',
		bank_dizhi: 'endereço do banco',
		bank_name: 'banco de abertura de conta',
		bank_network: 'rede bancária',
		swift_code: 'Swift',
		phone: 'Telefone',
		wechat_account: 'endereço do portador do cartão',
		wechat_nickname: 'código SWIFT',
		allNeed: 'Todos são obrigatórios',
		phoneNumber : "número do celular",
		code : "Código de Verificação",
		sendCode : "Enviar código de verificação",
		plsIptCode : "Por favor, digite o código de verificação",
		withdrawPassword: 'Senha de Saque',
		confirmWithdrawPassword: 'Confirmar Senha de Saque',
		plsInputWithdrawPassword: 'Por favor, digite a senha de saque',
		plsInputConfirmWithdrawPassword: 'Por favor, confirme a senha de saque',
		withdrawPasswordPlaceholder: 'Digite uma senha de saque de 6-16 dígitos',
		confirmWithdrawPasswordPlaceholder: 'Digite a senha de saque novamente',
		updateWithdrawPassword: 'Atualizar Senha de Saque',
		oldWithdrawPassword: 'Senha de Saque Antiga',
		newWithdrawPassword: 'Nova Senha de Saque',
		plsInputOldWithdrawPassword: 'Por favor, digite a senha de saque antiga',
		plsInputNewWithdrawPassword: 'Por favor, digite a nova senha de saque',
		withdrawPasswordNotMatch: 'As senhas de saque não coincidem',
		withdrawPasswordLengthError: 'O comprimento da senha de saque deve ser de 6-16 caracteres',
		withdrawPasswordRequired: 'Por favor, defina uma senha de saque',
		withdrawPasswordVerifyFailed: 'Falha na verificação da senha de saque',
		needRealAuth: "A autenticação de nome real deve ser concluída antes da negociação",
		realAuthPending: "A autenticação de nome real está em análise, aguarde a conclusão",
		score: "Pontuação"
	},
	transaction: {
		actualPL: 'PL Real',
		favorites: "Opcional",
		futures: "Contrato",
		coins: "Moedas",
		stock: "ações",
		tradingPair: 'par de negociação',
		lastPrice: 'Último Preço',
		todayChange: 'Mudança de hoje',
		searchPlaceholder: 'Pesquisar pela moeda/nome da ação/símbolo que você se importa',
		hotSearch: 'Pesquisa Quente',
		recentView: 'Visualizado recentemente',
		dayHigh: "Máxima de Hoje",
		dayLow: "Mínima de hoje",
		optional: 'opcional',
		buy: 'comprar',
		sell: 'vender',
		entrustPendingOrder: 'Ordem pendente de confiança',
		addOptionalSuccess: 'Adicionar opcional com sucesso',
		delOptionalSuccess: 'Excluir opcional com sucesso',
		seconds: "Opções",
		home: "Início",
		orderConfirm: "Confirmação do Pedido",
		currentPrice: 'preço atual',
		direction: "direção",
		selectTime: "Selecionar tempo de expiração",
		number: "Número",
		balance: 'saldo da conta',
		plsIptCrtNumber: 'Por favor, digite o número correto',
		buyPrice: 'preço de compra',
		expectedPL: "Lucro e perda esperados",
		continueTrade: "Continuar a negociar",
		secondsPosition: 'Posição de opção',
		position: 'mantendo',
		history: 'Histórico',
		orderTimes: 'Duração do pedido',
		pl: "lucro e perda",
		sellTime: "Tempo de Venda",
		buy: 'comprar',
		sell: 'vender',
		price: 'preço',
		time: "tempo",
		optional: 'opcional',
		buy: 'comprar',
		sell: 'vender',
		long: "fazer mais",
		short: "Curto",
		entrustPendingOrder: 'Ordem pendente de confiança',
		addOptionalSuccess: 'Adicionar opcional com sucesso',
		delOptionalSuccess: 'Excluir opcional com sucesso',
		price: "preço",
		amount: "Quantidade",
		marketPrice: "Preço de Mercado",
		limitPrice: "Preço Limite",
		number: "Número de lotes para negociar",
		margin: "Margem",
		handlingFee: "taxa de manuseio",
		balance: "Saldo",
		multiple: "Múltiplo",
		recharge: 'recarregar',
		plsIptCommissionPrice: 'Por favor, digite o preço da comissão',
		tradingPair: "Par de Negociação",
		direction: "direção",
		plsIptCrtPrice: 'Por favor, digite o preço correto',
		position: "Posição",
		time: "tempo",
		turnover: "volume de transação",
		operation: "Operação",
		type: "Tipo",
		cover: 'Fechar posição',
		confirmCover: 'Confirmar para fechar a posição? ',
		confirmSelfHold: 'Tem certeza de que deseja mudar para auto-sustentação? ',
		selfHolding: 'auto-sustentação',
		transSelfHold: 'Virar auto-sustentação',
		delegateList: "Lista de Delegados",
		riskRate: "Taxa de Risco",
		totalPandL: 'Lucro total da posição',
		oneClickCover: "Fechar posição com um clique",
		open: "Preço de abertura de posição",
		targetProfitPrice: "Preço de Lucro Alvo",
		updatePrice: "Preço Atual",
		stopLossPrice: "Preço de Stop Loss",
		overnightMoney: "Taxa Noturna",
		openTime: "Tempo de abertura",
		lots: "mão",
		setProfitLoss: "Definir Lucro e Stop Loss",
		expectedProfit: "Lucro Esperado",
		expectedLoss: "Perda esperada",
		coverAll: "Fechar todas as posições",
		onlyCoverMany: "Apenas cobrir ordens longas",
		onlyCoverEmpty: "Apenas cobrir ordens vazias",
		numbers: 'Número',
		coinBuyPrice: 'Negociar ao melhor preço atual',
		jine: 'quantidade',
		p_jine: 'Por favor, digite a quantidade',
		p_price: 'Por favor, digite o preço',
		p_confirm_sj: 'Confirmar ao preço de mercado',
		p_confirm_xj_1: 'Confirmar ao preço atual',
		p_confirm_ma: '? ',
		status: "Status",
		complete: "Concluído",
		doing: "em progresso",
		currentEntrust: "Confiança Atual",
		allEntrust: "Toda Confiança",
		entrust: "Confiança",
		entrustTime: "Tempo de Confiança",
		all: "todos",
		buyUp: "Comprar mais",
		buyDown: "Comprar menos",
		jye:'Quantidade da Transação',
	},
	fund: {
		overview: 'Visão Geral',
		balance: 'saldo',
		futures: 'contrato',
		deposit: 'Depósito',
		mining: 'Bloquear para ganhar moedas',
		stock: 'ações',
		margin: 'Super Alavancagem',
		yesterdayEarnings: `Ganhos de ontem`,
		yieldAtMaturily: 'Rendimento no vencimento',
		totalkRevenue: 'Receita total',
		currentProfit: 'Lucro e perda atual',
		profitRatio: 'Taxa de lucro',
		receive: 'recarregar/receber dinheiro',
		transfer: 'Saque/Transferência',
		financial: 'gestão financeira',
		valuation: 'avaliação',
		currentEarnings: 'Ganhos atuais',
		yesterdayIncome: `Renda de ontem`,
		crypto: 'moeda digital',
		fiat: 'moeda fiduciária',
		hideSmallCurrency: 'Ocultar Moeda Pequena',
		available: 'disponível',
		freeze: 'congelar',
		equivalent: 'equivalente',
		usdtm: 'padrão U',
		coinm: 'padrão de moeda',
		exchange: "Moeda",
		leverage: 'contrato',
		fiat: 'moeda fiduciária',
		second: 'Opção',
		convert: "Converter ativos totais",
		convert2: "Conversão de Ativos",
		record: "Registro de mudança de conta",
		availableQuota: "Cota Disponível",
		locked: "Bloqueado",
		converted: "convertido",
		account: "Conta",
		financialRecords: "Registros Financeiros",
		number: "Número",
		record2: "registro",
		time: "tempo",
		transfer: "Transferência de Moedas",
		from: "de",
		to: "para",
		transferNumber: "Número de Transferência",
		youGet: "você receberá",
		exchangeRate: "Taxa de Câmbio",
		handlingFee: "taxa de manuseio",
		fundTransfer: "Transferência de Fundos",
		insufficientBalance: "Saldo insuficiente",
		needHandlingFee: "Taxa de manuseio adicional",
		plsIptCrtNumber: 'Por favor, digite o número correto',
		c_transfer: "Tem certeza de que deseja transferir?",
		selectCurrency: "Selecionar Moeda",
		DIGICCY: 'Moeda Digital',
		saveQrcode: 'Salvar código QR',
		copyAddress: 'Copiar endereço do link',
		plsTrans: 'Por favor, transfira para este endereço',
		rechargeNumer: "Número de recargas",
		paymentVoucher: "Comprovante de Pagamento",
		plsIptRechargeNumer: "Por favor, digite o valor da recarga",
		plsUploadPaymentVoucher: "Por favor, carregue o comprovante de pagamento",
		plsIptCrtAmount: 'Por favor, digite o valor correto',
		withdraw: "Saque/Transferência",
		currencyName: "Nome da moeda",
		transactionInfo: "Conta de Transação",
		withdraw2: 'Saque',
		canUse: 'disponível',
		all: 'todos',
		plsIptWithdrawNumber: 'Por favor, digite o valor do saque',
		withdrawNumber: 'valor do saque',
		remark: 'Observação',
		handlingFee: 'manuseio',
		canGetNumber: 'Número de contas recebidas',
		leastNumber: 'Valor mínimo de saque',
		coinType: 'moeda',
		address: 'Endereço',
		date: 'data',
		withdrawAddress: 'Endereço de saque',
		plsSelectWithdrawAddress: 'Por favor, selecione o endereço de saque',
		withdrawToCard: 'mencionado cartão bancário',
		withdrawToCardInternational: 'internacional',
		withdrawToAddress: 'mencionado endereço da carteira',
		card: 'cartão bancário',
		addCard: 'Adicionar cartão bancário',
		plsAddCard: 'Por favor, adicione um cartão bancário',
		plsInputWithdrawPasswordForWithdraw: 'Por favor, digite a senha de saque para verificação'
	},
	ieo: {
		subscribe: "Inscrever-se",
		ing: "em progresso",
		done: "Feito",
		applySubscription: "Aplicar Inscrição",
		remaining: "Restante",
		lockPeriod: "Período de Bloqueio",
		endTime: "Hora de Término",
		subscribeCurrency: "Moeda de inscrição",
		issuancePrice: "Preço de Emissão",
		totalIssuance: "Emissão Total",
		whitePaper: "Livro Branco",
		subscriptionCycle: "Ciclo de Inscrição",
		warmUp: "Aquecimento",
		website: "site oficial",
		finish: "terminar",
		projectDetails: "Detalhes do Projeto",
		subscriptionConfirmation: "Confirmação de Inscrição",
		p_applicationsNumber: "Digite o número de inscrições",
		needToPay: "Precisa Pagar",
		currentBalance: "Saldo Atual",
		subscriptionTime: "Tempo de Inscrição",
		currency: "Moeda",
		applicationsNumber: "Número de aplicações",
		passesNumber: "Número de passes",
		timeToMarket: "Tempo para o Mercado",
		day: "dia",
		total: "Total",
		subscribed: 'Inscrito',
		shengou: "Inscrição",
		insufficientBalance: "Saldo insuficiente",
		p_number: "Por favor, digite o número",
		mySubscribe: "Minha Inscrição"
	},
	lockming: {
		lockming: 'Mineração bloqueada',
		fundsUnderCustody: "Fundos sob custódia",
		entrustedOrders: "Ordens Confiadas",
		estimatedTodayIncome: "Taxa de retorno estimada para hoje",
		cumulativeIncome: "Renda Cumulativa",
		ordersInCustody: "Ordens em Custódia",
		lockedPositionsToEarnCoins: "Bloqueado para ganhar moedas",
		lockedPositions: "Bloqueado",
	}
}

export default {
	nav: ['Página principal', 'Cotización', 'Comercio', 'Finanzas', 'Activos'],
	common: {
		search: "Buscar",
		confirm: "Determinar",
		cancel: "Cancelar",
		submit: "Submission",
		logout: "Salida",
		all: "Todos",
		edit: "Editar",
		delete: "Borrar",
		reset: "Reset",
		filter: "Screening",
		login: "Iniciar sesión",
		register: "Registro",
		email: "Buzón de correo",
		mobile: "Número de teléfono móvil",
		account: "Cuenta",
		password: "Contraseña",
		passwordPlaceholder: "Introduzca la contraseña",
		noaccount: "¿Sin número de cuenta?",
		go: "Ir",
		forgetPassword: "Olvida la contraseña.",
		confirmPassword: "Confirmar contraseña",
		invitecode: "Código de invitación",
		hasaccount: "¿Cuenta existente?",
		mobilecodePlaceholder: "Introduzca el Código de verificación del teléfono móvil",
		emailcodePlaceholder: "Introduzca el Código de verificación del buzón de correo",
		send: "Enviar Código de verificación",
		selectArea: "Selección regional",
		audit: "Auditoría",
		underAudit: "Auditoría en curso",
		auditFailure: "Fallo de auditoría",
		untie: "Desenganche",
		buy: "Comprar",
		sell: "Vender",
		plsLogin: "Por favor, inicie sesión primero",
		plsInputUsername: "Por favor, introduzca una cuenta",
		plsInputRePassword: "Introduzca la contraseña de confirmación",
		plsInputInviteCode: "Introduzca el Código de invitación",
		plsInputCode: "Introduzca el Código de verificación",
		plsInputMobile: "Por favor, introduzca el número de teléfono",
		plsInputEmail: "Por favor, introduzca el buzón de correo",
		login_success: "Inicio de sesión exitoso",
		login_fail: "Falló el inicio de sesión",
		reg_success: "Registro exitoso",
		reg_fail: "Falló el inicio de sesión",
		pwdInconsistent: "Dos contraseñas inconsistentes",
		hint: "Prompt",
		loading: "Cargado",
		success: "éxito",
		plsInput: "Por favor, introduzca",
		click: "Click",
		quotation: "Cotización",
		edit: "Editar",
		second: "Segundos",
		specialChart: "No se permiten caracteres especiales",
		open: "Precio de apertura",
		close: "Precio de cierre",
		high: "Precio máximo",
		low: "Precio más bajo",
		customerService: "Contacto con el servicio al cliente",
		rememberPassword: "Recuerde la contraseña",
		newPassword: "Nueva contraseña",
		pwdMoreThen6: "La longitud de la contraseña debe ser mayor de seis dígitos",
		codeLength6: "La longitud del Código de verificación debe ser de seis dígitos",
		rePwdMoreThen6: "La longitud de la contraseña de confirmación debe ser mayor de seis dígitos",
		pls: "Por favor.",
		saveSuccess: "Guardado en el álbum del sistema",
		nextStep: "Siguiente paso",
		log: "Registro",
		paramsWrong: "Error de parámetro",
		functionLoading: "Desarrollo funcional",
		DIGICCY: "Moneda digital",
		quickly: "Rápido",
		more: "Más",
		hasNoData: "No hay más datos.",
		audioError: "Error en el archivo de audio",
		toLong: "Carácter demasiado largo",
		selectLang : "Seleccionar idioma",
	},
	home: {
		contactUs: 'Contáctenos.',
		borrowing: 'Préstamo',
		borrowing_entry: 'entrada de préstamo',
		repayment_entry: 'entrada de reembolso de préstamo',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: "Conversión total de activos",
		recharge: "Recargar",
		withdraw: "Presentación",
		stock: "Acciones",
		futures: "Contrato",
		copytrade: "Merchandiser",
		financial: "Financiación financiera",
		margin: "Super palanca",
		otc: "OTC",
		coupon: "Centro de vales",
		bills: "Bill",
		exchange: "Cambio de divisas",
		mining: "minería",
		game: "Juegos",
		chatroom: "Sala de chat",
		quicklyBuyCoins: "Compra rápida de monedas",
		defiMining: "Minería defi",
		cloudMining: "Máquina de extracción de nubes",
		stock: "Acciones",
		stock_info: "No hay necesidad de abrir una posición.",
		lockming: "minería",
		transfer: "Transferencia de moneda",
		ieo: "IEO",
		tradeQuery: "Consulta de Operaciones",
		seconds: "Opciones",
		day: "Días",
		minimum: "Levántate.",
		dailyReturnRate: "Rendimiento diario",
		more: "Más",
		market: "Cotización",
		tradingPair: "Par de transacciones",
		lastPrice: "El último precio",
		todayChange: "Aumento o disminución hoy",
		transaction: "Comercio",
		nft: "Nft",
		invest: "Invertir",
		download: "Descargar",
		app_text: "Transacciones en línea en cualquier momento y en cualquier lugar",
	},
	setting: {
		my_qrcode: 'Mi código QR',
		mine: 'Mine',
		inviteDesc: "Invite a sus amigos a un descuento",
		inviteDesc2: "Disfrute del descuento con sus amigos",
		inviteNumber: "Número de invitados",
		inviteTransNumber: "Número de personas negociadas",
		myRebate: "Mi descuento",
		bill: "Bill",
		wallet: "Dirección de la cartera",
		banks: "Unión de tarjetas bancarias",
		banksInternational: "Unión de tarjetas bancarias - International",
		addBank: "Añadir tarjeta bancaria",
		securitySettings: "Configuración de Seguridad",
		coupon: "Centro de vales",
		systemNotification: "Notificación del sistema",
		defaultFiatCurrency: "Moneda francesa predeterminada",
		language: "Idioma",
		faq: "Preguntas frecuentes",
		operationalCompliance: "Cumplimiento operacional",
		share: "Compartir",
		contactUs: "Contáctenos.",
		aboutUs: "Sobre nosotros",
		avatar: "Avatar",
		nickname: "Apodo",
		signature: "Firma personal",
		grxx: "Información personal",
		grxxOptions: {
			nickname: {
				title: "Apodo",
				placeholder: "Por favor, introduzca un apodo",
				desc: "2 - 10 caracteres, soporte chino - inglés, número",
			},
			signature: {
				title: "Apodo",
				placeholder: "Por favor, introduzca un apodo",
				desc: "2 - 10 caracteres, soporte chino - inglés, número",
			}
		},
		xxtx: "Subir avatar",
		photograph: "Tomar fotos",
		selectFromAlbum: "Seleccionar del álbum",
		aqsz: "Configuración de Seguridad",
		editLoginPassword: "Modificar contraseña de inicio de sesión",
		setPayPassword: "Establecer contraseña de pago",
		editPayPassword: "Modificar la contraseña de pago",
		bindPhone: "Teléfono móvil vinculante",
		email: "Buzón de correo",
		stayLogin: "Permanecer conectado",
		xgdlmm: "Modificar contraseña de inicio de sesión",
		currentPassword: "Contraseña de inicio de sesión actual",
		currentPasswordPlaceholder: "Introduzca la contraseña de inicio de sesión actual",
		newPassword: "Nueva contraseña de inicio de sesión",
		newPasswordPlaceholder: "Introduzca una nueva contraseña de inicio de sesión",
		confirmNewPassword: "Confirmar nueva contraseña",
		confirmNewPasswordPlaceholder: "Introduzca una nueva contraseña para confirmar",
		currentPayPassword: "Contraseña de pago actual",
		currentPayPasswordPlaceholder: "Introduzca la contraseña de pago actual",
		newPayPassword: "Nueva contraseña de pago",
		newPayPasswordPlaceholder: "Introduzca una nueva contraseña de pago",
		confirmNewPayPassword: "Confirmar nueva contraseña de pago",
		confirmNewPayPasswordPlaceholder: "Introduzca la contraseña para confirmar el nuevo pago",
		phoneVerificationCode: "Código de verificación del teléfono móvil",
		editPhoneVerificationCode: "Introduzca el Código de verificación del teléfono móvil",
		getVerificationCode: "Obtener Código de verificación",
		lctyq: "Certificado de experiencia financiera",
		expired: "Caducado",
		expiredTime: "Fecha de caducidad",
		useImmediately: "Uso inmediato",
		addNewAccount: "Añadir una nueva cuenta",
		totayIncome: "Recoger hoy",
		totalIncome: "Recaudación acumulada",
		selectLang: "Por favor, seleccione el idioma",
		selectCoinType: "Seleccione la moneda",
		authentication: "Autenticación de la identidad",
		authentication1: "Autenticación básica",
		authentication2: "Certificación avanzada",
		authentication_text_1: "Autenticación de la identidad personal",
		authentication_text_2: "Permisos disponibles después de completar la autenticación:",
		authentication_text_3: "LV1. Autenticación básica",
		authentication_text_4: "Después de la certificación puede reflejar, límite de 24 horas 200btc",
		authentication_text_5: "Puede realizar transacciones en moneda francesa después de la autenticación, con un límite único de 2.000 dólares de los EE.UU.",
		authentication_text_6: "Lv2. Certificación avanzada",
		authentication_text_7: "Aumento del límite de presentación, límite de 24 horas 500btc",
		authentication_text_8: "Aumento del límite de negociación en moneda francesa, límite único de 1.000.000 de dólares de los EE.UU.",
		authentication_text_9: "Consejo cálido: para proteger la seguridad de los fondos de los usuarios, por favor complete el correo electrónico y la Unión telefónica, de lo contrario su autenticación no pasará.",
		goAudit: "Descertificación",
		noaudit: "No autenticado",
		auditing: "En autenticación",
		hasaudit: "Certificado",
		idType: 'Tipo de documento',
		idCard: 'Documento de identidad',
		passport: 'Pasaporte',
		driverLicense: 'Licencia de conducir',
		name: "Nombre",
		idcard: "Número de identificación",
		plsIptName: "Por favor, introduzca un nombre",
		plsIptCorrectIdcard: "Introduzca el número de identificación correcto",
		uploadIdcardFront: "Upload ID Positive",
		uploadIdcardReverse: "Subir la parte posterior de la tarjeta de identidad",
		emailVerificationCode: "Código de verificación del buzón de correo",
		plsiptEmailCode: "Introduzca el Código de verificación del buzón de correo",
		addWallet: "Añadir dirección de cartera",
		selectCurrency: "Seleccionar moneda",
		plsSelectCurrency: "Seleccione la moneda",
		plsIptAddress: "Introduzca la dirección de su cartera",
		walletAddress: "Dirección de la cartera",
		walletQrcode: "Código QR de cartera",
		plsIptWalletAddress: "Introduzca la dirección de su cartera",
		plsUploadWalletQrcode: "Por favor, suba el Código QR de su cartera",
		confirmLogout: "¿Estás seguro de dejar de iniciar sesión?",
		nonickname: "Por favor, establezca un apodo",
		plsInputMobile: "Por favor, introduzca el número de teléfono",
		alipay_account: "Número ABA",
		real_name: "Nombre real",
		bank_account: "Número de tarjeta bancaria",
		bank_dizhi: "Provincias y ciudades con cuentas abiertas",
		bank_name: "Banco de apertura de cuentas",
		bank_network: 'Red de apertura de cuentas',
		swift_code: 'SWIFT',
		phone: 'Teléfono de contacto',
		wechat_account: "Dirección del titular de la tarjeta",
		wechat_nickname: "Código Swift",
		allNeed: "Todos son obligatorios",
		phoneNumber: "Número de teléfono móvil",
		code: "Código de verificación",
		sendCode: "Enviar Código de verificación",
		plsIptCode: "Introduzca el Código de verificación",
		withdrawPassword: 'Contraseña de retiro',
		confirmWithdrawPassword: 'Confirmar contraseña de retiro',
		plsInputWithdrawPassword: 'Por favor ingrese la contraseña de retiro',
		plsInputConfirmWithdrawPassword: 'Por favor confirme la contraseña de retiro',
		withdrawPasswordPlaceholder: 'Ingrese una contraseña de retiro de 6-16 dígitos',
		confirmWithdrawPasswordPlaceholder: 'Ingrese la contraseña de retiro nuevamente',
		updateWithdrawPassword: 'Actualizar contraseña de retiro',
		oldWithdrawPassword: 'Contraseña de retiro anterior',
		newWithdrawPassword: 'Nueva contraseña de retiro',
		plsInputOldWithdrawPassword: 'Por favor ingrese la contraseña de retiro anterior',
		plsInputNewWithdrawPassword: 'Por favor ingrese la nueva contraseña de retiro',
		withdrawPasswordNotMatch: 'Las contraseñas de retiro no coinciden',
		withdrawPasswordLengthError: 'La longitud de la contraseña de retiro debe ser de 6-16 caracteres',
		withdrawPasswordRequired: 'Por favor establezca una contraseña de retiro',
		withdrawPasswordVerifyFailed: 'Falló la verificación de la contraseña de retiro',
		score: 'Score'
	},
	transaction: {
		actualPL: 'Actual PL',
		favorites: "Auto - Elección",
		futures: "Contrato",
		coins: "Moneda",
		stock: "Acciones",
		tradingPair: "Par de transacciones",
		lastPrice: "El último precio",
		todayChange: "Aumento o disminución hoy",
		searchPlaceholder: "Busque monedas / nombres / códigos de acciones que le interesen",
		hotSearch: "Búsqueda caliente",
		recentView: "Vista reciente",
		dayHigh: "Alto hoy",
		dayLow: "Hoy bajo",
		optional: "Auto - Elección",
		buy: "Comprar",
		sell: "Vender",
		entrustPendingOrder: "Certificado de autorización",
		addOptionalSuccess: "Añadir auto - selección con éxito",
		delOptionalSuccess: "Borrar auto - seleccionado con éxito",
		seconds: "Opciones",
		home: "Página principal",
		orderConfirm: "Confirmación del pedido",
		currentPrice: "Precio actual",
		direction: "Dirección",
		selectTime: "Seleccione la fecha de caducidad",
		number: "Cantidad",
		balance: "Saldo de la cuenta",
		plsIptCrtNumber: "Introduzca la cantidad correcta",
		buyPrice: "Precio de compra",
		expectedPL: "Ganancias y pérdidas previstas",
		continueTrade: "Continuar el comercio",
		secondsPosition: "Posición de opción",
		position: "En espera",
		history: "Historia",
		orderTimes: "Duración del pedido",
		pl: "Ganancias y pérdidas",
		sellTime: "Tiempo de venta",
		buy: "Comprar",
		sell: "Vender",
		price: "Precio",
		time: "Tiempo",
		optional: "Auto - Elección",
		buy: "Comprar",
		sell: "Vender",
		long: "Hacer más",
		short: "Short",
		entrustPendingOrder: "Certificado de autorización",
		addOptionalSuccess: "Añadir auto - selección con éxito",
		delOptionalSuccess: "Borrar auto - seleccionado con éxito",
		price: "Precio",
		amount: "Cantidad",
		marketPrice: "Precio de mercado",
		limitPrice: "Límite de precios",
		number: "Número de comerciantes",
		margin: "Margen",
		handlingFee: "Gastos de tramitación",
		balance: "Saldo",
		multiple: "Múltiplo",
		recharge: "Recargar",
		plsIptCommissionPrice: "Por favor, introduzca el precio de envío",
		tradingPair: "Par de transacciones",
		direction: "Dirección",
		plsIptCrtPrice: "Introduzca el precio correcto",
		position: "Posición",
		time: "Tiempo",
		turnover: "Volumen de las transacciones",
		operation: "Operaciones",
		type: "Tipo",
		cover: "Cierre",
		confirmCover: "¿Estás seguro de cerrar?",
		confirmSelfHold: "¿Estás seguro de que te conviertes?",
		selfHolding: "Autosuficiencia",
		transSelfHold: "Autosuficiencia",
		delegateList: "Lista de delegados",
		riskRate: "Tasa de riesgo",
		totalPandL: "Ingresos brutos por posición",
		oneClickCover: "Cierre de un botón",
		open: "Precio de apertura",
		targetProfitPrice: "Precio de parada",
		updatePrice: "Precio actual",
		stopLossPrice: "Precio de parada",
		overnightMoney: "Tarifa nocturna",
		openTime: "Tiempo de apertura",
		lots: "Mano",
		setProfitLoss: "Establecer parada de interferencia",
		expectedProfit: "Beneficios previstos",
		expectedLoss: "Pérdidas previstas",
		coverAll: "Cierre total",
		onlyCoverMany: "Sólo pingduoshan",
		onlyCoverEmpty: "Solo plano",
		numbers: "Cantidad",
		coinBuyPrice: "Comercio a precios óptimos actuales",
		jine: "Importe",
		p_jine: "Introduzca la cantidad",
		p_price: "Por favor, introduzca el precio",
		p_confirm_sj: "Determinar el precio de mercado",
		p_confirm_xj_1: "Determinar al precio actual",
		p_confirm_ma: "¿Qué?",
		status: "Estado",
		complete: "Completado",
		doing: "En curso",
		currentEntrust: "Delegación actual",
		allEntrust: "Todos los delegados",
		entrust: "Delegación",
		entrustTime: "Tiempo de delegación",
		all: "Todos",
		buyUp: "Comprar más",
		buyDown: "Comprar menos",
		jye:'cantidad de transacción',
	},
	fund: {
		overview: "Panorama general",
		balance: "Saldo",
		futures: "Contrato",
		deposit: "Depósito",
		mining: "minería",
		stock: "Acciones",
		margin: "Super palanca",
		yesterdayEarnings: "Ganancias de ayer",
		yieldAtMaturily: "Ingresos debidos",
		totalkRevenue: "Ingresos totales",
		currentProfit: "Ganancias y pérdidas corrientes",
		profitRatio: "Rentabilidad",
		receive: "Recargar / cobrar",
		transfer: "Retiro / transferencia",
		financial: "Financiación financiera",
		valuation: "Valoración",
		currentEarnings: "Ingresos Corrientes",
		yesterdayIncome: "Ganancias de ayer",
		crypto: "Moneda digital",
		fiat: "Moneda francesa",
		hideSmallCurrency: "Ocultar monedas pequeñas",
		available: "Disponible",
		freeze: "Congelación",
		equivalent: "Conversión",
		usdtm: "Estándar u",
		coinm: "Norma monetaria",
		exchange: "Moneda",
		leverage: "Contrato",
		fiat: "Moneda francesa",
		second: "Opciones",
		convert: "Conversión total de activos",
		convert2: "Conversión de activos",
		record: "Registro de cambios contables",
		availableQuota: "Cantidad disponible",
		locked: "Bloqueo",
		converted: "Conversión",
		account: "Cuenta",
		financialRecords: "Registros financieros",
		number: "Cantidad",
		record2: "Registro",
		time: "Tiempo",
		transfer: "Transferencia de moneda",
		from: "De",
		to: "A",
		transferNumber: "Número de transferencias",
		youGet: "Tendrás",
		exchangeRate: "Tipo de cambio",
		handlingFee: "Gastos de tramitación",
		fundTransfer: "Transferencia de fondos",
		insufficientBalance: "Saldo insuficiente",
		needHandlingFee: "Cargo adicional",
		plsIptCrtNumber: "Introduzca la cantidad correcta",
		c_transfer: "¿Estás seguro de remar?",
		selectCurrency: "Seleccionar moneda",
		DIGICCY: "Moneda digital",
		saveQrcode: "Guardar Código QR",
		copyAddress: "Copiar dirección de enlace",
		plsTrans: "Por favor, transfiera a esta dirección",
		rechargeNumer: "Cantidad de recarga",
		paymentVoucher: "Comprobante de pago",
		plsIptRechargeNumer: "Introduzca el número de recargas",
		plsUploadPaymentVoucher: "Por favor, suba el certificado de pago",
		plsIptCrtAmount: "Introduzca la cantidad correcta",
		withdraw: "Retiro / transferencia",
		currencyName: "Nombre de la moneda",
		transactionInfo: "Cuenta de negociación",
		withdraw2: "Presentación",
		canUse: "Disponible",
		all: "Todos",
		plsIptWithdrawNumber: "Por favor, introduzca la cantidad retirada",
		withdrawNumber: "Cantidad retirada",
		remark: "Observaciones",
		handlingFee: "Gastos de tramitación",
		canGetNumber: "Cantidad recibida",
		leastNumber: "Cantidad mínima de retirada",
		coinType: "Moneda",
		address: "Dirección",
		date: "Fecha",
		withdrawAddress: "Dirección de la moneda",
		plsSelectWithdrawAddress: "Por favor, seleccione la dirección de la moneda",
		withdrawToCard: "Referencia a la tarjeta bancaria",
		withdrawToCardInternation: "International",
		withdrawToAddress: "Mencionar la dirección de la cartera",
		card: "Tarjeta bancaria",
		addCard: "Añadir tarjeta bancaria",
		plsAddCard: "Por favor, agregue una tarjeta bancaria",
		plsInputWithdrawPasswordForWithdraw: 'Por favor ingrese la contraseña de retiro para verificación',
	},
	ieo: {
		subscribe: "Suscripción",
		ing: "En curso",
		done: "Terminado",
		applySubscription: "Solicitud de suscripción",
		remaining: "Remanente",
		lockPeriod: "Período de bloqueo",
		endTime: "Hora final",
		subscribeCurrency: "Moneda de suscripción",
		issuancePrice: "Precio de emisión",
		totalIssuance: "Total de emisiones",
		whitePaper: "Libro Blanco",
		subscriptionCycle: "Ciclo de suscripción",
		warmUp: "Precalentamiento",
		website: "Sitio web oficial",
		finish: "Fin",
		projectDetails: "Detalles del proyecto",
		subscriptionConfirmation: "Confirmación de suscripción",
		p_applicationsNumber: "Introduzca el número de suscripciones",
		needToPay: "Necesidad de pagar",
		currentBalance: "Saldo actual",
		subscriptionTime: "Tiempo de suscripción",
		currency: "Moneda",
		applicationsNumber: "Número de solicitudes",
		passesNumber: "Número de pases",
		timeToMarket: "Tiempo de cotización",
		day: "Días",
		total: "Total",
		subscribed: "Suscripción",
		shengou: "Suscripción",
		insufficientBalance: "Saldo insuficiente",
		p_number: "Por favor, introduzca la cantidad",
		mySubscribe: "Mi suscripción",
	},
	lockming: {
		lockming: "minería",
		fundsUnderCustody: "Fondos en fideicomiso",
		entrustedOrders: "Orden delegada",
		estimatedTodayIncome: "Rendimiento estimado hoy",
		cumulativeIncome: "Ingresos acumulados",
		ordersInCustody: "Orden en custodia",
		lockedPositionsToEarnCoins: "minería",
		lockedPositions: "Silo cerrado",
		minimumSingleTransaction: "Mínimo de una pluma",
		dailyYield: "Rendimiento diario",
		lockUpPeriod: "Período de bloqueo",
		acquisition: "Obtener",
		recentDays: "Recientemente",
		dividendTime: "Tiempo de pago",
		escrowFunds: "Fondos en fideicomiso",
		redemptionInAdvance: "Amortización anticipada",
		estimatedIncome: "Ingresos previstos",
		availableAssets: "Activos disponibles",
		investmentAmountObtained: "Obtención del importe de la inversión",
		all: "Todos",
		l_alert_1: "La minería sigue girando",
		l_alert_2: "El bloqueo de la mina se realiza mediante el depósito de usdt a la máquina de sobrealimentación de la plataforma para obtener ingresos de la mina en la piscina de la Plataforma.",
		features: "Productos destacados",
		onDemand: "Junto con la existencia",
		dividendPeriod: "Ciclo de dividendos",
		issuedDaily: "Distribución diaria",
		currentInterest: "Intereses corrientes",
		feature_1: "Seguridad del 100% de los fondos",
		feature_2: "Ingresos de vacaciones",
		feature_3: "Después del depósito exitoso<br>a partir del mismo día",
		forInstance: "Por ejemplo,",
		incomeCalculation: "Cálculo de los ingresos",
		forInstanceDesc: "Los miembros bloquearon 10.000 u en la Plataforma, eligieron un período de 5 días, Nissan produjo 0,3% - 0,4% de la cantidad de productos financieros bloqueados, la producción diaria es la siguiente:<br/>mínimo: 10.000 u * 0,3% = 30 u<br/>máximo: 10.000 u * 0,4% = 40 u<br/>es decir, después de 5 días puede obtener 150 u ~ 200 u de ingresos, los ingresos se distribuyen diariamente, los ingresos se pueden retirar en cualquier momento, el principal de la cerradura se transfiere automáticamente a su cuenta de capital.",
		aboutLiquidatedDamages: "Sobre los daños y perjuicios liquidados",
		aboutLiquidatedDamagesContent: "Si desea transferir el principal no vencido, se incurrirá en daños y perjuicios liquidados, que = porcentaje de liquidación por defecto * días restantes * Número de posiciones bloqueadas.<br/>example: The default Settlement proportion of the lockstock Mining is 0.4%, the remaining 3 days expires, the number of lockstock is 1000, the default penalty = 0.4% * 3 * 1000 = 12U, the actual principal Return is 1000u - 12U = 988u",
		joinNow: "Voy a asistir.",
		day: "Días",
		daily: "Todos los días",
		returnOnExpiration: "Retorno debido",
		get: "Obtener",
		numberOfCoinsDeposited: "Número de monedas depositadas",
		subscribe: "Suscripción",
		insufficientBalance: "Saldo insuficiente",
		p_number: "Por favor, introduzca la cantidad",
		leastSingle: "Mínimo de una pluma",
		lockedPositionList: "Lista de Silos",
		inProgress: "En curso",
		redeemed: "Redimido",
		expiryTime: "Fecha de vencimiento",
		lockUpTime: "Tiempo de cierre",
		earlyRedemptionPenalty: "Reembolso anticipado de daños y perjuicios",
		redemption: "Redención",
		c_redemption: "¿Seguro de amortización anticipada?",
	},
	invest: {
		invest: "Gestión financiera",
		section: "Zona",
		perPrice: "Precio unitario",
		apr: "Rendimiento anualizado",
		linkedReferencePrice: "Precio de referencia del gancho",
		holdingDays: "Duración de la posición",
		maturityDate: "Fecha de vencimiento",
		remainingShares: "Participación residual",
		progress: "Progreso actual",
		action: "Operaciones",
		purchase: "Comprar",
		myPosition: "Mis Finanzas",
		buyShares: "Comprar acciones",
		estimatedIncome: "Ingresos previstos",
		day: "Días",
		share: "Copy",
		fail: "Fracaso",
		purchased: "Comprado",
		settlementing: "Liquidación",
		success: "Completado",
		all: "Todos",
		name: "Nombre",
		remain: "Remanente",
		instruction: "Descripción del producto",
		help: "Ayuda",
		shuoming_1: "1. Este producto es un producto financiero no garantizado. Las fluctuaciones del mercado pueden causar pérdidas de capital. Por favor, tenga cuidado con la inversión.",
		shuoming_2: "2. Normas para la liquidación de la recaudación de fondos:",
		shuoming_3: 'Si el precio de liquidación es inferior al precio vinculado, el BTC se liquidará al vencimiento; Cantidad de liquidación = cantidad de inversión * (1 + días * rendimiento anualizado / 365).',
		shuoming_4: 'Si el precio de liquidación es ≥ precio vinculado, el usdt se liquidará después de su vencimiento; Cantidad liquidada = cantidad de inversión * precio vinculado * (1 + días * rendimiento anualizado / 365).',
		shuoming_5: "3. La tasa de rendimiento anualizada real varía con el mercado en tiempo real. Por favor, tenga en cuenta la compra real y la transacción.",
		shuoming_6: "4. El importe de la inversión se convertirá con el mercado en tiempo real. Por favor, tenga en cuenta la compra real y la transacción.",
		shuoming_7: "5. Después de la compra de los productos se puede ver en mi página de tenencia de moneda, después de la fecha de vencimiento, el pago se entregará automáticamente a la cuenta al contado.",
		error_1: "Por favor, introduzca la cantidad de compra",
		error_2: "La cantidad de compra debe ser un número entero",
		confirmPurchase: "¿Estás seguro de comprar este producto financiero?",
		purchaseSuccess: "¡Compra exitosa!",
	},
	follow: {
		follow: "Merchandiser",
		traderList: "Lista de comerciantes",
		totalProfit: "Ganancias y pérdidas totales",
		correctRate: "Precisión total",
		positionValuation: "Valoración de las posiciones",
		estimatedProfitToday: "Ingresos previstos hoy",
		rateOfReturn: "Tasa de rendimiento",
		becomeATrader: "Convertirse en comerciante",
		traderList: "Lista de comerciantes",
		onlyShowsThePosition: "Mostrar sólo posición",
		theDataIsUpdatedEveryHour: "Los datos se actualizan cada hora",
		following: "Merchandiser",
		follow: "Merchandiser",
		confirmFollow: "¿Estás seguro de seguir?",
		confirmCancel: "¿Estás seguro de cancelar el seguimiento?",
		followTrader: "Seguir a los comerciantes",
		editCopy: "Editar siguiente",
		beforeCopy: "Lea cuidadosamente antes de elegir seguir",
		copyAgreement: "Protocolo de seguimiento",
		copyType: "Modo de seguimiento",
		copyType1: "Seguimiento de múltiplos fijos",
		copyType2: "Número fijo de manos seguido",
		copyAlert1: "No importa cuántas órdenes haga el comerciante, usted sigue un múltiplo fijo de su elección.",
		copyAlert2: "(cuando el número de manos proporcionales sea > 1 y no sea un entero, se redondeará para mantener el valor de dos decimales)",
		copyMultiple: "Follow multiple",
		cancelCopy: "Cancelar seguimiento",
		currentFollowerNumber: "Número actual de seguidores",
		totalProfitAndLoss: "Ganancias y pérdidas totales",
		totalReturn: "Rendimiento total",
		accuracy: "Tasa de precisión",
		profitableOrders: "Orden de beneficios",
		successfulShortTrades: "Venta en corto exitosa",
		successfulLongTrades: "Hacer tratos largos con éxito",
		yesterdaysTradingStatus: "Situación comercial de ayer",
		currentPositions: "Posición actual",
		totalOrders: "Orden total",
		remain: "Remanente",
		distanceDue: "Vencimiento de la distancia",
		days: "Días",
		deadline: "Duración",
		subscription: "Suscripción",
		followUsers: "Seguir al usuario",
		followAmount: "Siguiente importe",
		followEarnings: "Ingresos siguientes",
		cancelFollow: "Cancelar seguimiento",
		currentCopy: "Merchandiser actual",
		historicalCopy: "Transacciones históricas",
		myTrader: "Mi comerciante",
		tradingPair: "Par de transacciones",
		open: "Precio de apertura",
		close: "Precio de cierre",
		earnings: "Ingresos",
		lots: "Número de manos",
		time: "Tiempo de negociación",
		buyIn: "Comprar",
		buyOut: "Vender",
		direction: "Dirección",
	},
	financial: {
		product: "Productos",
		position: "Hold",
		financial: "Finanzas",
		lockming: "minería",
		ieo: "IEO",
		invest: "Invertir",
		section: "Zona",
		apr: "Rendimiento anualizado",
		linkedReferencePrice: "Precio de referencia del gancho",
		holdingDays: "Duración de la posición",
		day: "Días",
		lockPeriod: "Período de bloqueo",
		ing: "En curso",
		done: "Terminado",
		applySubscription: "Solicitud de suscripción",
		remaining: "Remanente",
		total: "Total",
		subscribed: "Suscripción",
		mining: "minería",
		minimum: "Levántate.",
		dailyReturnRate: "Rendimiento diario",
	},
	nft: {
		nft: 'NFT',
		artwork: "Arte",
		artist: "Artista",
		artworkList: "Lista de obras de arte",
		artistList: "Lista de artistas",
		comprehensiveSorting: "Ordenación integrada",
		newest: "Último",
		hotest: "Más caliente",
		mostWorks: "La mayoría de las obras",
		mostPopular: "Más popular",
		record: "Registro de compras",
		recommendArtist: "Artistas recomendados",
		buy: "Comprar",
		artistHomepage: "Página principal del artista",
		works: "Obras",
		saled: "Vendido",
		fans: "Fan",
		allWorks: "Todas las obras",
		notSell: "Sin vender",
		myCollect: "Mi colección",
		collect: "Coleccionable",
		own: "Hold",
		placeABid: "Oferta",
		purchase: "Comprar",
		open: "Apertura posterior",
		image: "Imagen",
		gif: "Gráfico dinámico",
		audio: "Audio",
		video: "Video",
		creator: "Autor",
		currentBid: "La última oferta",
		price: "Precio",
		d: "Días",
		h: "Tiempo",
		m: "Puntos",
		s: "Segundos",
		saled: "Terminado",
		place_alert: "Artículo no vendido o terminado",
		createdBy: "Autor",
		place_alert2: "Su oferta debe ser mayor que",
		note: "Importante",
		CURRENT_BID: "La última oferta",
		place_alert3: "Para participar en una subasta, usted tiene que pagar",
		place_alert4: "El depósito se devolverá a su cuenta al final de la subasta.",
		place_alert6: "Después de la fecha límite de la oferta, si su oferta es exitosa, tendrá un día para pagar el precio final. Si el pago se retrasa, se deducirá su margen.",
		confirmBid: "Confirmación de la oferta",
		DESCRIPTION: "Descripción",
		ADDRESS: "Dirección",
		copy: "Copiar",
		per_increase: "Cada aumento de precio",
		currency_type: "Tipo de moneda",
		pay_type: "Método de compra",
		pay_type_2: "Modo de venta",
		normal: "General",
		auction: "Licitación",
		confirmAuction: "¿Estás seguro de participar en la subasta de este producto?",
		bindBox: "Caja ciega",
		sell_status: "Estado de venta",
		hasStart: "Ha comenzado",
		willStart: "No empezar",
		artwork_type: "Tipo de artículo",
		reset: "Reset",
		confirm: "Determinar",
		place_alert5: "Por favor, introduzca la palabra clave",
		collection: "Colección",
		byCollection: "Ser recogido",
		artworks: "Obras",
		goCollect: "Ir a la colección",
		price: "Precio",
		nonickname: "Apodo no establecido",
		cjdjs: "Plazo de presentación de ofertas",
		cjkssj: "Hora de inicio de la oferta",
		cjjzsj: "Plazo de presentación de ofertas",
		gotosee: "Ve al mercado.",
		position: "Hold",
		resell: "Revender",
		buyPrice: "Precio de compra",
		confirmResell: "Confirmación de la reventa",
		wrongPrice: "Por favor, elija el precio correcto",
		bidPlacedBy: "Ofertante",
		placeRecord: "Registro de ofertas",
		confirPurchase: "Confirmar la compra",
		artistList: "Lista de artistas",
		hasOpen: "Abierto",
		noOpen: "No abierto",
		clickOpen: "Abrir caja ciega",
		confirmOpen: "¿Está seguro de abrir la Caja ciega?",
		margin: "Margen",
		wrongPrice: "Introduzca el precio correcto",
		wrongPerIncrease: "Introduzca la tarifa correcta",
		wrongTime: "La hora final debe ser posterior a la hora final",
		confirmResell2: "¿Una vez que la mercancía se revende, no se puede retirar. Está seguro de que desea revender la mercancía?",
		reselling: "En reventa",
		message: "Notificación de mensajes",
		confirmPay: "¿Está seguro de pagar por este artículo?",
		zfdjs: "Cuenta atrás de pagos",
		payTime: "Tiempo de pago",
		hasRead: "Leído",
		lastPrice: "Precio final de transacción",
		isPay: "Pagado",
		isExpired: "Caducado",
		rarity: "Rareza",
	}
}

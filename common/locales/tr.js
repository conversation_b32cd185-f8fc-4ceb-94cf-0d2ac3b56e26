export default {
	nav: ['<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Finans', 'Varlıklar'],
	common: {
		search: 'Ara',
		confirm: "<PERSON><PERSON>",
		cancel: "<PERSON><PERSON><PERSON>",
		submit: "<PERSON><PERSON><PERSON>",
		logout: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		all: "<PERSON>ü<PERSON><PERSON>",
		edit: "<PERSON><PERSON><PERSON><PERSON>",
		delete: "Sil",
		reset: "<PERSON>ı<PERSON><PERSON>rl<PERSON>",
		filter: "<PERSON>ltrel<PERSON>",
		login: "<PERSON><PERSON>ş",
		register: "Kay<PERSON>t",
		email: "E-posta",
		mobile: "Cep telefonu numarası",
		account: "Hesa<PERSON>",
		password: "<PERSON><PERSON><PERSON>",
		passwordPlaceholder: "Lütfen şifrenizi girin",
		noaccount: "Hesabınız yok mu?",
		go: "Git",
		forgetPassword: "Şifremi Unuttum",
		confirmPassword: "Ş<PERSON>reyi Onayla",
		invitecode: "Davet Kodu",
		hasaccount: "Zaten hesabınız var mı?",
		mobilecodePlaceholder: "Lütfen cep telefonu doğrulama kodunu girin",
		emailcodePlaceholder: "Lütfen e-posta doğrulama kodunu girin",
		send: "Doğrulama kodu gönder",
		selectArea: "Bölge seçimi",
		audit: "Denetim",
		underAudit: "İnceleme altında",
		auditFailure: "Denetim başarısız",
		untie: "Çöz",
		buy: "Satın al",
		sell: "Sat",
		plsLogin: "Lütfen önce giriş yapın",
		plsInputUsername: "Lütfen hesap girin",
		plsInputRePassword: "Lütfen onay şifresini girin",
		plsInputInviteCode: "Lütfen davet kodunu girin",
		plsInputCode: "Lütfen doğrulama kodunu girin",
		plsInputMobile: "Lütfen cep telefonu numarasını girin",
		plsInputEmail: "Lütfen e-postanızı girin",
		login_success: "Giriş başarılı",
		login_fail: "Giriş başarısız",
		reg_success: "Kayıt başarılı",
		reg_fail: "Giriş başarısız",
		pwdInconsistent: "İki şifre tutarsız",
		hint: "İpucu",
		loading: 'Yükleniyor',
		success: "Başarılı",
		plsInput: "Lütfen girin",
		click: "Tıkla",
		quotation: "Fiyat",
		edit: "Düzenle",
		second: 'Saniye',
		specialChart: 'Özel karakterlere izin verilmez',
		open: 'Açılış fiyatı',
		close: 'Kapanış fiyatı',
		high: 'En yüksek fiyat',
		low: 'En düşük fiyat',
		customerService: 'Müşteri hizmetleri ile iletişime geçin',
		rememberPassword: 'Şifreyi hatırla',
		newPassword: 'Yeni şifre',
		pwdMoreThen6: 'Şifre uzunluğu altı haneden fazla olmalıdır',
		codeLength6: 'Doğrulama kodu uzunluğu altı hane olmalıdır',
		rePwdMoreThen6: 'Onay şifresi uzunluğu altı haneden fazla olmalıdır',
		pls: 'Lütfen',
		saveSuccess: "Sistem albümüne kaydedildi",
		nextStep: "Sonraki",
		log: 'Kayıt',
		paramsWrong: "Parametre hatası",
		functionLoading: "Fonksiyon geliştiriliyor",
		DIGICCY: "Dijital Para",
		quickly: "Hızlı",
		more: "Daha fazla",
		hasNoData: 'Daha fazla veri yok',
		audioError: 'Ses dosyası hatası',
		toLong: 'Karakter çok uzun',
		selectLang: "Dil seçin",
	},
	home: {
		contactUs: 'Bize Ulaşın',
		borrowing: 'Borçlanma',
		borrowing_entry: 'Kredi girişi',
		repayment_entry: 'Kredi geri ödeme girişi',
		title: 'Tebbit BTG',
		totalAssetsEquivaleng: 'Toplam varlık eşdeğeri',
		recharge: 'Yeniden yükle',
		withdraw: 'Para çekme',
		stock: 'Hisse senedi',
		futures: 'Kontrat',
		copytrade: 'Kopyala',
		financial: 'Finansal yönetim',
		margin: 'Süper Kaldıraç',
		otc: 'OTC',
		coupon: 'Kupon Merkezi',
		bills: 'Faturalar',
		exchange: 'Borsa',
		mining: 'Kilitleyerek coin kazan',
		game: 'Mini oyun',
		chatroom: 'Sohbet odası',
		quicklyBuyCoins: 'Hızlı Coin Satın Al',
		defiMining: 'DeFi Madenciliği',
		cloudMining: 'Bulut Madencisi',
		stock: 'Hisse senedi',
		stock_info: 'Pozisyon açmaya gerek yok, hemen pozisyon tut',
		lockming: 'Kilitli madencilik',
		transfer: "Coin transferi",
		ieo: "IEO",
		tradeQuery: "İşlem Sorgulama",
		seconds: "Opsiyonlar",
		day: "Gün",
		minimum: 'Başlat',
		dailyReturnRate: "Günlük getiri oranı",
		more: "Daha fazla",
		market: "Fiyat",
		tradingPair: 'İşlem çifti',
		lastPrice: 'Son Fiyat',
		todayChange: 'Bugünkü değişim',
		transaction: 'İşlem',
		nft: 'NFT',
		invest: "Yatırım",
		download: 'İndir',
		app_text: 'Her zaman, her yerde, çevrimiçi işlem',
	},
	setting: {
		my_qrcode: 'QR kodum',
		mine: 'Benimki',
		inviteDesc: 'Arkadaşları davet et, birlikte indirim al',
		inviteDesc2: 'Arkadaşlarla indirimlerden yararlan',
		inviteNumber: 'Davet sayısı',
		inviteTransNumber: 'İşlem yapan kişi sayısı',
		myRebate: 'İndirimim',
		bill: "Fatura",
		wallet: "Cüzdan adresi",
		banks: "Banka Kartı Bağlama",
		banksInternational: "Banka Kartı Bağlama - Uluslararası",
		addBank: "Banka kartı ekle",
		securitySettings: "Güvenlik Ayarları",
		coupon: "Kupon Merkezi",
		systemNotification: "Sistem Bildirimi",
		defaultFiatCurrency: "Varsayılan Fiat Para Birimi",
		language: "Dil",
		faq: "Sık Sorulan Sorular",
		operationalCompliance: "Operasyonel Uyumluluk",
		share: "Paylaş",
		contactUs: "Bize Ulaşın",
		aboutUs: "Hakkımızda",
		avatar: 'Avatar',
		nickname: 'Takma ad',
		signature: 'Kişisel imza',
		grxx: "Kişisel bilgiler",
		grxxOptions: {
			nickname: {
				title: 'Takma ad',
				placeholder: 'Lütfen takma ad girin',
				desc: '2-10 karakter, Çince ve İngilizce, sayıları destekler',
			},
			signature: {
				title: 'Kişisel imza',
				placeholder: 'Lütfen kişiselleştirilmiş imza girin',
				desc: '2-30 karakter, Çince ve İngilizce, sayıları destekler',
			}
		},
		xxtx: "Avatar Yükle",
		photograph: 'Fotoğraf çek',
		selectFromAlbum: 'Albümden seç',
		aqsz: "Güvenlik Ayarları",
		editLoginPassword: 'Giriş şifresini değiştir',
		setPayPassword: "Ödeme şifresi belirle",
		editPayPassword: "Ödeme şifresini değiştir",
		bindPhone: "Telefon bağla",
		email: 'E-posta',
		stayLogin: 'Giriş yapılı kal',
		xgdlmm: "Giriş şifresini değiştir",
		currentPassword: 'Mevcut giriş şifresi',
		currentPasswordPlaceholder: 'Lütfen mevcut giriş şifresini girin',
		newPassword: "Yeni giriş şifresi",
		newPasswordPlaceholder: "Lütfen yeni giriş şifresi girin",
		confirmNewPassword: "Yeni şifreyi onayla",
		confirmNewPasswordPlaceholder: "Lütfen onaylamak için yeni şifre girin",
		currentPayPassword: "Mevcut Ödeme Şifresi",
		currentPayPasswordPlaceholder: 'Lütfen mevcut ödeme şifresini girin',
		newPayPassword: "Yeni Ödeme Şifresi",
		newPayPasswordPlaceholder: "Lütfen yeni ödeme şifresi girin",
		confirmNewPayPassword: "Yeni ödeme şifresini onayla",
		confirmNewPayPasswordPlaceholder: "Lütfen onay yeni ödeme şifresini girin",
		phoneVerificationCode: 'Telefon doğrulama kodu',
		editPhoneVerificationCode: "Lütfen telefon doğrulama kodunu girin",
		getVerificationCode: 'Doğrulama kodu al',
		lctyq: "Finansal Yönetim Deneyim Kuponu",
		expired: "Süresi dolmuş",
		expiredTime: "Son Kullanma Tarihi",
		useImmediately: "Hemen Kullan",
		addNewAccount: "Yeni hesap ekle",
		totayIncome: "Bugünkü tahsilat",
		totalIncome: "Birikmiş tahsilatlar",
		selectLang: "Lütfen dil seçin",
		selectCoinType: "Lütfen para birimi seçin",
		authentication: 'Kimlik Doğrulama',
		authentication1: 'Temel kimlik doğrulama',
		authentication2: 'Gelişmiş Kimlik Doğrulama',
		authentication_text_1: 'Kişisel kimlik doğrulaması',
		authentication_text_2: 'Kimlik doğrulamadan sonra kullanılabilir izinler:',
		authentication_text_3: 'Lv1.Temel Kimlik Doğrulama',
		authentication_text_4: 'Kimlik doğrulamadan sonra yansıtılabilir, 24 saatlik limit 200BTC',
		authentication_text_5: 'Kimlik doğrulamadan sonra fiat para işlemleri yapılabilir, tek limit 2000USDT',
		authentication_text_6: 'Lv2. Gelişmiş Kimlik Doğrulama',
		authentication_text_7: 'Kredi limitini artır, 24 saatlik limit 500BTC',
		authentication_text_8: 'Fiat para işlem limitini artır, tek limit 1000000USDT',
		authentication_text_9: 'Hatırlatma: Kullanıcı fonlarının güvenliğini korumak için lütfen önce e-posta ve telefon bağlamayı tamamlayın, aksi takdirde kimlik doğrulamanız başarısız olacaktır. ',
		goAudit: 'Kimlik doğrulamaya git',
		noaudit: 'Doğrulanmamış',
		auditing: 'Doğrulanıyor',
		hasaudit: 'Doğrulandı',
		idType: 'Kimlik Türü',
		idCard: 'Kimlik Kartı',
		passport: 'Pasaport',
		driverLicense: 'Ehliyet',
		name: 'İsim',
		idcard: 'Kimlik numarası',
		plsIptName: 'Lütfen isim girin',
		plsIptCorrectIdcard: 'Lütfen doğru kimlik numarasını girin',
		uploadIdcardFront: 'Kimlik kartı ön yüzünü yükle',
		uploadIdcardReverse: 'Kimlik kartının arkasını yükle',
		emailVerificationCode: 'E-posta Doğrulama Kodu',
		plsiptEmailCode: 'Lütfen e-posta doğrulama kodunu girin',
		addWallet: 'Cüzdan adresi ekle',
		selectCurrency: 'Para birimi seç',
		plsSelectCurrency: 'Lütfen para birimi seçin',
		plsIptAddress: 'Lütfen cüzdan adresini girin',
		walletAddress: 'Cüzdan adresi',
		walletQrcode: 'Cüzdan QR kodu',
		plsIptWalletAddress: 'Lütfen cüzdan adresini girin',
		plsUploadWalletQrcode: 'Lütfen cüzdan QR kodunu yükleyin',
		confirmLogout: 'Çıkış yapmak istediğinizden emin misiniz? ',
		nonickname: 'Lütfen takma ad belirleyin',
		plsInputMobile: "Lütfen cep telefonu numarasını girin",
		alipay_account: 'ABA numarası',
		real_name: 'Gerçek isim',
		bank_account: 'Banka kart numarası',
		bank_dizhi: 'Banka adresi',
		bank_name: 'Hesap açan banka',
		bank_network: 'Banka ağı',
		swift_code: 'Swift',
		phone: 'Telefon',
		wechat_account: 'Kart sahibi adresi',
		wechat_nickname: 'SWIFT kodu',
		allNeed: 'Hepsi gerekli',
		phoneNumber: "Cep telefonu numarası",
		code: "Doğrulama Kodu",
		sendCode: "Doğrulama kodu gönder",
		plsIptCode: "Lütfen doğrulama kodunu girin",
		withdrawPassword: 'Para Çekme Şifresi',
		confirmWithdrawPassword: 'Para Çekme Şifresini Onayla',
		plsInputWithdrawPassword: 'Lütfen para çekme şifresini girin',
		plsInputConfirmWithdrawPassword: 'Lütfen para çekme şifresini onaylayın',
		withdrawPasswordPlaceholder: 'Lütfen 6-16 haneli para çekme şifresi girin',
		confirmWithdrawPasswordPlaceholder: 'Para çekme şifresini tekrar girin',
		updateWithdrawPassword: 'Para Çekme Şifresini Güncelle',
		oldWithdrawPassword: 'Eski Para Çekme Şifresi',
		newWithdrawPassword: 'Yeni Para Çekme Şifresi',
		plsInputOldWithdrawPassword: 'Lütfen eski para çekme şifresini girin',
		plsInputNewWithdrawPassword: 'Lütfen yeni para çekme şifresini girin',
		withdrawPasswordNotMatch: 'Para çekme şifreleri eşleşmiyor',
		withdrawPasswordLengthError: 'Para çekme şifresi uzunluğu 6-16 karakter olmalıdır',
		withdrawPasswordRequired: 'Lütfen para çekme şifresi belirleyin',
		withdrawPasswordVerifyFailed: 'Para çekme şifresi doğrulaması başarısız',
		needRealAuth: "İşlem yapmadan önce kimlik doğrulaması tamamlanmalıdır",
		realAuthPending: "Kimlik doğrulaması inceleniyor, lütfen incelemenin tamamlanmasını bekleyin",
		score: "Puan"
	},
	transaction: {
		actualPL: 'Gerçek K/Z',
		favorites: "İsteğe bağlı",
		futures: "Kontrat",
		coins: "Coinler",
		stock: "Hisse senedi",
		tradingPair: 'İşlem çifti',
		lastPrice: 'Son Fiyat',
		todayChange: 'Bugünkü değişim',
		searchPlaceholder: 'İlgilendiğiniz para birimi/hisse senedi adı/sembolü arayın',
		hotSearch: 'Popüler Arama',
		recentView: 'Son görüntülenen',
		dayHigh: "Bugünün En Yükseği",
		dayLow: "Bugünün en düşüğü",
		optional: 'İsteğe bağlı',
		buy: 'Satın al',
		sell: 'Sat',
		entrustPendingOrder: 'Bekleyen emir emanet et',
		addOptionalSuccess: 'İsteğe bağlı ekleme başarılı',
		delOptionalSuccess: 'İsteğe bağlı silme başarılı',
		seconds: "Opsiyonlar",
		home: "Ana Sayfa",
		orderConfirm: "Emir Onayı",
		currentPrice: 'Mevcut fiyat',
		direction: "Yön",
		selectTime: "Son kullanma tarihini seçin",
		number: "Sayı",
		balance: 'Hesap bakiyesi',
		plsIptCrtNumber: 'Lütfen doğru sayıyı girin',
		buyPrice: 'Satın alma fiyatı',
		expectedPL: "Beklenen kar ve zarar",
		continueTrade: "İşleme devam et",
		secondsPosition: 'Opsiyon pozisyonu',
		position: 'Tutma',
		history: 'Geçmiş',
		orderTimes: 'Emir süresi',
		pl: "Kar ve zarar",
		sellTime: "Satış Zamanı",
		buy: 'Satın al',
		sell: 'Sat',
		price: 'Fiyat',
		time: "Zaman",
		optional: 'İsteğe bağlı',
		buy: 'Satın al',
		sell: 'Sat',
		long: "Daha fazla yap",
		short: "Kısa",
		entrustPendingOrder: 'Bekleyen emir emanet et',
		addOptionalSuccess: 'İsteğe bağlı ekleme başarılı',
		delOptionalSuccess: 'İsteğe bağlı silme başarılı',
		price: "Fiyat",
		amount: "Miktar",
		marketPrice: "Piyasa Fiyatı",
		limitPrice: "Limit Fiyatı",
		number: "İşlem yapılacak lot sayısı",
		margin: "Marj",
		handlingFee: "İşlem ücreti",
		balance: "Bakiye",
		multiple: "Çoklu",
		recharge: 'Yeniden yükle',
		plsIptCommissionPrice: 'Lütfen komisyon fiyatını girin',
		tradingPair: "İşlem Çifti",
		direction: "Yön",
		plsIptCrtPrice: 'Lütfen doğru fiyatı girin',
		position: "Pozisyon",
		time: "Zaman",
		turnover: "İşlem hacmi",
		operation: "İşlem",
		type: "Tür",
		cover: 'Pozisyonu kapat',
		confirmCover: 'Pozisyonu kapatmayı onaylıyor musunuz? ',
		confirmSelfHold: 'Kendi kendine tutmaya geçmek istediğinizden emin misiniz? ',
		selfHolding: 'Kendi kendine tutma',
		transSelfHold: 'Kendi kendine tutmaya çevir',
		delegateList: "Temsilci Listesi",
		riskRate: "Risk Oranı",
		totalPandL: 'Pozisyonun toplam karı',
		oneClickCover: "Tek tıkla pozisyon kapat",
		open: "Pozisyon açma fiyatı",
		targetProfitPrice: "Kar Al Fiyatı",
		updatePrice: "Mevcut Fiyat",
		stopLossPrice: "Zarar Durdur Fiyatı",
		overnightMoney: "Gecelik Ücret",
		openTime: "Açılış zamanı",
		lots: "El",
		setProfitLoss: "Kar Al ve Zarar Durdur Ayarla",
		expectedProfit: "Beklenen Kar",
		expectedLoss: "Beklenen zarar",
		coverAll: "Tüm pozisyonları kapat",
		onlyCoverMany: "Sadece uzun emirleri kapat",
		onlyCoverEmpty: "Sadece boş emirleri kapat",
		numbers: 'Sayı',
		coinBuyPrice: 'Mevcut en iyi fiyattan işlem yap',
		jine: 'Miktar',
		p_jine: 'Lütfen miktarı girin',
		p_price: 'Lütfen fiyatı girin',
		p_confirm_sj: 'Piyasa fiyatından onayla',
		p_confirm_xj_1: 'Mevcut fiyattan onayla',
		p_confirm_ma: '? ',
		status: "Durum",
		complete: "Tamamlandı",
		doing: "Devam ediyor",
		currentEntrust: "Mevcut Emanet",
		allEntrust: "Tüm Emanet",
		entrust: "Emanet",
		entrustTime: "Emanet Zamanı",
		all: "Tümü",
		buyUp: "Daha fazla satın al",
		buyDown: "Daha az satın al",
		jye: 'İşlem Miktarı',
	},
	fund: {
		overview: 'Genel Bakış',
		balance: 'Bakiye',
		futures: 'Kontrat',
		deposit: 'Para Yatırma',
		mining: 'Kilitleyerek coin kazan',
		stock: 'Hisse senedi',
		margin: 'Süper Kaldıraç',
		yesterdayEarnings: 'Dünkü kazançlar',
		yieldAtMaturily: 'Vade sonunda getiri',
		totalkRevenue: 'Toplam gelir',
		currentProfit: 'Mevcut kar ve zarar',
		profitRatio: 'Kar oranı',
		receive: 'Yeniden yükle/para al',
		transfer: 'Para çekme/Transfer',
		financial: 'Finansal yönetim',
		valuation: 'Değerleme',
		currentEarnings: 'Mevcut kazançlar',
		yesterdayIncome: 'Dünkü gelir',
		crypto: 'Dijital para',
		fiat: 'Fiat para',
		hideSmallCurrency: 'Küçük Para Birimini Gizle',
		available: 'Mevcut',
		freeze: 'Dondur',
		equivalent: 'Eşdeğer',
		usdtm: 'U standardı',
		coinm: 'Coin standardı',
		exchange: "Coin",
		leverage: 'Kontrat',
		fiat: 'Fiat para',
		second: 'Opsiyon',
		convert: "Toplam varlıkları dönüştür",
		convert2: "Varlık Dönüştürme",
		record: "Hesap değişiklik kaydı",
		availableQuota: "Mevcut Kota",
		locked: "Kilitli",
		converted: "Dönüştürülmüş",
		account: "Hesap",
		financialRecords: "Finansal Kayıtlar",
		number: "Sayı",
		record2: "Kayıt",
		time: "Zaman",
		transfer: "Coin Transferi",
		from: "Kimden",
		to: "Kime",
		transferNumber: "Transfer Sayısı",
		youGet: "Alacağınız",
		exchangeRate: "Döviz Kuru",
		handlingFee: "İşlem ücreti",
		fundTransfer: "Fon Transferi",
		insufficientBalance: "Yetersiz bakiye",
		needHandlingFee: "Ek işlem ücreti",
		plsIptCrtNumber: 'Lütfen doğru sayıyı girin',
		c_transfer: "Transfer etmek istediğinizden emin misiniz?",
		selectCurrency: "Para Birimi Seç",
		DIGICCY: 'Dijital Para',
		saveQrcode: 'QR kodunu kaydet',
		copyAddress: 'Bağlantı adresini kopyala',
		plsTrans: 'Lütfen bu adrese transfer yapın',
		rechargeNumer: "Yeniden yükleme sayısı",
		paymentVoucher: "Ödeme Fişi",
		plsIptRechargeNumer: "Lütfen yeniden yükleme miktarını girin",
		plsUploadPaymentVoucher: "Lütfen ödeme fişini yükleyin",
		plsIptCrtAmount: 'Lütfen doğru miktarı girin',
		withdraw: "Para çekme/Transfer",
		currencyName: "Para birimi adı",
		transactionInfo: "İşlem Hesabı",
		withdraw2: 'Para çekme',
		canUse: 'Mevcut',
		all: 'Tümü',
		plsIptWithdrawNumber: 'Lütfen para çekme miktarını girin',
		withdrawNumber: 'Para çekme miktarı',
		remark: 'Not',
		handlingFee: 'İşlem',
		canGetNumber: 'Alınan hesap sayısı',
		leastNumber: 'Minimum para çekme miktarı',
		coinType: 'Para birimi',
		address: 'Adres',
		date: 'Tarih',
		withdrawAddress: 'Para çekme adresi',
		plsSelectWithdrawAddress: 'Lütfen para çekme adresini seçin',
		withdrawToCard: 'Banka kartına çek',
		withdrawToCardInternational: 'Uluslararası',
		withdrawToAddress: 'Cüzdan adresine çek',
		card: 'Banka kartı',
		addCard: 'Banka kartı ekle',
		plsAddCard: 'Lütfen banka kartı ekleyin',
		plsInputWithdrawPasswordForWithdraw: 'Lütfen doğrulama için para çekme şifresini girin'
	},
	tradeQuery: {
		title: "İşlem Sorgulama",
		tradingPair: "İşlem Çifti",
		direction: "İşlem Yönü",
		betSeconds: "Bahis Süresi",
		betAmount: "Bahis Miktarı",
		currentStatus: "Mevcut Durum",
		buyUp: "Yükseliş",
		buyDown: "Düşüş",
		seconds: "saniye",
		profit: "Kar",
		loss: "Zarar",
		draw: "Berabere",
		trading: "İşlem Devam Ediyor",
		noData: "Veri bulunamadı",
		loading: "Yükleniyor...",
		refresh: "Yenile"
	},
	contact: {
		contactUs: 'Bize Ulaşın'
	}
}

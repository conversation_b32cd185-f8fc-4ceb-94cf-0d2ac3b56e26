export default {
	nav:['首页','行情','交易','金融','资产'],
	common: {
		search: '搜索',
		confirm: "确定",
		cancel: "取 消",
		submit: "提 交",
		logout: '退 出',
		all: "全 部",
		edit: "编 辑",
		delete: "删 除",
		reset: "重 置",
		filter: "筛 选",
		login: "登 录",
		register: "注 册",
		email: "邮箱",
		mobile: "手机号",
		account: "账号",
		password: "密码",
		passwordPlaceholder: "请输入密码",
		noaccount: "没有账号？",
		go: "去",
		forgetPassword: "忘记密码",
		confirmPassword: "确认密码",
		invitecode: "邀请码",
		hasaccount: "已有账号？",
		mobilecodePlaceholder: "请输入手机验证码",
		emailcodePlaceholder: "请输入邮箱验证码",
		send: "发送验证码",
		selectArea: "地区选择",
		audit: "审核",
		underAudit: "审核中",
		auditFailure: "审核失败",
		untie: "解绑",
		buy: "买",
		sell: "卖",
		plsLogin: "请先登录",
		plsInputUsername: "请输入账户",
		plsInputRePassword: "请输入确认密码",
		plsInputInviteCode: "请输入邀请码",
		plsInputCode: "请输入验证码",
		plsInputMobile: "请输入手机号",
		plsInputEmail: "请输入邮箱",
		login_success: "登录成功",
		login_fail: "登录失败",
		reg_success: "注册成功",
		reg_fail: "登录失败",
		pwdInconsistent: "两次密码不一致",
		hint: "提示",
		loading: '加载中',
		success: "成功",
		plsInput: "请输入",
		click: "点击",
		quotation: "行情",
		edit: "编辑",
		second: '秒',
		specialChart: '不允许包含特殊字符',
		open: '开盘价',
		close: '收盘价',
		high: '最高价',
		low: '最低价',
		customerService: '联系客服',
		rememberPassword: '记住密码',
		newPassword: '新密码',
		pwdMoreThen6: '密码的长度应大于六位',
		codeLength6: '验证码长度应为六位',
		rePwdMoreThen6: '确认密码的长度应大于六位',
		pls:'请',
		saveSuccess:"已保存至系统相册",
		nextStep:"下一步",
		log:'记录',
		paramsWrong:"参数错误",
		functionLoading:"功能开发中",
		DIGICCY:"数字货币",
		quickly:"快速",
		more:"更多",
		hasNoData:'没有更多数据了',
		audioError:'音频文件出错',
		toLong:'字符过长',
		selectLang:'选择语言'
	},
	home:{
		contactUs: '在线客服',
		borrowing: '借贷',
		borrowing_entry: '借贷入口',
		repayment_entry: '还贷入口',
		title:'Tebbit BTG',
		totalAssetsEquivaleng:'总资产折合',
		recharge:'充值',
		withdraw:'提现',
		stock:'股票',
		futures:'合约',
		copytrade:'跟单',
		financial:'金融理财',
		margin:'超级杠杆',
		otc:'OTC',
		coupon:'卡券中心',
		bills:'账单',
		exchange:'汇兑',
		mining:'锁仓赚币',
		game:'小游戏',
		chatroom:'聊天室',
		quicklyBuyCoins:'快捷买币',
		defiMining:'DeFi挖矿',
		cloudMining:'云矿机',
		stock:'股票',
		stock_info:'无需开仓，立即持仓',
		lockming:'锁仓挖矿',
		transfer:"币币划转",
		ieo:"IEO",
		tradeQuery:"交易查询",
		seconds:"期权",
		day:"天",
		minimum:'起',
		dailyReturnRate:"日收益率",
		more:"更多",
		market:"行情",
		tradingPair:'交易对',
		lastPrice:'最新价',
		todayChange:'今日涨跌幅',
		transaction:'交易',
		nft:'NFT',
		invest:"投资理财",
		download:'下载',
		app_text:'随时随地，在线交易'
	},
	setting:{
		my_qrcode: '我的二维码',
		mine: '我的',
		inviteDesc:'邀请好友，共同返利',
		inviteDesc2:'与好友共同享受返利',
		inviteNumber:'邀请人数',
		inviteTransNumber:'已交易人数',
		myRebate:'我的返利',
		bill:"账单",
		wallet:"钱包地址",
		banks: "银行卡绑定",
		banksInternational: "银行卡绑定【国际】",
		addBank:"添加银行卡",
		securitySettings:"安全设置",
		coupon:"卡券中心",
		systemNotification:"系统通知",
		defaultFiatCurrency:"默认法币",
		language:"语言",
		faq:"常见问题",
		operationalCompliance:"经营合规",
		share:"分享",
		contactUs:"联系我们",
		aboutUs:"关于我们",
		avatar:'头像',
		nickname:'昵称',
		signature:'个性签名',
		grxx:"个人信息",
		grxxOptions:{
			nickname:{
				title:'昵称',
				placeholder:'请输入昵称',
				desc:'2-10个字符，支持中英文、数字',
			},
			signature:{
				title:'个性签名',
				placeholder:'请输入个性签名',
				desc:'2-30个字符，支持中英文、数字',
			}
		},
		xxtx:"上传头像",
		photograph:'拍照',
		selectFromAlbum:'从相册选择',
		aqsz:"安全设置",
		editLoginPassword:'修改登录密码',
		setPayPassword:"设置支付密码",
		editPayPassword:"修改支付密码",
		bindPhone:"绑定手机",
		email:'邮箱',
		stayLogin:'保持登录',
		xgdlmm:"修改登录密码",
		currentPassword:'当前登录密码',
		currentPasswordPlaceholder:'请输入当前登录密码',
		newPassword:"新登录密码",
		newPasswordPlaceholder:"请输入新登录密码",
		confirmNewPassword:"确认新密码",
		confirmNewPasswordPlaceholder:"请输入确认新密码",
		currentPayPassword:"当前支付密码",
		currentPayPasswordPlaceholder:'请输入当前支付密码',
		newPayPassword:"新支付密码",
		newPayPasswordPlaceholder:"请输入新支付密码",
		confirmNewPayPassword:"确认新支付密码",
		confirmNewPayPasswordPlaceholder:"请输入确认新支付密码",
		phoneVerificationCode:'手机验证码',
		editPhoneVerificationCode:"请输入手机验证码",
		getVerificationCode:'获取验证码',
		lctyq:"理财体验券",
		expired:"已失效",
		expiredTime:"失效日期",
		useImmediately:"立即使用",
		addNewAccount:"添加新账号",
		totayIncome:"今日收款",
		totalIncome:"累计收款",
		selectLang:"请选择语言",
		selectCoinType:"请选择币种",
		authentication:'身份认证',
		authentication1:'基础认证',
		authentication2:'高级认证',
		authentication_text_1:'个人身份认证',
		authentication_text_2:'完成认证后可获得的权限：',
		authentication_text_3:'Lv1.基础认证',
		authentication_text_4:'认证后可以体现，24小时限额200BTC',
		authentication_text_5:'认证后可以进行法币交易，单笔限额2000USDT',
		authentication_text_6:'Lv2.高级认证',
		authentication_text_7:'增加体现额度，24小时限额500BTC',
		authentication_text_8:'增加法币交易额度，单笔限额1000000USDT',
		authentication_text_9:'温馨提示：为保护用户资金安全，请先完成邮箱和电话的绑定，否则您的认证将无法通过。',
		goAudit:'去认证',
		noaudit:'未认证',
		auditing:'认证中',
		hasaudit:'已认证',
		idType: '证件类型',
		idCard: '身份证',
		passport: '护照',
		driverLicense: '驾驶证',
		name:'姓名',
		idcard:'证件号码',
		plsIptName:'请输入姓名',
		plsIptCorrectIdcard:'请输入正确的身份证号',
		uploadIdcardFront:'上传证件正面',
		uploadIdcardReverse:'上传证件背面',
		emailVerificationCode:'邮箱验证码',
		plsiptEmailCode:'请输入邮箱验证码',
		addWallet:'添加钱包地址',
		selectCurrency:'选择币种',
		plsSelectCurrency:'请选择币种',
		plsIptAddress:'请输入钱包地址',
		walletAddress:'钱包地址',
		walletQrcode:'钱包二维码',
		plsIptWalletAddress:'请输入钱包地址',
		plsUploadWalletQrcode:'请上传钱包二维码',
		confirmLogout:'确定退出登录吗？',
		nonickname:'请设置昵称',
		plsInputMobile: "请输入手机号",
		alipay_account:'ABA号',
		real_name:'真实姓名',
		bank_account:'银行卡号',
		bank_dizhi:'开户省市',
		bank_name:'开户行',
		bank_network: '开户网点',
		swift_code: '国际支付代码',
		phone: '联系电话',
		wechat_account:'持卡人地址',
		wechat_nickname:'SWIFT代码',
		allNeed:'所有均为必填项',
		phoneNumber:'手机号',
		code:'验证码',
		sendCode:'发送验证码',
		plsIptCode:'请输入验证码',
		withdrawPassword:'提现密码',
		confirmWithdrawPassword:'确认提现密码',
		plsInputWithdrawPassword:'请输入提现密码',
		plsInputConfirmWithdrawPassword:'请输入确认提现密码',
		withdrawPasswordPlaceholder:'请输入6-16位提现密码',
		confirmWithdrawPasswordPlaceholder:'请再次输入提现密码',
		updateWithdrawPassword:'修改提现密码',
		oldWithdrawPassword:'原提现密码',
		newWithdrawPassword:'新提现密码',
		plsInputOldWithdrawPassword:'请输入原提现密码',
		plsInputNewWithdrawPassword:'请输入新提现密码',
		withdrawPasswordNotMatch:'两次输入的提现密码不一致',
		withdrawPasswordLengthError:'提现密码长度应为6-16位',
		withdrawPasswordRequired:'请设置提现密码',
		withdrawPasswordVerifyFailed:'提现密码验证失败',
		needRealAuth: '交易前需要完成实名认证',
		realAuthPending: '实名认证审核中，请等待审核完成',
		score: '信用分'
	},
	transaction:{
		actualPL: '实际盈亏',
		favorites:"自选",
		futures:"合约",
		coins:"币币",
		stock:"股票",
		tradingPair:'交易对',
		lastPrice:'最新价',
		todayChange:'今日涨跌幅',
		searchPlaceholder:'搜索您关心的币种/股票名称/代码',
		hotSearch:'热门搜索',
		recentView:'最近查看',
		dayHigh:"今日高",
		dayLow:"今日低",
		optional:'自选',
		buy:'买入',
		sell:'卖出',
		entrustPendingOrder:'委托挂单',
		addOptionalSuccess:'添加自选成功',
		delOptionalSuccess:'删除自选成功',
		seconds:"期权",
		home:"首页",
		orderConfirm:"订单确认",
		currentPrice:'当前价',
		direction:"方向",
		selectTime:"选择到期时间",
		number:"数量",
		balance:'账户余额',
		plsIptCrtNumber:'请输入正确的数量',
		buyPrice:'购买价',
		expectedPL:"预计盈亏",
		continueTrade:"继续交易",
		secondsPosition:'期权持仓',
		position:'在持',
		history:'历史',
		orderTimes:'订单时长',
		pl:"盈亏",
		sellTime:"卖出时间",
		buy:'买入',
		sell:'卖出',
		price:'价格',
		time:"时间",
		optional:'自选',
		buy:'买入',
		sell:'卖出',
		long:"做多",
		short:"做空",
		entrustPendingOrder:'委托挂单',
		addOptionalSuccess:'添加自选成功',
		delOptionalSuccess:'删除自选成功',
		price:"价格",
		amount:"数量",
		marketPrice:"市价",
		limitPrice:"限价",
		number:"交易手数",
		margin:"保证金",
		handlingFee:"手续费",
		balance:"余额",
		multiple:"倍数",
		recharge:'充值',
		plsIptCommissionPrice:'请输入委托价格',
		tradingPair:"交易对",
		direction:"方向",
		plsIptCrtPrice:'请输入正确的价格',
		position:"持仓",
		time:"时间",
		turnover:"交易额",
		operation:"操作",
		type:"类型",
		cover:'平仓',
		confirmCover:'确认平仓吗？',
		confirmSelfHold:'确定转自持吗？',
		selfHolding:'自持',
		transSelfHold:'转自持',
		delegateList:"委托列表",
		riskRate:"风险率",
		totalPandL:'持仓总收益',
		oneClickCover:"一键平仓",
		open:"开仓价",
		targetProfitPrice:"止盈价",
		updatePrice:"当前价",
		stopLossPrice:"止损价",
		overnightMoney:"隔夜费",
		openTime:"开仓时间",
		lots:"手",
		setProfitLoss:"设置止盈止损",
		expectedProfit:"预期盈利",
		expectedLoss:"预期亏损",
		coverAll:"全部平仓",
		onlyCoverMany:"只平多单",
		onlyCoverEmpty:"只平空单",
		numbers:'数量',
		coinBuyPrice:'以当前最优价格交易',
		jine:'金额',
		p_jine:'请输入金额',
		p_price:'请输入价格',
		p_confirm_sj:'确定以市价',
		p_confirm_xj_1:'确定以现价',
		p_confirm_ma:'吗？',
		status:"状态",
		complete:"已完成",
		doing:"进行中",
		currentEntrust:"当前委托",
		allEntrust:"全部委托",
		entrust:"委托",
		entrustTime:"委托时间",
		all:"全部",
		buyUp:"买多",
		buyDown:"买少",
		jye:'交易额',
	},
	fund:{
		overview:'总览',
		balance:'余额',
		futures:'合约',
		deposit:'存款',
		mining:'锁仓赚币',
		stock:'股票',
		margin:'超级杠杆',
		yesterdayEarnings:'昨日收益',
		yieldAtMaturily:'到期收益',
		totalkRevenue:'总收益',
		currentProfit:'当前盈亏',
		profitRatio:'盈亏率',
		receive:'充值/收款',
		transfer:'提现/转账',
		financial:'金融理财',
		valuation:'估值',
		currentEarnings:'当前收益',
		yesterdayIncome:'昨日收益',
		crypto:'数字货币',
		fiat:'法币',
		hideSmallCurrency:'隐藏小额币种',
		available:'可用',
		freeze:'冻结',
		equivalent:'折合',
		usdtm:'U本位',
		coinm:'币本位',
		exchange:"币币",
		leverage:'合约',
		fiat:'法币',
		second:'期权',
		convert:"总资产折合",
		convert2:"资产折合",
		record:"帐变记录",
		availableQuota:"可用额度",
		locked:"锁定",
		converted:"折合",
		account:"账户",
		financialRecords:"财务记录",
		number:"数量",
		record2:"记录",
		time:"时间",
		transfer:"币币划转",
		from:"从",
		to:"至",
		transferNumber:"划转数量",
		youGet:"你将得到",
		exchangeRate:"兑换汇率",
		handlingFee:"手续费",
		fundTransfer:"资金划转",
		insufficientBalance:"余额不足",
		needHandlingFee:"另需手续费",
		plsIptCrtNumber:'请输入正确的数量',
		c_transfer:"确定划转吗？",
		selectCurrency:"选择币种",
		DIGICCY:'数字货币',
		saveQrcode:'保存二维码',
		copyAddress:'复制链接地址',
		plsTrans:'请往此地址划转',
		rechargeNumer:"充值数量",
		paymentVoucher:"支付凭证",
		plsIptRechargeNumer:"请输入充值数量",
		plsUploadPaymentVoucher:"请上传支付凭证",
		plsIptCrtAmount:'请输入正确的金额',
		withdraw:"提现/转账",
		currencyName:"币种名称",
		transactionInfo:"交易账户",
		withdraw2:'提现',
		canUse:'可用',
		all:'全部',
		plsIptWithdrawNumber:'请输入提现数量',
		withdrawNumber:'提现数量',
		remark:'备注',
		handlingFee:'手续费',
		canGetNumber:'到账数量',
		leastNumber:'最低提现数量',
		coinType:'币种',
		address:'地址',
		date:'日期',
		withdrawAddress:'提币地址',
		plsSelectWithdrawAddress:'请选择提币地址',
		withdrawToCard:'提到银行卡',
		withdrawToCardInternational: '提到国际银行卡',
		withdrawToAddress:'提到钱包地址',
		card:'银行卡',
		addCard:'添加银行卡',
		plsAddCard:'请添加银行卡',
		plsInputWithdrawPasswordForWithdraw:'请输入提现密码进行验证'
	},
	ieo:{
		subscribe:"认购",
		ing:"进行中",
		done:"已结束",
		applySubscription:"申请认购",
		remaining:"剩余",
		lockPeriod:"锁仓周期",
		endTime:"结束时间",
		subscribeCurrency:"认购币种",
		issuancePrice:"发行价格",
		totalIssuance:"发行总量",
		whitePaper:"白皮书",
		subscriptionCycle:"申购周期",
		warmUp:"预热",
		website:"官网",
		finish:"结束",
		projectDetails:"项目详情",
		subscriptionConfirmation:"认购确认",
		p_applicationsNumber:"输入认购数量",
		needToPay:"需要支付",
		currentBalance:"当前余额",
		subscriptionTime:"申购时间",
		currency:"币种",
		applicationsNumber:"申请数量",
		passesNumber:"通过数量",
		timeToMarket:"上市时间",
		day:"天",
		total:"总量",
		subscribed:'已认购',
		shengou:"申购",
		insufficientBalance:"余额不足",
		p_number:"请输入数量",
		mySubscribe:"我的认购"
	},

	invest:{
		invest:'理财',
		section:'专区',
		perPrice:'单份价格',
		apr:"年化收益率",
		linkedReferencePrice:"挂钩参考价",
		holdingDays:"持仓期限",
		maturityDate:"到期日",
		remainingShares:"剩余份额",
		progress:"当前进度",
		action:"操作",
		purchase:'购买',
		myPosition:'我的理财',
		buyShares:'买入份额',
		estimatedIncome:"预计收益",
		day:'天',
		share:'份',
		fail:'失败',
		purchased:'已买入',
		settlementing:'结算中',
		success:'已完成',
		all:"全部",
		name:'名称',
		remain:'剩余',
		instruction:"产品说明",
		help:'帮助',
		shuoming_1:'1.本产品为非保本理财产品，市场波动可能导致本金损失，请谨慎投资.',
		shuoming_2:'2.回款结算规则：',
		shuoming_3:'如果结算价＜挂钩价，到期后结算BTC；结算数量=投资金额*(1+天数*年化收益率/365).',
		shuoming_4:'如果结算价≥挂钩价，到期后结算USDT；结算数量=投资金额*挂钩价*(1+天数*年化收益率/365）.',
		shuoming_5:'3.实际年化收益率随市场实时变动，请以实际买入成交为准.',
		shuoming_6:'4.投资金额随市场实时折算得出，请以实际买入成交为准.',
		shuoming_7:'5.产品购买后可在我的持币页面进行查看，到期结束后回款会自动发放至现货账户.',
		error_1:'请输入购买数量',
		error_2:'购买数量应为整数',
		confirmPurchase:'确定购买该理财产品吗？',
		purchaseSuccess:'购买成功!',
	},
	follow:{
		follow:'跟单',
		traderList:'交易员列表',
		totalProfit:'总盈亏',
		correctRate:'总准确率',
		positionValuation:"持仓估值",
		estimatedProfitToday:"今日预计收益",
		rateOfReturn:"收益率",
		becomeATrader:"成为交易员",
		traderList:"交易员列表",
		onlyShowsThePosition:"仅显示有位置",
		theDataIsUpdatedEveryHour:"数据每小时更新一次",
		following:"跟单中",
		follow:"跟单",
		confirmFollow:'确定跟随吗？',
		confirmCancel:'确定取消跟随？',
		followTrader:'跟随交易员',
		editCopy: "编辑跟随",
		beforeCopy: "选择跟随之前请仔细阅读",
		copyAgreement: "跟随协议",
		copyType: "跟随方式",
		copyType1: "固定倍数跟随",
		copyType2: "固定手数跟随",
		copyAlert1: "无论交易员下单多少，您都按选择的固定倍数跟随。",
		copyAlert2: "(当比例手数>1且不为整数时，四舍五入保留两位小数取值）",
		copyMultiple: "跟随倍数",
		cancelCopy: "取消跟随",
		currentFollowerNumber:"当前跟随人数",
		totalProfitAndLoss:"总盈亏",
		totalReturn:"总收益率",
		accuracy:"准确率",
		profitableOrders:"盈利订单",
		successfulShortTrades:"成功做空交易",
		successfulLongTrades:"成功做多交易",
		yesterdaysTradingStatus:"昨日交易状况",
		currentPositions:"当前持仓",
		totalOrders:"总订单",
		remain:'剩余',
		distanceDue:"距离到期",
		days:"天",
		deadline:"期限",
		subscription:"申购",
		followUsers:"跟随用户",
		followAmount:"跟随金额",
		followEarnings:"跟随收益",
		cancelFollow:"取消跟随",
		currentCopy:"当前跟单",
		historicalCopy:"历史交易",
		myTrader:"我的交易员",
		tradingPair:'交易对',
		open:'开仓价',
		close:'平仓价',
		earnings:'收益',
		lots:'手数',
		time:'交易时间',
		buyIn:'买入',
		buyOut:'卖出',
		direction:'方向'
	},
	financial:{
		product:'产品',
		position:'持有',
		financial:'金融',
		lockming:'锁仓挖矿',
		ieo:"IEO",
		invest:"投资理财",
		section:'专区',
		apr:"年化收益率",
		linkedReferencePrice:"挂钩参考价",
		holdingDays:"持仓期限",
		day:'天',
		lockPeriod:"锁仓周期",
		ing:"进行中",
		done:"已结束",
		applySubscription:"申请认购",
		remaining:"剩余",
		total:"总量",
		subscribed:'已认购',
		mining:'锁仓赚币',
		minimum:'起',
		dailyReturnRate:"日收益率",
	},
	nft:{
		nft:'NFT',
		artwork:'艺术品',
		artist:'艺术家',
		artworkList:'艺术品列表',
		artistList:'艺术家列表',
		comprehensiveSorting:'综合排序',
		newest:'最新的',
		hotest:'最热的',
		mostWorks:'作品最多',
		mostPopular:'最受欢迎',
		record:'购买记录',
		recommendArtist:'推荐的艺术家',
		buy:'购买',
		artistHomepage:'艺术家主页',
		works:'作品',
		saled:'已售',
		fans:'粉丝',
		allWorks:'全部作品',
		notSell:'未售出',
		myCollect:'我的收藏',
		collect:"收藏的",
		own:"持有",
		placeABid:'出价',
		purchase:'购买',
		open:'后开放',
		image:'图片',
		gif:'动图',
		audio:'音频',
		video:'视频',
		creator:'作者',
		currentBid:'最新出价',
		price:'价格',
		d:'天',
		h:'时',
		m:'分',
		s:'秒',
		saled:'已结束',
		place_alert:'物品未到售卖时间或已结束',
		createdBy:'作者',
		place_alert2:"您的出价必须高于",
		note:'重要',
		CURRENT_BID:'最新出价',
		place_alert3:'参与竞拍，您需要支付',
		place_alert4:'的保证金，竞拍结束时，保证金将返还至您的账户。',
		place_alert6:'出价时间截止后,若您竞拍成功，您将有一天的时间支付最终价格。若支付超时，则会扣除您的保证金。',
		confirmBid:'确认出价',
		DESCRIPTION:'描述',
		ADDRESS:'地址',
		copy:'复制',
		per_increase:'每次加价',
		currency_type:'货币类型',
		pay_type:'购买方式',
		pay_type_2:'售卖方式',
		normal:'普通',
		auction:'竞拍',
		confirmAuction:'确定参与竞拍该商品吗?',
		bindBox:'盲盒',
		sell_status:'售卖状态',
		hasStart:'已开始',
		willStart:'未开始',
		artwork_type:'物品类型',
		reset:'重置',
		confirm:'确定',
		place_alert5:'请输入关键词',
		collection:'已收藏',
		byCollection:'被收藏',
		artworks:'作品',
		goCollect:'去收藏',
		price:'价格',
		nonickname:'未设置昵称',
		cjdjs:'出价截止时间',
		cjkssj:'出价开始时间',
		cjjzsj:'出价截止时间',
		gotosee:'快去市场看看吧',
		position:'持有',
		resell:'转卖',
		buyPrice:'买入价',
		confirmResell:'确认转卖',
		wrongPrice:'请选择正确的价格',
		bidPlacedBy:'出价人',
		placeRecord:'出价记录',
		confirPurchase:'确认购买',
		artistList:'艺术家列表',
		hasOpen:'已开启',
		noOpen:'未开启',
		clickOpen:'开启盲盒',
		confirmOpen:'确认开启盲盒吗？',
		margin:'保证金',
		wrongPrice:'请输入正确的价格',
		wrongPerIncrease:'请输入正确的加价',
		wrongTime:'结束时间应晚于结束时间',
		confirmResell2:'商品一旦转卖，不可撤回，确定要转卖该商品吗？',
		reselling:'转卖中',
		message:'消息通知',
		confirmPay:'确定支付购买该商品吗？',
		zfdjs:'支付倒计时',
		payTime:'支付时间',
		hasRead:'已读',
		lastPrice:'最终成交价',
		isPay:'已支付',
		isExpired:'已过期',
		rarity:'稀有度'
	},
	tradeQuery: {
		title: "交易查询",
		tradingPair: "交易对",
		direction: "交易方向",
		betSeconds: "下注秒数",
		betAmount: "下注金额",
		currentStatus: "当前状态",
		buyUp: "买涨",
		buyDown: "买跌",
		seconds: "秒",
		profit: "盈利",
		loss: "亏损",
		draw: "平局",
		trading: "交易中",
		noData: "暂无数据",
		loading: "加载中...",
		refresh: "刷新"
	},
	contact: {
		contactUs: '在线客服'
	}

}

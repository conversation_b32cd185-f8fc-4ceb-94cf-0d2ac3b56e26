/**
 * @license
 * KLineChart v8.3.2
 * Copyright (c) 2019 lihu.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).klinecharts={})}(this,(function(t){"use strict";var e={name:"BBI",shortName:"BBI",series:"price",precision:2,calcParams:[3,6,12,24],shouldCheckParamCount:!0,shouldOhlc:!0,plots:[{key:"bbi",title:"BBI: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max.apply(null,i),r=[],a=[];return t.map((function(e,o){var s={},c=e.close;if(i.forEach((function(e,i){r[i]=(r[i]||0)+c,e-1>o||(a[i]=r[i]/e,r[i]-=t[o-(e-1)].close)})),o>=n-1){var h=0;a.forEach((function(t){h+=t})),s.bbi=h/4}return s}))}},i={name:"DMA",shortName:"DMA",calcParams:[10,50,10],plots:[{key:"dma",title:"DMA: ",type:"line"},{key:"ama",title:"AMA: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max(i[0],i[1]),r=0,a=0,o=0,s=[];return t.forEach((function(e,c){var h,l,u={},f=e.close;if(r+=f,a+=f,i[0]-1>c||(h=r/i[0],r-=t[c-(i[0]-1)].close),i[1]-1>c||(l=a/i[1],a-=t[c-(i[1]-1)].close),c>=n-1){var d=h-l;u.dma=d,o+=d,n+i[2]-2>c||(u.ama=o/i[2],o-=s[c-(i[2]-1)].dma)}s.push(u)})),s}},n={name:"DMI",shortName:"DMI",calcParams:[14,6],plots:[{key:"pdi",title:"PDI: ",type:"line"},{key:"mdi",title:"MDI: ",type:"line"},{key:"adx",title:"ADX: ",type:"line"},{key:"adxr",title:"ADXR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0,s=0,c=0,h=0,l=0,u=[];return t.forEach((function(e,f){var d={},v=t[f-1]||e,p=v.close,_=e.high,y=e.low,m=_-y,x=Math.abs(_-p),g=Math.abs(p-y),S=_-v.high,k=v.low-y,b=Math.max(Math.max(m,x),g),w=S>0&&S>k?S:0,E=k>0&&k>S?k:0;if(n+=b,r+=w,a+=E,f>=i[0]-1){f>i[0]-1?(o=o-o/i[0]+b,s=s-s/i[0]+w,c=c-c/i[0]+E):(o=n,s=r,c=a);var P=0,C=0;0!==o&&(P=100*s/o,C=100*c/o),d.pdi=P,d.mdi=C;var A=0;C+P!==0&&(A=Math.abs(C-P)/(C+P)*100),h+=A,2*i[0]-2>f||(d.adx=l=f>2*i[0]-2?(l*(i[0]-1)+A)/i[0]:h/i[0],2*i[0]+i[1]-3>f||(d.adxr=(u[f-(i[1]-1)].adx+l)/2))}u.push(d)})),u}},r={name:"MACD",shortName:"MACD",calcParams:[12,26,9],plots:[{key:"dif",title:"DIF: ",type:"line"},{key:"dea",title:"DEA: ",type:"line"},{key:"macd",title:"MACD: ",type:"bar",baseValue:0,color:function(t,e){var i=(t.current.technicalIndicatorData||{}).macd;return i>0?e.bar.upColor:0>i?e.bar.downColor:e.bar.noChangeColor},isStroke:function(t){return(t.current.technicalIndicatorData||{}).macd>(t.prev.technicalIndicatorData||{}).macd}}],calcTechnicalIndicator:function(t,e){var i,n,r=e.params,a=0,o=0,s=0,c=0,h=Math.max(r[0],r[1]);return t.map((function(t,e){var l={},u=t.close;return a+=u,r[0]-1>e||(i=e>r[0]-1?(2*u+(r[0]-1)*i)/(r[0]+1):a/r[0]),r[1]-1>e||(n=e>r[1]-1?(2*u+(r[1]-1)*n)/(r[1]+1):a/r[1]),h-1>e||(l.dif=o=i-n,s+=o,h+r[2]-2>e||(l.macd=2*(o-(c=e>h+r[2]-2?(2*o+c*(r[2]-1))/(r[2]+1):s/r[2])),l.dea=c)),l}))}},a={name:"CR",shortName:"CR",calcParams:[26,10,20,40,60],plots:[{key:"cr",title:"CR: ",type:"line"},{key:"ma1",title:"MA1: ",type:"line"},{key:"ma2",title:"MA2: ",type:"line"},{key:"ma3",title:"MA3: ",type:"line"},{key:"ma4",title:"MA4: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.ceil(i[1]/2.5+1),r=Math.ceil(i[2]/2.5+1),a=Math.ceil(i[3]/2.5+1),o=Math.ceil(i[4]/2.5+1),s=0,c=[],h=0,l=[],u=0,f=[],d=0,v=[],p=[];return t.forEach((function(e,_){var y={},m=t[_-1]||e,x=(m.high+m.close+m.low+m.open)/4,g=Math.max(0,e.high-x),S=Math.max(0,x-e.low);i[0]-1>_||(y.cr=0!==S?g/S*100:0,s+=y.cr,h+=y.cr,u+=y.cr,d+=y.cr,i[0]+i[1]-2>_||(c.push(s/i[1]),i[0]+i[1]+n-3>_||(y.ma1=c[c.length-1-n]),s-=p[_-(i[1]-1)].cr),i[0]+i[2]-2>_||(l.push(h/i[2]),i[0]+i[2]+r-3>_||(y.ma2=l[l.length-1-r]),h-=p[_-(i[2]-1)].cr),i[0]+i[3]-2>_||(f.push(u/i[3]),i[0]+i[3]+a-3>_||(y.ma3=f[f.length-1-a]),u-=p[_-(i[3]-1)].cr),i[0]+i[4]-2>_||(v.push(d/i[4]),i[0]+i[4]+o-3>_||(y.ma4=v[v.length-1-o]),d-=p[_-(i[4]-1)].cr)),p.push(y)})),p}},o={name:"AO",shortName:"AO",calcParams:[5,34],shouldCheckParamCount:!0,plots:[{key:"ao",title:"AO: ",type:"bar",baseValue:0,color:function(t,e){return(t.current.technicalIndicatorData||{}).ao>(t.prev.technicalIndicatorData||{}).ao?e.bar.upColor:e.bar.downColor},isStroke:function(t){return(t.current.technicalIndicatorData||{}).ao>(t.prev.technicalIndicatorData||{}).ao}}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max(i[0],i[1]),r=0,a=0,o=0,s=0;return t.map((function(e,c){var h={},l=(e.low+e.high)/2;if(r+=l,a+=l,c>=i[0]-1){o=r/i[0];var u=t[c-(i[0]-1)];r-=(u.low+u.high)/2}if(c>=i[1]-1){s=a/i[1];var f=t[c-(i[1]-1)];a-=(f.low+f.high)/2}return n-1>c||(h.ao=o-s),h}))}},s={name:"CCI",shortName:"CCI",calcParams:[20],plots:[{key:"cci",title:"CCI: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]-1,r=0,a=[];return t.map((function(e,o){var s={},c=(e.high+e.low+e.close)/3;if(r+=c,a.push(c),o>=n){var h=r/i[0],l=a.slice(o-n,o+1),u=0;l.forEach((function(t){u+=Math.abs(t-h)}));var f=u/i[0];s.cci=0!==f?(c-h)/f/.015:0,r-=(t[o-n].high+t[o-n].low+t[o-n].close)/3}return s}))}},c={name:"RSI",shortName:"RSI",calcParams:[6,12,24],shouldCheckParamCount:!1,plots:[{key:"rsi1",title:"RSI1: ",type:"line"},{key:"rsi2",title:"RSI2: ",type:"line"},{key:"rsi3",title:"RSI3: ",type:"line"}],regeneratePlots:function(t){return t.map((function(t,e){var i=e+1;return{key:"rsi".concat(i),title:"RSI".concat(i,": "),type:"line"}}))},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[],a=[];return t.map((function(e,o){var s={},c=e.close-(t[o-1]||e).close;return i.forEach((function(e,i){if(c>0?r[i]=(r[i]||0)+c:a[i]=(a[i]||0)+Math.abs(c),o>=e-1){s[n[i].key]=0!==a[i]?100-100/(1+r[i]/a[i]):0;var h=t[o-(e-1)],l=h.close-(t[o-e]||h).close;l>0?r[i]-=l:a[i]-=Math.abs(l)}})),s}))}};function h(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=Number.MIN_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER;return t.forEach((function(t){e=Math.max(t.high,e),i=Math.min(t.low,i)})),{hn:e,ln:i}}var l={name:"KDJ",shortName:"KDJ",calcParams:[9,3,3],plots:[{key:"k",title:"K: ",type:"line"},{key:"d",title:"D: ",type:"line"},{key:"j",title:"J: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=[];return t.forEach((function(e,r){var a={},o=e.close;if(r>=i[0]-1){var s=h(t.slice(r-(i[0]-1),r+1)),c=s.ln,l=s.hn-c;a.k=((i[1]-1)*(n[r-1].k||50)+(o-c)/(0===l?1:l)*100)/i[1],a.d=((i[2]-1)*(n[r-1].d||50)+a.k)/i[2],a.j=3*a.k-2*a.d}n.push(a)})),n}},u={name:"WR",shortName:"WR",calcParams:[6,10,14],shouldCheckParamCount:!1,plots:[{key:"wr1",title:"WR1: ",type:"line"},{key:"wr2",title:"WR2: ",type:"line"},{key:"wr3",title:"WR3: ",type:"line"}],regeneratePlots:function(t){return t.map((function(t,e){return{key:"wr".concat(e+1),title:"WR".concat(e+1,": "),type:"line"}}))},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots;return t.map((function(e,r){var a={},o=e.close;return i.forEach((function(e,i){var s=e-1;if(r>=s){var c=h(t.slice(r-s,r+1)),l=c.hn,u=l-c.ln;a[n[i].key]=0===u?0:(o-l)/u*100}})),a}))}};var f={name:"BOLL",shortName:"BOLL",calcParams:[20,{value:2,allowDecimal:!0}],precision:2,shouldOhlc:!0,plots:[{key:"up",title:"UP: ",type:"line"},{key:"mid",title:"MID: ",type:"line"},{key:"dn",title:"DN: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]-1,r=0;return t.map((function(e,a){var o={};if(r+=e.close,a>=n){o.mid=r/i[0];var s=function(t,e){var i=t.length,n=0;t.forEach((function(t){var i=t.close-e;n+=i*i}));var r=n>0,a=Math.sqrt((n=Math.abs(n))/i);return r?a:-1*a}(t.slice(a-n,a+1),o.mid);o.up=o.mid+i[1]*s,o.dn=o.mid-i[1]*s,r-=t[a-n].close}return o}))}},d={name:"SAR",shortName:"SAR",series:"price",calcParams:[2,2,20],precision:2,shouldOhlc:!0,plots:[{key:"sar",title:"SAR: ",type:"circle",color:function(t,e){var i=t.current,n=i.kLineData||{};return(n.high+n.low)/2>(i.technicalIndicatorData||{}).sar?e.circle.upColor:e.circle.downColor}}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]/100,r=i[1]/100,a=i[2]/100,o=n,s=-100,c=!1,h=0;return t.map((function(e,i){var l=h,u=e.high,f=e.low;if(c){(-100===s||u>s)&&(s=u,o=Math.min(o+r,a)),h=l+o*(s-l);var d=Math.min(t[Math.max(1,i)-1].low,f);h>e.low?(h=s,o=n,s=-100,c=!c):h>d&&(h=d)}else{(-100===s||s>f)&&(s=f,o=Math.min(o+r,a)),h=l+o*(s-l);var v=Math.max(t[Math.max(1,i)-1].high,u);e.high>h?(h=s,o=0,s=-100,c=!c):v>h&&(h=v)}return{sar:h}}))}},v={technicalIndicatorExtensions:{},shapeExtensions:{},addTechnicalIndicatorTemplate:function(t){var e=this;t&&[].concat(t).forEach((function(t){t.name&&(e.technicalIndicatorExtensions[t.name]=t)}))},addShapeTemplate:function(t){var e=this;t&&[].concat(t).forEach((function(t){t.name&&(e.shapeExtensions[t.name]=t)}))}};function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _(t,e){for(var i=0;e.length>i;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function y(t,e,i){return e&&_(t.prototype,e),i&&_(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function x(t,e){if(b(t)&&b(e))for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&i in t){var n=t[i],r=e[i];b(r)&&b(n)&&!S(r)&&!S(n)?x(n,r):E(e[i])&&(t[i]=e[i])}}function g(t){if(!b(t))return t;var e;for(var i in e=S(t)?[]:{},t)if(Object.prototype.hasOwnProperty.call(t,i)){var n=t[i];e[i]=b(n)?g(n):n}return e}function S(t){return"[object Array]"===Object.prototype.toString.call(t)}function k(t){return t&&"function"==typeof t}function b(t){return!!t&&"object"===m(t)}function w(t){return"number"==typeof t&&!isNaN(t)}function E(t){return null!=t}function P(t){return"boolean"==typeof t}function C(t){return"string"==typeof t}var A="stroke",I="fill",T="dash",M="solid",O="left",D="right",R="normal",L="percentage",B="log",F="candle_solid",z="candle_stroke",V="candle_up_stroke",j="candle_down_stroke",H="ohlc",N="area",Y="always",W="follow_cross",X="rect",G="standard",U="circle",Z="rect",K="triangle",q="diamond",$="custom",J="point",Q="top",tt="bottom",et={grid:{show:!0,horizontal:{show:!0,size:1,color:"#EDEDED",style:T,dashValue:[2,2]},vertical:{show:!0,size:1,color:"#EDEDED",style:T,dashValue:[2,2]}},candle:{margin:{top:.2,bottom:.1},type:F,bar:{upColor:"#26A69A",downColor:"#EF5350",noChangeColor:"#999999"},area:{lineSize:2,lineColor:"#2196F3",value:"close",backgroundColor:[{offset:0,color:"rgba(33, 150, 243, 0.01)"},{offset:1,color:"rgba(33, 150, 243, 0.2)"}]},priceMark:{show:!0,high:{show:!0,color:"#76808F",textMargin:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"},low:{show:!0,color:"#76808F",textMargin:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"},last:{show:!0,upColor:"#26A69A",downColor:"#EF5350",noChangeColor:"#888888",line:{show:!0,style:T,dashValue:[4,4],size:1},text:{show:!0,size:12,paddingLeft:2,paddingTop:2,paddingRight:2,paddingBottom:2,color:"#FFFFFF",family:"Helvetica Neue",weight:"normal",borderRadius:2}}},tooltip:{showRule:Y,showType:G,labels:["时间: ","开: ","收: ","高: ","低: ","成交量: "],values:null,defaultValue:"n/a",rect:{paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:6,offsetLeft:8,offsetTop:8,offsetRight:8,borderRadius:4,borderSize:1,borderColor:"#F2F3F5",backgroundColor:"#FEFEFE"},text:{size:12,family:"Helvetica Neue",weight:"normal",color:"#76808F",marginLeft:8,marginTop:6,marginRight:8,marginBottom:0}}},technicalIndicator:{margin:{top:.2,bottom:.1},bar:{upColor:"rgba(38, 166, 154, .65)",downColor:"rgba(239, 83, 80, .65)",noChangeColor:"#888888"},line:{size:1,colors:["#FF9600","#9D65C9","#2196F3","#E11D74","#01C5C4"]},circle:{upColor:"rgba(38, 166, 154, .65)",downColor:"rgba(239, 83, 80, .65)",noChangeColor:"#888888"},lastValueMark:{show:!1,text:{show:!1,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:3,paddingTop:2,paddingRight:3,paddingBottom:2,borderRadius:2}},tooltip:{showRule:Y,showType:G,showName:!0,showParams:!0,defaultValue:"n/a",text:{size:12,family:"Helvetica Neue",weight:"normal",color:"#76808F",marginTop:6,marginRight:8,marginBottom:0,marginLeft:8}}},xAxis:{show:!0,height:null,axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:"#76808F",size:12,family:"Helvetica Neue",weight:"normal",paddingTop:3,paddingBottom:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}},yAxis:{show:!0,width:null,type:R,position:D,inside:!1,axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:"#76808F",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:3,paddingRight:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}},separator:{size:1,color:"#DDDDDD",fill:!0,activeBackgroundColor:"rgba(33, 150, 243, 0.08)"},crosshair:{show:!0,horizontal:{show:!0,line:{show:!0,style:T,dashValue:[4,2],size:1,color:"#76808F"},text:{show:!0,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderSize:1,borderColor:"#686D76",borderRadius:2,backgroundColor:"#686D76"}},vertical:{show:!0,line:{show:!0,style:T,dashValue:[4,2],size:1,color:"#76808F"},text:{show:!0,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderSize:1,borderRadius:2,borderColor:"#686D76",backgroundColor:"#686D76"}}},shape:{point:{backgroundColor:"#2196F3",borderColor:"rgba(33, 150, 243, 0.35)",borderSize:1,radius:5,activeBackgroundColor:"#2196F3",activeBorderColor:"rgba(33, 150, 243, 0.35)",activeBorderSize:3,activeRadius:5},line:{style:M,color:"#2196F3",size:1,dashValue:[2,2]},polygon:{style:A,stroke:{style:M,size:1,color:"#2196F3",dashValue:[2,2]},fill:{color:"#2196F3"}},arc:{style:A,stroke:{style:M,size:1,color:"#2196F3",dashValue:[2,2]},fill:{color:"#2196F3"}},text:{style:I,color:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",offset:[0,0]}},annotation:{position:Q,offset:[20,0],symbol:{type:q,size:8,color:"#2196F3",activeSize:10,activeColor:"#FF9600"}},tag:{position:J,offset:0,line:{show:!0,style:T,dashValue:[4,2],size:1,color:"#2196F3"},text:{color:"#FFFFFF",backgroundColor:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderRadius:2,borderSize:1,borderColor:"#2196F3"},mark:{offset:0,color:"#FFFFFF",backgroundColor:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderRadius:2,borderSize:1,borderColor:"#2196F3"}}};function it(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"--";if(b(t)){var n=t[e];if(E(n))return n}return i}function nt(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"MM-DD hh:mm";if(w(e)){var n=t.format(new Date(e)),r=n.split(", "),a=r[0].split("/"),o={YYYY:a[2],MM:a[0],DD:a[1],"hh:mm":"24"===r[1].match(/^[\d]{2}/)[0]?r[1].replace(/^[\d]{2}/,"00"):r[1]};return i.replace(/YYYY|MM|DD|(hh:mm)/g,(function(t){return o[t]}))}return"--"}function rt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,i=+t;return(i||0===i)&&w(i)?i.toFixed(e):"".concat(i)}function at(t){return w(+t)?t>1e9?"".concat(+(t/1e9).toFixed(3),"B"):t>1e6?"".concat(+(t/1e6).toFixed(3),"M"):t>1e3?"".concat(+(t/1e3).toFixed(3),"K"):t:"--"}function ot(t,e){return null==e&&(e=10),+(t=(+t).toFixed(e=Math.min(Math.max(0,e),20)))}function st(t){return Math.log(t)/Math.log(10)}function ct(t){return Math.pow(10,t)}var ht={ZOOM:"zoom",SCROLL:"scroll",CROSSHAIR:"crosshair",TOOLTIP:"tooltip",PANE_DRAG:"pane_drag"};function lt(t){return Object.values(ht).indexOf(t)>-1}var ut=function(){function t(e){p(this,t),this._chartStore=e,this._dateTimeFormat=new Intl.DateTimeFormat("en",{hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),this._zoomEnabled=!0,this._scrollEnabled=!0,this._loading=!0,this._loadMoreCallback=null,this._more=!0,this._totalDataSpace=0,this._dataSpace=6,this._barSpace=this._calcBarSpace(),this._offsetRightSpace=50,this._offsetRightBarCount=this._offsetRightSpace/this._dataSpace,this._leftMinVisibleBarCount=2,this._rightMinVisibleBarCount=2,this._from=0,this._to=0,this._preOffsetRightBarCount=0}return y(t,[{key:"_calcBarSpace",value:function(){return Math.max(1,Math.min(Math.floor(.82*this._dataSpace),Math.floor(this._dataSpace)-1))}},{key:"adjustFromTo",value:function(){var t=this._chartStore.dataList().length,e=this._totalDataSpace/this._dataSpace,i=e-Math.min(this._leftMinVisibleBarCount,t);this._offsetRightBarCount>i&&(this._offsetRightBarCount=i);var n=-t+Math.min(this._rightMinVisibleBarCount,t);n>this._offsetRightBarCount&&(this._offsetRightBarCount=n),this._to=Math.round(this._offsetRightBarCount+t+.5),this._from=Math.round(this._to-e)-1,this._to>t&&(this._to=t),0>this._from&&(this._from=0),this._chartStore.adjustVisibleDataList(),0===this._from&&this._more&&!this._loading&&k(this._loadMoreCallback)&&(this._loading=!0,this._loadMoreCallback(it(this._chartStore.dataList()[0],"timestamp")))}},{key:"setMore",value:function(t){this._more=t}},{key:"setLoading",value:function(t){this._loading=t}},{key:"dateTimeFormat",value:function(){return this._dateTimeFormat}},{key:"setTimezone",value:function(t){var e;try{e=new Intl.DateTimeFormat("en",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(t){}e&&(this._dateTimeFormat=e)}},{key:"timezone",value:function(){return this._dateTimeFormat.resolvedOptions().timeZone}},{key:"dataSpace",value:function(){return this._dataSpace}},{key:"barSpace",value:function(){return this._barSpace}},{key:"halfBarSpace",value:function(){return this._barSpace/2}},{key:"setDataSpace",value:function(t,e){1>t||t>50||this._dataSpace===t||(this._dataSpace=t,this._barSpace=this._calcBarSpace(),e&&e(),this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0),this._chartStore.invalidate())}},{key:"setTotalDataSpace",value:function(t){this._totalDataSpace!==t&&(this._totalDataSpace=t,this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0))}},{key:"setOffsetRightSpace",value:function(t,e){this._offsetRightSpace=t,this._offsetRightBarCount=t/this._dataSpace,e&&(this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0),this._chartStore.invalidate())}},{key:"resetOffsetRightSpace",value:function(){this.setOffsetRightSpace(this._offsetRightSpace)}},{key:"offsetRightSpace",value:function(){return this._offsetRightSpace}},{key:"offsetRightBarCount",value:function(){return this._offsetRightBarCount}},{key:"setOffsetRightBarCount",value:function(t){this._offsetRightBarCount=t}},{key:"setLeftMinVisibleBarCount",value:function(t){this._leftMinVisibleBarCount=t}},{key:"setRightMinVisibleBarCount",value:function(t){this._rightMinVisibleBarCount=t}},{key:"from",value:function(){return this._from}},{key:"to",value:function(){return this._to}},{key:"startScroll",value:function(){this._preOffsetRightBarCount=this._offsetRightBarCount}},{key:"scroll",value:function(t,e){if(this._scrollEnabled){var i=t/this._dataSpace;this._chartStore.actionStore().execute(ht.SCROLL,{barCount:i,distance:t}),this._offsetRightBarCount=this._preOffsetRightBarCount-i,this.adjustFromTo();var n=e||this._chartStore.crosshairStore().get();this._chartStore.crosshairStore().set(n,!0),this._chartStore.invalidate()}}},{key:"getDataByDataIndex",value:function(t){return this._chartStore.dataList()[t]}},{key:"coordinateToFloatIndex",value:function(t){var e=this._chartStore.dataList().length;return Math.round(1e6*(e+this._offsetRightBarCount-(this._totalDataSpace-t)/this._dataSpace))/1e6}},{key:"dataIndexToTimestamp",value:function(t){var e=this.getDataByDataIndex(t);if(e)return e.timestamp}},{key:"timestampToDataIndex",value:function(t){return 0===this._chartStore.dataList().length?0:function(t,e,i){var n=0,r=0;for(r=t.length-1;n!==r;){var a=Math.floor((r+n)/2),o=r-n,s=t[a][e];if(i===t[n][e])return n;if(i===t[r][e])return r;if(i===s)return a;if(i>s?n=a:r=a,2>=o)break}return n}(this._chartStore.dataList(),"timestamp",t)}},{key:"dataIndexToCoordinate",value:function(t){var e=this._chartStore.dataList().length;return this._totalDataSpace-(e+this._offsetRightBarCount-t-.5)*this._dataSpace}},{key:"coordinateToDataIndex",value:function(t){return Math.ceil(this.coordinateToFloatIndex(t))-1}},{key:"zoom",value:function(t,e){var i=this;if(this._zoomEnabled){if(!e||!E(e.x)){var n=this._chartStore.crosshairStore().get();e={x:E(n.x)?n.x:this._totalDataSpace/2}}this._chartStore.actionStore().execute(ht.ZOOM,{coordinate:e,scale:t});var r=this.coordinateToFloatIndex(e.x);this.setDataSpace(this._dataSpace+t*(this._dataSpace/10),(function(){i._offsetRightBarCount+=r-i.coordinateToFloatIndex(e.x)}))}}},{key:"setZoomEnabled",value:function(t){this._zoomEnabled=t}},{key:"zoomEnabled",value:function(){return this._zoomEnabled}},{key:"setScrollEnabled",value:function(t){this._scrollEnabled=t}},{key:"scrollEnabled",value:function(){return this._scrollEnabled}},{key:"setLoadMoreCallback",value:function(t){this._loadMoreCallback=t}},{key:"clear",value:function(){this._more=!0,this._loading=!0,this._from=0,this._to=0}}]),t}();function ft(t,e){return ft=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},ft(t,e)}function dt(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ft(t,e)}function vt(t,e){if(e&&("object"===m(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function pt(t){return pt=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},pt(t)}function _t(t,e,i,n,r,a,o){try{var s=t[a](o),c=s.value}catch(t){return void i(t)}s.done?e(c):Promise.resolve(c).then(n,r)}var yt={exports:{}};!function(t){var e=function(t){var e,i=Object.prototype,n=i.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function c(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,i){return t[e]=i}}function h(t,e,i,n){var r=Object.create((e&&e.prototype instanceof _?e:_).prototype),a=new A(n||[]);return r._invoke=function(t,e,i){var n=u;return function(r,a){if(n===d)throw Error("Generator is already running");if(n===v){if("throw"===r)throw a;return T()}for(i.method=r,i.arg=a;;){var o=i.delegate;if(o){var s=E(o,i);if(s){if(s===p)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===u)throw n=v,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=d;var c=l(t,e,i);if("normal"===c.type){if(n=i.done?v:f,c.arg===p)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n=v,i.method="throw",i.arg=c.arg)}}}(t,i,a),r}function l(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}t.wrap=h;var u="suspendedStart",f="suspendedYield",d="executing",v="completed",p={};function _(){}function y(){}function m(){}var x={};c(x,a,(function(){return this}));var g=Object.getPrototypeOf,S=g&&g(g(I([])));S&&S!==i&&n.call(S,a)&&(x=S);var k=m.prototype=_.prototype=Object.create(x);function b(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function i(r,a,o,s){var c=l(t[r],t,a);if("throw"!==c.type){var h=c.arg,u=h.value;return u&&"object"==typeof u&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(u).then((function(t){h.value=t,o(h)}),(function(t){return i("throw",t,o,s)}))}s(c.arg)}var r;this._invoke=function(t,n){function a(){return new e((function(e,r){i(t,n,e,r)}))}return r=r?r.then(a,a):a()}}function E(t,i){var n=t.iterator[i.method];if(n===e){if(i.delegate=null,"throw"===i.method){if(t.iterator.return&&(i.method="return",i.arg=e,E(t,i),"throw"===i.method))return p;i.method="throw",i.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=l(n,t.iterator,i.arg);if("throw"===r.type)return i.method="throw",i.arg=r.arg,i.delegate=null,p;var a=r.arg;return a?a.done?(i[t.resultName]=a.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,p):a:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,p)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function I(t){if(t){var i=t[a];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function i(){for(;++r<t.length;)if(n.call(t,r))return i.value=t[r],i.done=!1,i;return i.value=e,i.done=!0,i};return o.next=o}}return{next:T}}function T(){return{value:e,done:!0}}return y.prototype=m,c(k,"constructor",m),c(m,"constructor",y),y.displayName=c(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,c(t,s,"GeneratorFunction")),t.prototype=Object.create(k),t},t.awrap=function(t){return{__await:t}},b(w.prototype),c(w.prototype,o,(function(){return this})),t.AsyncIterator=w,t.async=function(e,i,n,r,a){void 0===a&&(a=Promise);var o=new w(h(e,i,n,r),a);return t.isGeneratorFunction(i)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},b(k),c(k,s,"Generator"),c(k,a,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var i in t)e.push(i);return e.reverse(),function i(){for(;e.length;){var n=e.pop();if(n in t)return i.value=n,i.done=!1,i}return i.done=!0,i}},t.values=I,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var i in this)"t"===i.charAt(0)&&n.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function r(n,r){return s.type="throw",s.arg=t,i.next=n,r&&(i.method="next",i.arg=e),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return r("end");if(this.prev>=o.tryLoc){var c=n.call(o,"catchLoc"),h=n.call(o,"finallyLoc");if(c&&h){if(o.catchLoc>this.prev)return r(o.catchLoc,!0);if(o.finallyLoc>this.prev)return r(o.finallyLoc)}else if(c){if(o.catchLoc>this.prev)return r(o.catchLoc,!0)}else{if(!h)throw Error("try statement without catch or finally");if(o.finallyLoc>this.prev)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(this.prev>=r.tryLoc&&n.call(r,"finallyLoc")&&r.finallyLoc>this.prev){var a=r;break}}!a||"break"!==t&&"continue"!==t||a.tryLoc>e||e>a.finallyLoc||(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),C(i),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var r=n.arg;C(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,i,n){return this.delegate={iterator:I(t),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=e),p}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}}(yt);var mt=yt.exports,xt="line",gt="bar",St="circle",kt={PRICE:"price",VOLUME:"volume",NORMAL:"normal"};function bt(t,e,i,n,r,a){var o=a.color,s=a.isStroke,c={prev:{kLineData:t[i-1],technicalIndicatorData:e[i-1]},current:{kLineData:t[i],technicalIndicatorData:e[i]},next:{kLineData:t[i+1],technicalIndicatorData:e[i+1]}};return E(n.color)&&(o=k(n.color)?n.color(c,r)||a.color:n.color||a.color),E(n.isStroke)&&(s=k(n.isStroke)?n.isStroke(c):n.isStroke),{color:o,isStroke:s}}var wt=function(){function t(e){var i=e.name,n=e.shortName,r=e.series,a=e.calcParams,o=e.plots,s=e.precision,c=e.shouldCheckParamCount,h=e.shouldOhlc,l=e.shouldFormatBigNumber,u=e.minValue,f=e.maxValue,d=e.styles;p(this,t),this.name=i||"",this.shortName=n||i||"",this.series=-1!==Object.values(kt).indexOf(r)?r:kt.NORMAL,this.precision=w(s)&&s>=0?s:4,this._precisionFlag=!1,this.calcParams=S(a)?a:[],this.plots=S(o)?o:[],this.shouldCheckParamCount=!P(c)||c,this.shouldOhlc=!!P(h)&&h,this.shouldFormatBigNumber=!!P(l)&&l,this.minValue=u,this.maxValue=f,this.styles=d,this.result=[]}var e,i;return y(t,[{key:"_createParams",value:function(t){return t.map((function(t){return b(t)?t.value:t}))}},{key:"setShortName",value:function(t){return this.shortName!==t&&(this.shortName=t,!0)}},{key:"setPrecision",value:function(t,e){return!(!w(t)||0>t||e&&(!e||this._precisionFlag))&&(this.precision=parseInt(t,10),e||(this._precisionFlag=!0),!0)}},{key:"setCalcParams",value:function(t){if(!S(t))return!1;if(this.shouldCheckParamCount&&t.length!==this.calcParams.length)return!1;for(var e=[],i=0;t.length>i;i++){var n=t[i],r=void 0,a=void 0;b(n)?(r=n.value,a=n.allowDecimal):(r=n,a=!1);var o=this.calcParams[i];if(b(o)&&P(o.allowDecimal)&&(a=o.allowDecimal),!w(r)||0>=r||!a&&parseInt(r,10)!==r)return!1;e.push({allowDecimal:a,value:r})}this.calcParams=e;var s=this.regeneratePlots(this._createParams(e));return s&&S(s)&&(this.plots=s),!0}},{key:"setShouldOhlc",value:function(t){return!(!P(t)||this.shouldOhlc===t)&&(this.shouldOhlc=t,!0)}},{key:"setShouldFormatBigNumber",value:function(t){return!(!P(t)||this.shouldFormatBigNumber===t)&&(this.shouldFormatBigNumber=t,!0)}},{key:"setStyles",value:function(t,e){return!!b(t)&&(this.styles||(this.styles={margin:g(e.margin),bar:g(e.bar),line:g(e.line),circle:g(e.circle)}),x(this.styles,t),!0)}},{key:"calc",value:(e=mt.mark((function t(e){return mt.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.calcTechnicalIndicator(e,{params:this._createParams(this.calcParams),plots:this.plots});case 2:if(t.t0=t.sent,t.t0){t.next=5;break}t.t0=[];case 5:this.result=t.t0;case 6:case"end":return t.stop()}}),t,this)})),i=function(){var t=this,i=arguments;return new Promise((function(n,r){var a=e.apply(t,i);function o(t){_t(a,n,r,o,s,"next",t)}function s(t){_t(a,n,r,o,s,"throw",t)}o(void 0)}))},function(t){return i.apply(this,arguments)})},{key:"calcTechnicalIndicator",value:function(t,e){}},{key:"regeneratePlots",value:function(t){}}]),t}();function Et(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return Pt(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Pt(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function Pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function Ct(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var At=function(){function t(e){p(this,t),this._chartStore=e,this._templates=this._createTemplates(),this._instances=new Map}return y(t,[{key:"_createTechInfo",value:function(t){return{name:t.name,shortName:t.shortName,series:t.series,calcParams:t.calcParams,shouldCheckParamCount:t.shouldCheckParamCount,shouldOhlc:t.shouldOhlc,shouldFormatBigNumber:t.shouldFormatBigNumber,precision:t.precision,styles:t.styles,plots:t.plots,result:t.result||[]}}},{key:"_createTemplates",value:function(){var t={},e=v.technicalIndicatorExtensions;for(var i in e){var n=this._createTemplateInstance(e[i]);n&&(t[i]=n)}return t}},{key:"_createTemplateInstance",value:function(t){var e=t.name,i=t.shortName,n=t.series,r=t.calcParams,a=t.plots,o=t.precision,s=t.shouldCheckParamCount,c=t.shouldOhlc,h=t.shouldFormatBigNumber,l=t.minValue,u=t.maxValue,f=t.styles,d=t.calcTechnicalIndicator,v=t.regeneratePlots,_=t.createToolTipDataSource,m=t.render;if(!e||!k(d))return null;var x=function(t){dt(v,t);var d=Ct(v);function v(){return p(this,v),d.call(this,{name:e,shortName:i,series:n,calcParams:r,plots:a,precision:o,shouldCheckParamCount:s,shouldOhlc:c,shouldFormatBigNumber:h,minValue:l,maxValue:u,styles:f})}return y(v)}(wt);return x.prototype.calcTechnicalIndicator=d,k(v)&&(x.prototype.regeneratePlots=v),k(_)&&(x.prototype.createToolTipDataSource=_),k(m)&&(x.prototype.render=m),new x}},{key:"addTemplate",value:function(t){var e=this;t.forEach((function(t){var i=e._createTemplateInstance(t||{});i&&(e._templates[i.name]=i)}))}},{key:"hasTemplate",value:function(t){return!!this._templates[t]}},{key:"getTemplateInfo",value:function(t){if(!E(t)){var e={};for(var i in this._templates){e[i]=this._createTechInfo(this._templates[i])}return e}var n=this._templates[t];return n?this._createTechInfo(n):{}}},{key:"addInstance",value:function(t,e,i){var n=e.name,r=e.calcParams,a=e.precision,o=e.shouldOhlc,s=e.shouldFormatBigNumber,c=e.styles,h=this._instances.get(t);if(!h||!h.has(n)){h||(h=new Map,this._instances.set(t,h));var l=this._templates[n],u=Object.create(Object.getPrototypeOf(l));for(var f in l)Object.prototype.hasOwnProperty.call(l,f)&&(u[f]=l[f]);return u.setCalcParams(r),u.setPrecision(a),u.setShouldOhlc(o),u.setShouldFormatBigNumber(s),u.setStyles(c,this._chartStore.styleOptions().technicalIndicator),i||h.clear(),h.set(n,u),u.calc(this._chartStore.dataList())}}},{key:"instances",value:function(t){return this._instances.get(t)||new Map}},{key:"removeInstance",value:function(t,e){var i=!1;if(this._instances.has(t)){var n=this._instances.get(t);E(e)?n.has(e)&&(n.delete(e),i=!0):(n.clear(),i=!0),0===n.size&&this._instances.delete(t)}return i}},{key:"hasInstance",value:function(t){return this._instances.has(t)}},{key:"calcInstance",value:function(t,e){var i=this,n=[];if(E(t))if(E(e)){var r=this._instances.get(e);r&&r.has(t)&&n.push(r.get(t).calc(this._chartStore.dataList()))}else this._instances.forEach((function(e){e.has(t)&&n.push(e.get(t).calc(i._chartStore.dataList()))}));else this._instances.forEach((function(t){t.forEach((function(t){n.push(t.calc(i._chartStore.dataList()))}))}));return Promise.all(n)}},{key:"getInstanceInfo",value:function(t,e){var i=this,n=function(t){var n,r=[],a=Et(t);try{for(a.s();!(n=a.n()).done;){var o=n.value[1];if(o){var s=i._createTechInfo(o);if(o.name===e)return s;r.push(s)}}}catch(t){a.e(t)}finally{a.f()}return r};if(!E(t)){var r={};return this._instances.forEach((function(t,e){r[e]=n(t)})),r}return this._instances.has(t)?n(this._instances.get(t)):{}}},{key:"setSeriesPrecision",value:function(t,e){var i=function(i){i.series===kt.PRICE&&i.setPrecision(t,!0),i.series===kt.VOLUME&&i.setPrecision(e,!0)};for(var n in this._templates)i(this._templates[n]);this._instances.forEach((function(t){t.forEach((function(t){i(t)}))}))}},{key:"override",value:function(t,e){var i=this,n=t.name,r=t.shortName,a=t.calcParams,o=t.precision,s=t.shouldOhlc,c=t.shouldFormatBigNumber,h=t.styles,l=this._chartStore.styleOptions().technicalIndicator,u=new Map;if(E(e))this._instances.has(e)&&u.set(e,this._instances.get(e));else{u=this._instances;var f=this._templates[n];f&&(f.setCalcParams(a),f.setShortName(r),f.setPrecision(o),f.setShouldOhlc(s),f.setShouldFormatBigNumber(c),f.setStyles(h,l))}var d=!1,v=[];if(u.forEach((function(t){if(t.has(n)){var e=t.get(n),u=e.setShortName(r),f=e.setCalcParams(a),p=e.setPrecision(o),_=e.setShouldOhlc(s),y=e.setShouldFormatBigNumber(c),m=e.setStyles(h,l);(u||f||p||_||y||m)&&(d=!0),f&&v.push(e.calc(i._chartStore.dataList()))}})),d)return Promise.all(v)}}]),t}();function It(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Tt(t,e){e.forEach((function(e){var i=e.key,n=e.fn;k(n)&&(t[i]=n)}))}var Mt=function(){function t(e){var i=e.id,n=e.chartStore,r=e.xAxis,a=e.yAxis;p(this,t),this._id=i,this._chartStore=n,this._xAxis=r,this._yAxis=a,this._styles=null}return y(t,[{key:"draw",value:function(t){}},{key:"setStyles",value:function(t,e){return!!b(t)&&(this._styles||(this._styles=g(e)),x(this._styles,t),!0)}},{key:"id",value:function(){return this._id}},{key:"styles",value:function(){return this._styles}},{key:"setYAxis",value:function(t){t&&(this._yAxis=t)}},{key:"checkEventCoordinateOn",value:function(t){}},{key:"onClick",value:function(t){}},{key:"onRightClick",value:function(t){}},{key:"onMouseEnter",value:function(t){}},{key:"onMouseLeave",value:function(t){}}]),t}();function Ot(t,e,i,n){t.fillStyle=e,t.beginPath(),t.arc(i.x,i.y,n,0,2*Math.PI),t.closePath(),t.fill()}function Dt(t,e,i){var n=Math.abs(e.x-t.x),r=Math.abs(e.y-t.y),a=Math.abs(i.x-t.x);return Math.abs(n*Math.abs(i.y-t.y)-a*r)/2}function Rt(t,e){var i=t.x-e.x;if(0!==i){var n=(t.y-e.y)/i;return{k:n,b:t.y-n*t.x}}}function Lt(t,e,i){return Bt(Rt(t,e),i)}function Bt(t,e){return t?e.x*t.k+t.b:e.y}function Ft(t,e,i){if(!i||!t||!e)return!1;if(t.x===e.x)return 2>Math.abs(i.x-t.x);var n=Rt(t,e),r=Bt(n,i),a=Math.abs(r-i.y);return 4>a*a/(n.k*n.k+1)}function zt(t,e,i){return!!Ft(t,e,i)&&(t.x===e.x?e.y>t.y?2>t.y-i.y:2>i.y-t.y:e.x>t.x?2>t.x-i.x:2>i.x-t.x)}function Vt(t,e,i){return!!Ft(t,e,i)&&(t.x===e.x?4>Math.abs(t.y-i.y)+Math.abs(e.y-i.y)-Math.abs(t.y-e.y):4>Math.abs(t.x-i.x)+Math.abs(e.x-i.x)-Math.abs(t.x-e.x))}function jt(t,e,i){if(!i)return!1;var n=i.x-t.x,r=i.y-t.y;return!(n*n+r*r>e*e)}function Ht(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=[];if(t.length>1)if(t[0].x===t[1].x){var r=0,a=e.y;if(n.push([{x:t[0].x,y:r},{x:t[0].x,y:a}]),t.length>2){n.push([{x:t[2].x,y:r},{x:t[2].x,y:a}]);for(var o=t[0].x-t[2].x,s=0;i>s;s++){var c=o*(s+1);n.push([{x:t[0].x+c,y:r},{x:t[0].x+c,y:a}])}}}else{var h=0,l=e.x,u=Rt(t[0],t[1]),f=u.k,d=u.b;if(n.push([{x:h,y:h*f+d},{x:l,y:l*f+d}]),t.length>2){var v=t[2].y-f*t[2].x;n.push([{x:h,y:h*f+v},{x:l,y:l*f+v}]);for(var p=d-v,_=0;i>_;_++){var y=d+p*(_+1);n.push([{x:h,y:h*f+y},{x:l,y:l*f+y}])}}}return n}function Nt(t,e,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),t.beginPath();var n=!0;e.forEach((function(e){e&&(n?(t.moveTo(e.x,e.y),n=!1):t.lineTo(e.x,e.y))})),i(),t.restore()}function Yt(t,e){Nt(t,e,(function(){t.closePath(),t.stroke()}))}function Wt(t,e){Nt(t,e,(function(){t.closePath(),t.fill()}))}function Xt(t,e,i,n){t.beginPath();var r=t.lineWidth%2?.5:0;t.moveTo(i,e+r),t.lineTo(n,e+r),t.stroke(),t.closePath()}function Gt(t,e,i,n){t.beginPath();var r=t.lineWidth%2?.5:0;t.moveTo(e+r,i),t.lineTo(e+r,n),t.stroke(),t.closePath()}function Ut(t,e){Nt(t,e,(function(){t.stroke(),t.closePath()}))}function Zt(t){return t.ownerDocument&&t.ownerDocument.defaultView&&t.ownerDocument.defaultView.devicePixelRatio||2}function Kt(t,e){return Math.round(t.measureText(e).width)}function qt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Helvetica Neue";return"".concat(e," ").concat(t,"px ").concat(i)}function $t(t,e,i){t.font=qt(i.size,i.weight,i.family);var n=Kt(t,e);return i.paddingLeft+i.paddingRight+n+2*(i.borderSize||0)}function Jt(t){return t.paddingTop+t.paddingBottom+t.size+2*(t.borderSize||0)}function Qt(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return te(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return te(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function te(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function ee(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var ie="other",ne="point",re="none",ae="line",oe="text",se="continuous_line",ce="polygon",he="arc",le=0,ue=1,fe=2,de={NORMAL:"normal",WEAK_MAGNET:"weak_magnet",STRONG_MAGNET:"strong_magnet"};var ve=function(t){dt(i,t);var e=ee(i);function i(t){var n,r=t.id,a=t.name,o=t.totalStep,s=t.chartStore,c=t.xAxis,h=t.yAxis,l=t.points,u=t.styles,f=t.lock,d=t.mode,v=t.data;return p(this,i),(n=e.call(this,{id:r,chartStore:s,xAxis:c,yAxis:h}))._name=a,n._totalStep=o,n._lock=f,n._mode=de.NORMAL,n.setMode(d),n._data=v,n._drawStep=1,n._points=[],n.setPoints(l),n.setStyles(u,s.styleOptions().shape),n._prevPressPoint=null,n._prevPoints=null,n._coordinates=[],n}return y(i,[{key:"setPoints",value:function(t){if(S(t)&&t.length>0){var e;this._totalStep-1>t.length?(this._drawStep=t.length+1,this._points=g(t),e=t.length):(this._drawStep=-1,this._points=t.slice(0,this._totalStep-1),e=this._totalStep-1);for(var i=0;e>i;i++)this.performEventMoveForDrawing({step:i+2,mode:this._mode,points:this._points,movePoint:this._points[i],xAxis:this._xAxis,yAxis:this._yAxis});-1===this._drawStep&&this.performEventPressedMove({mode:this._mode,points:this._points,pressPointIndex:this._points.length-1,pressPoint:this._points[this._points.length-1],xAxis:this._xAxis,yAxis:this._yAxis})}}},{key:"_timestampOrDataIndexToCoordinateX",value:function(t){var e=t.timestamp,i=t.dataIndex;return e&&(i=this._chartStore.timeScaleStore().timestampToDataIndex(e)),this._xAxis.convertToPixel(i)}},{key:"_drawLines",value:function(t,e,i,n){t.save(),t.strokeStyle=i.color||n.color,t.lineWidth=i.size||n.size,i.style===T&&t.setLineDash(i.dashValue||n.dashValue),e.forEach((function(e){var i,n;if(e.length>1)switch((i=e[0]).x===(n=e[1]).x?fe:i.y===n.y?ue:le){case le:Ut(t,e);break;case ue:Xt(t,e[0].y,e[0].x,e[1].x);break;case fe:Gt(t,e[0].x,e[0].y,e[1].y)}})),t.restore()}},{key:"_drawContinuousLines",value:function(t,e,i,n){t.save(),t.strokeStyle=i.color||n.color,t.lineWidth=i.size||n.size,i.style===T&&t.setLineDash(i.dashValue||n.dashValue),e.forEach((function(e){e.length>0&&Ut(t,e)})),t.restore()}},{key:"_drawPolygons",value:function(t,e,i,n){var r;if(t.save(),i.style===I)t.fillStyle=(i.fill||n.fill).color,r=Wt;else{var a=i.stroke||n.stroke;a.style===T&&t.setLineDash(a.dashValue),t.lineWidth=a.size,t.strokeStyle=a.color,r=Yt}e.forEach((function(e){e.length>0&&r(t,e)})),t.restore()}},{key:"_drawArcs",value:function(t,e,i,n){if(t.save(),i.style===I)t.fillStyle=(i.fill||n.fill).color;else{var r=i.stroke||n.stroke;r.style===T&&t.setLineDash(r.dashValue),t.lineWidth=r.size,t.strokeStyle=r.color}e.forEach((function(e){var n=e.x,r=e.y,a=e.radius,o=e.startAngle,s=e.endAngle;t.beginPath(),t.arc(n,r,a,o,s),i.style===I?(t.closePath(),t.fill()):(t.stroke(),t.closePath())})),t.restore()}},{key:"_drawText",value:function(t,e,i,n){var r;t.save(),i.style===A?(t.strokeStyle=i.color||n.color,r=t.strokeText):(t.fillStyle=i.color||n.color,r=t.fillText),t.font=qt(i.size||n.size,i.family||n.family,i.weight||n.weight);var a=i.offset||n.offset||[0,0];e.forEach((function(e){r.call(t,e.text,e.x+a[1],e.y+a[0])})),t.restore()}},{key:"draw",value:function(t){var e=this;this._coordinates=this._points.map((function(t){var i=t.value;return{x:e._timestampOrDataIndexToCoordinateX({timestamp:t.timestamp,dataIndex:t.dataIndex}),y:e._yAxis.convertToPixel(i)}}));var i=this._styles||this._chartStore.styleOptions().shape;if(1!==this._drawStep&&this._coordinates.length>0){var n={width:this._xAxis.width(),height:this._yAxis.height()},r={price:this._chartStore.pricePrecision(),volume:this._chartStore.volumePrecision()};this._shapeDataSources=this.createShapeDataSource({step:this._drawStep,mode:this._mode,points:this._points,coordinates:this._coordinates,viewport:{width:this._xAxis.width(),height:this._yAxis.height()},precision:{price:this._chartStore.pricePrecision(),volume:this._chartStore.volumePrecision()},styles:i,xAxis:this._xAxis,yAxis:this._yAxis,data:this._data})||[],this._shapeDataSources.forEach((function(n){var r=n.styles,a=n.dataSource,o=void 0===a?[]:a;if(n.isDraw)switch(n.type){case ae:e._drawLines(t,o,r||i.line,i.line);break;case se:e._drawContinuousLines(t,o,r||i.line,i.line);break;case ce:e._drawPolygons(t,o,r||i.polygon,i.polygon);break;case he:e._drawArcs(t,o,r||i.arc,i.arc);break;case oe:e._drawText(t,o,r||i.text,i.text)}})),this.drawExtend&&(t.save(),this.drawExtend({ctx:t,dataSource:this._shapeDataSources,styles:i,viewport:n,precision:r,mode:this._mode,xAxis:this._xAxis,yAxis:this._yAxis,data:this._data}),t.restore())}var a=this._chartStore.shapeStore().mouseOperate();(a.hover.id===this._id&&a.hover.element!==re||a.click.id===this._id&&a.click.element!==re||this.isDrawing())&&this._coordinates.forEach((function(n,r){var o=n.x,s=n.y,c=i.point.radius,h=i.point.backgroundColor,l=i.point.borderColor,u=i.point.borderSize;a.hover.id===e._id&&a.hover.element===ne&&r===a.hover.elementIndex&&(c=i.point.activeRadius,h=i.point.activeBackgroundColor,l=i.point.activeBorderColor,u=i.point.activeBorderSize),Ot(t,l,{x:o,y:s},c+u),Ot(t,h,{x:o,y:s},c)}))}},{key:"setLock",value:function(t){this._lock=t}},{key:"name",value:function(){return this._name}},{key:"lock",value:function(){return this._lock}},{key:"totalStep",value:function(){return this._totalStep}},{key:"mode",value:function(){return this._mode}},{key:"setMode",value:function(t){Object.values(de).indexOf(t)>-1&&(this._mode=t)}},{key:"setData",value:function(t){return t!==this._data&&(this._data=t,!0)}},{key:"data",value:function(){return this._data}},{key:"points",value:function(){return this._points}},{key:"isDrawing",value:function(){return-1!==this._drawStep}},{key:"isStart",value:function(){return 1===this._drawStep}},{key:"checkEventCoordinateOn",value:function(t){for(var e=this._styles||this._chartStore.styleOptions().shape,i=this._coordinates.length-1;i>-1;i--)if(jt(this._coordinates[i],e.point.radius,t))return{id:this._id,element:ne,elementIndex:i,instance:this};if(this._shapeDataSources){var n,r=Qt(this._shapeDataSources);try{for(r.s();!(n=r.n()).done;){var a=n.value,o=a.key,s=a.type,c=a.dataSource,h=void 0===c?[]:c;if(a.isCheck)for(var l=0;h.length>l;l++){if(this.checkEventCoordinateOnShape({key:o,type:s,dataSource:h[l],eventCoordinate:t}))return{id:this._id,element:ie,elementIndex:l,instance:this}}}}catch(t){r.e(t)}finally{r.f()}}}},{key:"_performValue",value:function(t,e,i){var n=this._yAxis.convertFromPixel(t);if(this._mode===de.NORMAL||"candle_pane"!==i)return n;var r=this._chartStore.timeScaleStore().getDataByDataIndex(e);if(!r)return n;if(n>r.high){if(this._mode===de.WEAK_MAGNET){var a=this._yAxis.convertToPixel(r.high);return this._yAxis.convertFromPixel(a-8)>n?r.high:n}return r.high}if(r.low>n){if(this._mode===de.WEAK_MAGNET){var o=this._yAxis.convertToPixel(r.low);return n>this._yAxis.convertFromPixel(o-8)?r.low:n}return r.low}var s=Math.max(r.open,r.close);if(n>s)return r.high-n>n-s?s:r.high;var c=Math.min(r.open,r.close);return c>n?c-n>n-r.low?r.low:c:n-c>s-n?s:c}},{key:"mouseMoveForDrawing",value:function(t,e){var i=this._xAxis.convertFromPixel(t.x),n=this._chartStore.timeScaleStore().dataIndexToTimestamp(i),r=this._performValue(t.y,i,e.paneId);this._points[this._drawStep-1]={timestamp:n,value:r,dataIndex:i},this.performEventMoveForDrawing({step:this._drawStep,mode:this._mode,points:this._points,movePoint:{timestamp:n,value:r,dataIndex:i},xAxis:this._xAxis,yAxis:this._yAxis}),this.onDrawing({id:this._id,step:this._drawStep,points:this._points})}},{key:"mouseLeftButtonDownForDrawing",value:function(){this._drawStep===this._totalStep-1?(this._drawStep=-1,this._chartStore.shapeStore().progressInstanceComplete(),this.onDrawEnd({id:this._id,points:this._points})):this._drawStep++}},{key:"mousePressedPointMove",value:function(t,e){var i=this._chartStore.shapeStore().mouseOperate(),n=i.click.elementIndex;if(!this._lock&&i.click.id===this._id&&i.click.element===ne&&-1!==n){var r=this._xAxis.convertFromPixel(t.x),a=this._chartStore.timeScaleStore().dataIndexToTimestamp(r),o=this._performValue(t.y,r,e.paneId);this._points[n].timestamp=a,this._points[n].dataIndex=r,this._points[n].value=o,this.performEventPressedMove({points:this._points,mode:this._mode,pressPointIndex:n,pressPoint:{dataIndex:r,timestamp:a,value:o},xAxis:this._xAxis,yAxis:this._yAxis}),this.onPressedMove({id:this._id,element:ne,points:this._points,event:e})}}},{key:"startPressedOtherMove",value:function(t){var e=this._xAxis.convertFromPixel(t.x),i=this._yAxis.convertFromPixel(t.y);this._prevPressPoint={dataIndex:e,value:i},this._prevPoints=g(this._points)}},{key:"mousePressedOtherMove",value:function(t,e){var i=this;if(this._prevPressPoint){var n=this._xAxis.convertFromPixel(t.x),r=this._yAxis.convertFromPixel(t.y),a=n-this._prevPressPoint.dataIndex,o=r-this._prevPressPoint.value;this._points=this._prevPoints.map((function(t){E(t.dataIndex)||(t.dataIndex=i._chartStore.timeScaleStore().timestampToDataIndex(t.timestamp));var e=t.dataIndex+a;return{dataIndex:e,value:t.value+o,timestamp:i._chartStore.timeScaleStore().dataIndexToTimestamp(e)}})),this.onPressedMove({id:this._id,element:ie,points:this._points,event:e})}}},{key:"onDrawStart",value:function(t){}},{key:"onDrawing",value:function(t){}},{key:"onDrawEnd",value:function(t){}},{key:"onPressedMove",value:function(t){}},{key:"onRemove",value:function(t){}},{key:"checkEventCoordinateOnShape",value:function(t){}},{key:"createShapeDataSource",value:function(t){}},{key:"performEventMoveForDrawing",value:function(t){}},{key:"performEventPressedMove",value:function(t){}}]),i}(Mt),pe=1,_e=2,ye=3;function me(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function xe(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?me(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):me(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function ge(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return Se(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Se(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function Se(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function ke(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var be=function(){function t(e){p(this,t),this._chartStore=e,this._templates=this._createTemplates(),this._mouseOperate={click:{id:"",element:re,elementIndex:-1},hover:{id:"",element:re,elementIndex:-1}},this._progressInstance=null,this._pressedInstance=null,this._instances=new Map}return y(t,[{key:"_createTemplates",value:function(){var t={},e=v.shapeExtensions;for(var i in e){var n=this._createTemplateClass(e[i]);n&&(t[i]=n)}return t}},{key:"_createTemplateClass",value:function(t){var e=t.name,i=t.totalStep,n=t.checkEventCoordinateOnShape,r=t.createShapeDataSource,a=t.performEventPressedMove,o=t.performEventMoveForDrawing,s=t.drawExtend;if(!(e&&w(i)&&k(n)&&k(r)))return null;var c=function(t){dt(r,t);var n=ke(r);function r(t){var a=t.id,o=t.chartStore,s=t.xAxis,c=t.yAxis,h=t.points,l=t.styles,u=t.lock,f=t.mode,d=t.data;return p(this,r),n.call(this,{id:a,name:e,totalStep:i,chartStore:o,xAxis:s,yAxis:c,points:h,styles:l,lock:u,mode:f,data:d})}return y(r)}(ve);return c.prototype.checkEventCoordinateOnShape=n,c.prototype.createShapeDataSource=r,k(a)&&(c.prototype.performEventPressedMove=a),k(o)&&(c.prototype.performEventMoveForDrawing=o),k(s)&&(c.prototype.drawExtend=s),c}},{key:"addTemplate",value:function(t){var e=this;t.forEach((function(t){var i=e._createTemplateClass(t);i&&(e._templates[t.name]=i)}))}},{key:"getTemplate",value:function(t){return this._templates[t]}},{key:"getInstance",value:function(t){var e,i=ge(this._instances);try{for(i.s();!(e=i.n()).done;){var n=(e.value[1]||[]).find((function(e){return e.id()===t}));if(n)return n}}catch(t){i.e(t)}finally{i.f()}return null}},{key:"hasInstance",value:function(t){return!!this.getInstance(t)}},{key:"addInstance",value:function(t,e){t.isDrawing()?this._progressInstance={paneId:e,instance:t,fixed:E(e)}:(this._instances.has(e)||this._instances.set(e,[]),this._instances.get(e).push(t)),this._chartStore.invalidate(pe)}},{key:"progressInstance",value:function(){return this._progressInstance||{}}},{key:"progressInstanceComplete",value:function(){var t=this.progressInstance(),e=t.instance,i=t.paneId;e&&!e.isDrawing()&&(this._instances.has(i)||this._instances.set(i,[]),this._instances.get(i).push(e),this._progressInstance=null)}},{key:"updateProgressInstance",value:function(t,e){var i=this.progressInstance(),n=i.instance;n&&!i.fixed&&(n.setYAxis(t),this._progressInstance.paneId=e)}},{key:"pressedInstance",value:function(){return this._pressedInstance||{}}},{key:"updatePressedInstance",value:function(t,e,i){this._pressedInstance=t?{instance:t,paneId:e,element:i}:null}},{key:"instances",value:function(t){return this._instances.get(t)||[]}},{key:"setInstanceOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.id,i=t.styles,n=t.lock,r=t.mode,a=t.data,o=this._chartStore.styleOptions().shape,s=!1;if(E(e)){var c=this.getInstance(e);c&&(c.setLock(n),c.setMode(r),(c.setStyles(i,o)||c.setData(a))&&(s=!0))}else this._instances.forEach((function(t){t.forEach((function(t){t.setLock(n),t.setMode(r),(t.setStyles(i,o)||t.setData(a))&&(s=!0)}))}));s&&this._chartStore.invalidate(pe)}},{key:"getInstanceInfo",value:function(t){var e=function(t){return{name:t.name(),id:t.id(),totalStep:t.totalStep(),lock:t.lock(),mode:t.mode(),points:t.points(),styles:t.styles(),data:t.data()}},i=this.progressInstance();if(!E(t)){var n={};return this._instances.forEach((function(t,r){n[r]=t.map((function(t){return e(t)})),i.paneId===r&&i.instance&&n[r].push(e(i.instance))})),n}if(i.instance&&i.instance.id()===t)return e(i.instance);var r=this.getInstance(t);return r?e(r):null}},{key:"removeInstance",value:function(t){var e=!1,i=this.progressInstance().instance;if(!i||E(t)&&i.id()!==t||(i.onRemove({id:i.id()}),this._progressInstance=null,e=!0),E(t)){var n,r=ge(this._instances);try{for(r.s();!(n=r.n()).done;){var a=n.value,o=a[1]||[],s=o.findIndex((function(e){return e.id()===t}));if(s>-1){o[s].onRemove({id:o[s].id()}),o.splice(s,1),0===o.length&&this._instances.delete(a[0]),e=!0;break}}}catch(t){r.e(t)}finally{r.f()}}else this._instances.forEach((function(t){t.length>0&&t.forEach((function(t){t.onRemove({id:t.id()})}))})),this._instances.clear(),e=!0;e&&this._chartStore.invalidate(pe)}},{key:"mouseOperate",value:function(){return this._mouseOperate}},{key:"setMouseOperate",value:function(t){var e=this._mouseOperate,i=e.hover,n=e.click;!t.hover||i.id===t.hover.id&&i.element===t.hover.element&&i.elementIndex===t.hover.elementIndex||(this._mouseOperate.hover=xe({},t.hover)),!t.click||n.id===t.click.id&&n.element===t.click.element&&n.elementIndex===t.click.elementIndex||(this._mouseOperate.click=xe({},t.click))}},{key:"isEmpty",value:function(){return 0===this._instances.size&&!this.progressInstance().instance}},{key:"isDrawing",value:function(){var t=this.progressInstance().instance;return t&&t.isDrawing()}},{key:"isPressed",value:function(){return!!this.pressedInstance().instance}}]),t}();function we(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return Ee(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Ee(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function Ee(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function Pe(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}var Ce=function(){function t(e){p(this,t),this._chartStore=e,this._annotations=new Map,this._visibleAnnotations=new Map,this._mouseOperate={id:""}}return y(t,[{key:"mouseOperate",value:function(){return this._mouseOperate}},{key:"setMouseOperate",value:function(t){t&&this._mouseOperate.id!==t.id&&(this._mouseOperate=function(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Pe(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Pe(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({},t))}},{key:"createVisibleAnnotations",value:function(){var t=this;this._visibleAnnotations.clear(),this._annotations.size>0&&this._chartStore.visibleDataList().forEach((function(e){var i=e.data,n=e.x;t._annotations.forEach((function(e,r){if(e.size>0){var a=e.get(i.timestamp)||[];if(a.length>0){var o,s=we(a);try{for(s.s();!(o=s.n()).done;){var c=o.value;c.createSymbolCoordinate(n),t._visibleAnnotations.has(r)?t._visibleAnnotations.get(r).push(c):t._visibleAnnotations.set(r,[c])}}catch(t){s.e(t)}finally{s.f()}}}}))}))}},{key:"add",value:function(t,e){var i=this;this._annotations.has(e)||this._annotations.set(e,new Map),t.forEach((function(t){var n=i._annotations.get(e);n.has(t.id())?n.get(t.id()).push(t):n.set(t.id(),[t])})),this.createVisibleAnnotations(),this._chartStore.invalidate(pe)}},{key:"get",value:function(t){return this._visibleAnnotations.get(t)}},{key:"remove",value:function(t,e){var i=!1;if(E(t)){if(this._annotations.has(t))if(E(e)){var n=this._annotations.get(t);[].concat(e).forEach((function(t){var e=t.timestamp;n.has(e)&&(i=!0,n.delete(e))})),0===n.size&&this._annotations.delete(t),i&&this.createVisibleAnnotations()}else i=!0,this._annotations.delete(t),this._visibleAnnotations.delete(t)}else i=!0,this._annotations.clear(),this._visibleAnnotations.clear();i&&this._chartStore.invalidate(pe)}},{key:"isEmpty",value:function(){return 0===this._visibleAnnotations.size}}]),t}(),Ae=function(){function t(e){p(this,t),this._chartStore=e,this._tags=new Map}return y(t,[{key:"_getById",value:function(t,e){var i=this.get(e);return i?i.get(t):null}},{key:"has",value:function(t,e){return!!this._getById(t,e)}},{key:"update",value:function(t,e,i){var n=this._getById(t,e);return!!n&&n.update(i)}},{key:"get",value:function(t){return this._tags.get(t)}},{key:"add",value:function(t,e){this._tags.has(e)||this._tags.set(e,new Map);var i=this._tags.get(e);t.forEach((function(t){i.set(t.id(),t)})),this._chartStore.invalidate(pe)}},{key:"remove",value:function(t,e){var i=!1;if(E(t)){if(this._tags.has(t))if(E(e)){var n=this._tags.get(t);[].concat(e).forEach((function(t){n.has(t)&&(i=!0,n.delete(t))})),0===n.size&&this._tags.delete(t)}else i=!0,this._tags.delete(t)}else i=!0,this._tags.clear();i&&this._chartStore.invalidate(pe)}}]),t}();function Ie(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Te(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Ie(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ie(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var Me=function(){function t(e){p(this,t),this._chartStore=e,this._crosshair={}}return y(t,[{key:"set",value:function(t,e){var i,n,r=this._chartStore.dataList(),a=t||{},o=r[n=E(a.x)?0>(i=this._chartStore.timeScaleStore().coordinateToDataIndex(a.x))?0:i>r.length-1?r.length-1:i:i=r.length-1],s=this._chartStore.timeScaleStore().dataIndexToCoordinate(i),c=this._crosshair.x,h=this._crosshair.y,l=this._crosshair.paneId;this._crosshair=Te(Te({},a),{},{realX:s,kLineData:o,realDataIndex:i,dataIndex:n}),o&&this._chartStore.crosshairChange(this._crosshair),c===a.x&&h===a.y&&l===a.paneId||e||this._chartStore.invalidate(pe)}},{key:"recalculate",value:function(t){this.set(this._crosshair,t)}},{key:"get",value:function(){return this._crosshair}}]),t}(),Oe=function(){function t(){p(this,t),this._observers=[]}return y(t,[{key:"subscribe",value:function(t){0>this._observers.indexOf(t)&&this._observers.push(t)}},{key:"unsubscribe",value:function(t){var e=this._observers.indexOf(t);e>-1?this._observers.splice(e,1):this._observers=[]}},{key:"execute",value:function(t){this._observers.forEach((function(e){e(t)}))}},{key:"hasObservers",value:function(){return this._observers.length>0}}]),t}(),De=function(){function t(){p(this,t),this._delegates=new Map}return y(t,[{key:"execute",value:function(t,e){this.has(t)&&this._delegates.get(t).execute(e)}},{key:"has",value:function(t){return this._delegates.has(t)&&this._delegates.get(t).hasObservers()}},{key:"subscribe",value:function(t,e){return!!lt(t)&&(this._delegates.has(t)||this._delegates.set(t,new Oe),this._delegates.get(t).subscribe(e),!0)}},{key:"unsubscribe",value:function(t,e){if(lt(t)){var i=this._delegates.get(t);return i.unsubscribe(e),i.hasObservers()||this._delegates.delete(t),!0}return!1}}]),t}(),Re=function(){function t(e,i){p(this,t),this._handler=i,this._styleOptions=g(et),x(this._styleOptions,e),this._pricePrecision=2,this._volumePrecision=0,this._dataList=[],this._visibleDataList=[],this._dragPaneFlag=!1,this._timeScaleStore=new ut(this),this._technicalIndicatorStore=new At(this),this._shapeStore=new be(this),this._annotationStore=new Ce(this),this._tagStore=new Ae(this),this._crosshairStore=new Me(this),this._actionStore=new De}return y(t,[{key:"adjustVisibleDataList",value:function(){this._visibleDataList=[];for(var t=this._timeScaleStore.from(),e=this._timeScaleStore.to(),i=t;e>i;i++){var n=this._dataList[i],r=this._timeScaleStore.dataIndexToCoordinate(i);this._visibleDataList.push({index:i,x:r,data:n})}this._annotationStore.createVisibleAnnotations()}},{key:"styleOptions",value:function(){return this._styleOptions}},{key:"applyStyleOptions",value:function(t){x(this._styleOptions,t)}},{key:"pricePrecision",value:function(){return this._pricePrecision}},{key:"volumePrecision",value:function(){return this._volumePrecision}},{key:"setPriceVolumePrecision",value:function(t,e){this._pricePrecision=t,this._volumePrecision=e,this._technicalIndicatorStore.setSeriesPrecision(t,e)}},{key:"dataList",value:function(){return this._dataList}},{key:"visibleDataList",value:function(){return this._visibleDataList}},{key:"addData",value:function(t,e,i){if(b(t)){if(S(t)){this._timeScaleStore.setLoading(!1),this._timeScaleStore.setMore(!P(i)||i);var n=0===this._dataList.length;this._dataList=t.concat(this._dataList),n&&this._timeScaleStore.resetOffsetRightSpace(),this._timeScaleStore.adjustFromTo()}else{if(e<this._dataList.length)this._dataList[e]=t,this.adjustVisibleDataList();else{this._dataList.push(t);var r=this._timeScaleStore.offsetRightBarCount();0>r&&this._timeScaleStore.setOffsetRightBarCount(--r),this._timeScaleStore.adjustFromTo()}}this._crosshairStore.recalculate(!0)}}},{key:"clearDataList",value:function(){this._dataList=[],this._visibleDataList=[],this._timeScaleStore.clear()}},{key:"timeScaleStore",value:function(){return this._timeScaleStore}},{key:"technicalIndicatorStore",value:function(){return this._technicalIndicatorStore}},{key:"shapeStore",value:function(){return this._shapeStore}},{key:"annotationStore",value:function(){return this._annotationStore}},{key:"tagStore",value:function(){return this._tagStore}},{key:"crosshairStore",value:function(){return this._crosshairStore}},{key:"actionStore",value:function(){return this._actionStore}},{key:"invalidate",value:function(t){this._handler.invalidate(t)}},{key:"crosshairChange",value:function(t){this._handler.crosshair(t)}},{key:"dragPaneFlag",value:function(){return this._dragPaneFlag}},{key:"setDragPaneFlag",value:function(t){this._dragPaneFlag=t}}]),t}();function Le(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=pt(t)););return t}function Be(){return Be="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var n=Le(t,e);if(n){var r=Object.getOwnPropertyDescriptor(n,e);return r.get?r.get.call(3>arguments.length?t:i):r.value}},Be.apply(this,arguments)}function Fe(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=document.createElement(t);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i.style[n]=e[n]);return i}var ze=function(){function t(e){p(this,t),this._height=-1,this._container=e.container,this._chartStore=e.chartStore,this._initBefore(e),this._initElement(),this._mainWidget=this._createMainWidget(this._element,e),this._yAxisWidget=this._createYAxisWidget(this._element,e)}return y(t,[{key:"_initBefore",value:function(t){}},{key:"_initElement",value:function(){this._element=Fe("div",{width:"100%",margin:"0",padding:"0",position:"relative",overflow:"hidden",boxSizing:"border-box"});var t=this._container.lastChild;t?this._container.insertBefore(this._element,t):this._container.appendChild(this._element)}},{key:"_createMainWidget",value:function(t,e){}},{key:"_createYAxisWidget",value:function(t,e){}},{key:"width",value:function(){return this._element.offsetWidth}},{key:"setWidth",value:function(t,e){this._mainWidget.setWidth(t),this._yAxisWidget&&this._yAxisWidget.setWidth(e)}},{key:"height",value:function(){return this._height}},{key:"setHeight",value:function(t){this._height=t,this._mainWidget.setHeight(t),this._yAxisWidget&&this._yAxisWidget.setHeight(t)}},{key:"setOffsetLeft",value:function(t,e){this._mainWidget.setOffsetLeft(t),this._yAxisWidget&&this._yAxisWidget.setOffsetLeft(e)}},{key:"layout",value:function(){this._element.offsetHeight!==this._height&&(this._element.style.height="".concat(this._height,"px")),this._mainWidget.layout(),this._yAxisWidget&&this._yAxisWidget.layout()}},{key:"invalidate",value:function(t){this._yAxisWidget&&this._yAxisWidget.invalidate(t),this._mainWidget.invalidate(t)}},{key:"createHtml",value:function(t){var e=t.id,i=t.content,n=t.style;return"yAxis"===t.position?this._yAxisWidget&&this._yAxisWidget.createHtml({id:e,content:i,style:n}):this._mainWidget.createHtml({id:e,content:i,style:n})}},{key:"removeHtml",value:function(t){this._yAxisWidget&&this._yAxisWidget.removeHtml(t),this._mainWidget.removeHtml(t)}},{key:"getImage",value:function(t){var e=this._element.offsetWidth,i=this._element.offsetHeight,n=Fe("canvas",{width:"".concat(e,"px"),height:"".concat(i,"px"),boxSizing:"border-box"}),r=n.getContext("2d"),a=Zt(n);n.width=e*a,n.height=i*a,r.scale(a,a);var o=this._mainWidget.getElement(),s=o.offsetWidth,c=o.offsetHeight,h=parseInt(o.style.left,10);if(r.drawImage(this._mainWidget.getImage(t),h,0,s,c),this._yAxisWidget){var l=this._yAxisWidget.getElement(),u=l.offsetWidth,f=l.offsetHeight,d=parseInt(l.style.left,10);r.drawImage(this._yAxisWidget.getImage(t),d,0,u,f)}return n}},{key:"destroy",value:function(){this._container.removeChild(this._element)}}]),t}();function Ve(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}var je=function(){function t(e){p(this,t),this._width=0,this._height=0,this._initElement(e.container),this._mainView=this._createMainView(this._element,e),this._overlayView=this._createOverlayView(this._element,e),this._htmlBaseId=0,this._htmls=new Map}return y(t,[{key:"_initElement",value:function(t){this._element=Fe("div",{margin:"0",padding:"0",position:"absolute",top:"0",overflow:"hidden",boxSizing:"border-box"}),t.appendChild(this._element)}},{key:"_createMainView",value:function(t,e){}},{key:"_createOverlayView",value:function(t,e){}},{key:"createHtml",value:function(t){var e=t.id,i=t.content,n=t.style,r=Fe("div",function(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Ve(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ve(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({boxSizing:"border-box",position:"absolute",zIndex:12},void 0===n?{}:n));if(C(i)){var a=i.replace(/(^\s*)|(\s*$)/g,"");r.innerHTML=a}else r.appendChild(i);var o=e||"html_".concat(++this._htmlBaseId);return this._htmls.has(o)?this._element.replaceChild(r,this._htmls.get(o)):this._element.appendChild(r),this._htmls.set(o,r),o}},{key:"removeHtml",value:function(t){var e=this;t?[].concat(t).forEach((function(t){var i=e._htmls.get(t);i&&(e._element.removeChild(i),e._htmls.delete(t))})):(this._htmls.forEach((function(t){e._element.removeChild(t)})),this._htmls.clear())}},{key:"getElement",value:function(){return this._element}},{key:"setWidth",value:function(t){this._width=t,this._mainView.setWidth(t),this._overlayView.setWidth(t)}},{key:"setHeight",value:function(t){this._height=t,this._mainView.setHeight(t),this._overlayView.setHeight(t)}},{key:"setOffsetLeft",value:function(t){this._element.style.left="".concat(t,"px")}},{key:"layout",value:function(){this._element.offsetWidth!==this._width&&(this._element.style.width="".concat(this._width,"px")),this._element.offsetHeight!==this._height&&(this._element.style.height="".concat(this._height,"px")),this._mainView.layout(),this._overlayView.layout()}},{key:"invalidate",value:function(t){switch(t){case pe:this._overlayView.flush();break;case _e:case ye:this._mainView.flush(),this._overlayView.flush()}}},{key:"getImage",value:function(t){var e=Fe("canvas",{width:"".concat(this._width,"px"),height:"".concat(this._height,"px"),boxSizing:"border-box"}),i=e.getContext("2d"),n=Zt(e);return e.width=this._width*n,e.height=this._height*n,i.scale(n,n),i.drawImage(this._mainView.getImage(),0,0,this._width,this._height),t&&this._overlayView&&i.drawImage(this._overlayView.getImage(),0,0,this._width,this._height),e}}]),t}();function He(t){return window.requestAnimationFrame?window.requestAnimationFrame(t):window.setTimeout(t,20)}var Ne=function(){function t(e,i){p(this,t),this._chartStore=i,this._initCanvas(e)}return y(t,[{key:"_initCanvas",value:function(t){this._canvas=Fe("canvas",{position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"}),this._ctx=this._canvas.getContext("2d"),t.appendChild(this._canvas)}},{key:"_redraw",value:function(t){this._ctx.clearRect(0,0,this._canvas.offsetWidth,this._canvas.offsetHeight),t&&t(),this._draw()}},{key:"_draw",value:function(){}},{key:"setWidth",value:function(t){this._width=t}},{key:"setHeight",value:function(t){this._height=t}},{key:"layout",value:function(){var t=this;this._height!==this._canvas.offsetHeight||this._width!==this._canvas.offsetWidth?this._redraw((function(){var e=Zt(t._canvas);t._canvas.style.width="".concat(t._width,"px"),t._canvas.style.height="".concat(t._height,"px"),t._canvas.width=Math.floor(t._width*e),t._canvas.height=Math.floor(t._height*e),t._ctx.scale(e,e)})):this.flush()}},{key:"flush",value:function(){var t,e=this;this.requestAnimationId&&(t=this.requestAnimationId,window.cancelAnimationFrame||clearTimeout(t),window.cancelAnimationFrame(t),this.requestAnimationId=null),this.requestAnimationId=He((function(){e._redraw()}))}},{key:"getImage",value:function(){return this._canvas}}]),t}();function Ye(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var We=function(t){dt(i,t);var e=Ye(i);function i(t,n,r,a,o){var s;return p(this,i),(s=e.call(this,t,n))._xAxis=r,s._yAxis=a,s._paneId=o,s}return y(i,[{key:"_draw",value:function(){this._ctx.globalCompositeOperation="destination-over",this._drawContent()}},{key:"_drawContent",value:function(){this._drawTechs(),this._drawGrid()}},{key:"_drawGrid",value:function(){var t=this,e=this._chartStore.styleOptions().grid;if(e.show){var i=e.horizontal;this._ctx.save(),i.show&&(this._ctx.strokeStyle=i.color,this._ctx.lineWidth=i.size,this._ctx.setLineDash(i.style===T?i.dashValue:[]),this._yAxis.ticks().forEach((function(e){Xt(t._ctx,e.y,0,t._width)})));var n=e.vertical;n.show&&(this._ctx.strokeStyle=n.color,this._ctx.lineWidth=n.size,this._ctx.setLineDash(n.style===T?n.dashValue:[]),this._xAxis.ticks().forEach((function(e){Gt(t._ctx,e.x,0,t._height)}))),this._ctx.restore()}}},{key:"_drawTechs",value:function(){var t=this;this._ctx.globalCompositeOperation="source-over";var e=this._chartStore.styleOptions().technicalIndicator;this._chartStore.technicalIndicatorStore().instances(this._paneId).forEach((function(i){var n=i.plots,r=[],a=t._chartStore.dataList(),o=i.result,s=i.styles||e;i.render&&(t._ctx.save(),i.render({ctx:t._ctx,dataSource:{from:t._chartStore.timeScaleStore().from(),to:t._chartStore.timeScaleStore().to(),kLineDataList:t._chartStore.dataList(),technicalIndicatorDataList:o},viewport:{width:t._width,height:t._height,dataSpace:t._chartStore.timeScaleStore().dataSpace(),barSpace:t._chartStore.timeScaleStore().barSpace()},styles:s,xAxis:t._xAxis,yAxis:t._yAxis}),t._ctx.restore());var c=s.line.colors||[],h=c.length,l=t._yAxis.isCandleYAxis();t._ctx.lineWidth=1,t._drawGraphics((function(e,u,f,d,v){var p=o[u]||{},_=0;i.shouldOhlc&&!l&&t._drawCandleBar(e,d,v,u,f,s.bar,H),n.forEach((function(i){var n=p[i.key],l=t._yAxis.convertToPixel(n);switch(i.type){case St:if(E(n)){var f=bt(a,o,u,i,s,{color:s.circle.noChangeColor,isStroke:!0});t._drawCircle({x:e,y:l,radius:d,color:f.color,isStroke:f.isStroke})}break;case gt:if(E(n)){var v;v=E(i.baseValue)?i.baseValue:t._yAxis.min();var y=t._yAxis.convertToPixel(v),m=Math.abs(y-l),x={x:e-d,width:2*d,height:Math.max(1,m)};x.y=l>y?y:1>m?y-1:l;var g=bt(a,o,u,i,s,{color:s.bar.noChangeColor});x.color=g.color,x.isStroke=g.isStroke,t._drawBar(x)}break;case xt:var S=null;if(E(n)&&(S={x:e,y:l}),r[_])r[_].coordinates.push(S);else{var k=bt(a,o,u,i,s,{color:c[_%h]});r[_]={color:k.color,coordinates:[S]}}_++}}))}),(function(){t._drawLines(r,s)}))})),this._ctx.globalCompositeOperation="destination-over"}},{key:"_drawGraphics",value:function(t,e){var i=this._chartStore.visibleDataList(),n=this._chartStore.timeScaleStore().barSpace(),r=this._chartStore.timeScaleStore().halfBarSpace();i.forEach((function(e,i){t(e.x,e.index,e.data,r,n,i)})),e&&e()}},{key:"_drawLines",value:function(t,e){var i=this;this._ctx.lineWidth=e.line.size,t.forEach((function(t){i._ctx.strokeStyle=t.color,Ut(i._ctx,t.coordinates)}))}},{key:"_drawBar",value:function(t){t.isStroke?(this._ctx.strokeStyle=t.color,this._ctx.strokeRect(t.x+.5,t.y,t.width-1,t.height)):(this._ctx.fillStyle=t.color,this._ctx.fillRect(t.x,t.y,t.width,t.height))}},{key:"_drawCircle",value:function(t){this._ctx.strokeStyle=t.color,this._ctx.fillStyle=t.color,this._ctx.beginPath(),this._ctx.arc(t.x,t.y,t.radius,2*Math.PI,0,!0),t.isStroke?this._ctx.stroke():this._ctx.fill(),this._ctx.closePath()}},{key:"_drawCandleBar",value:function(t,e,i,n,r,a,o){var s=r.open,c=r.close,h=r.high,l=r.low;c>s?(this._ctx.strokeStyle=a.upColor,this._ctx.fillStyle=a.upColor):s>c?(this._ctx.strokeStyle=a.downColor,this._ctx.fillStyle=a.downColor):(this._ctx.strokeStyle=a.noChangeColor,this._ctx.fillStyle=a.noChangeColor);var u=this._yAxis.convertToPixel(s),f=this._yAxis.convertToPixel(c),d=this._yAxis.convertToPixel(h),v=this._yAxis.convertToPixel(l),p=Math.min(u,f),_=Math.max(u,f);this._ctx.fillRect(t-.5,d,1,p-d),this._ctx.fillRect(t-.5,_,1,v-_);var y=Math.max(1,_-p);switch(o){case F:this._ctx.fillRect(t-e,p,i,y);break;case z:this._ctx.strokeRect(t-e+.5,p,i-1,y);break;case V:c>s?this._ctx.strokeRect(t-e+.5,p,i-1,y):this._ctx.fillRect(t-e,p,i,y);break;case j:c>s?this._ctx.fillRect(t-e,p,i,y):this._ctx.strokeRect(t-e+.5,p,i-1,y);break;default:this._ctx.fillRect(t-.5,d,1,v-d),this._ctx.fillRect(t-e,u-.5,e,1),this._ctx.fillRect(t,f-.5,e,1)}}}]),i}(Ne);function Xe(t,e,i,n,r){t.fillStyle=e,t.fillText(r,i,n)}function Ge(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Ue=function(t){dt(i,t);var e=Ge(i);function i(t,n,r,a,o){var s;return p(this,i),(s=e.call(this,t,n))._xAxis=r,s._yAxis=a,s._paneId=o,s}return y(i,[{key:"_draw",value:function(){this._ctx.textBaseline="alphabetic",this._drawTag(),this._drawShape(),this._drawAnnotation();var t=this._chartStore.crosshairStore().get();if(t.kLineData){var e=this._chartStore.styleOptions().crosshair;t.paneId===this._paneId&&this._drawCrosshairLine(e,"horizontal",t.y,0,this._width,Xt),t.paneId&&this._drawCrosshairLine(e,"vertical",t.realX,0,this._height,Gt),this._drawTooltip(t,this._chartStore.technicalIndicatorStore().instances(this._paneId))}}},{key:"_drawAnnotation",value:function(){var t=this,e=this._chartStore.annotationStore().get(this._paneId);e&&e.forEach((function(e){e.draw(t._ctx)}))}},{key:"_drawTag",value:function(){var t=this,e=this._chartStore.tagStore().get(this._paneId);e&&e.forEach((function(e){e.drawMarkLine(t._ctx)}))}},{key:"_drawShape",value:function(){var t=this;this._chartStore.shapeStore().instances(this._paneId).forEach((function(e){e.draw(t._ctx)}));var e=this._chartStore.shapeStore().progressInstance();e.paneId===this._paneId&&e.instance.draw(this._ctx)}},{key:"_drawTooltip",value:function(t,e){var i=this._chartStore.styleOptions().technicalIndicator;this._drawBatchTechToolTip(t,e,i,0,this._shouldDrawTooltip(t,i.tooltip))}},{key:"_drawCrosshairLine",value:function(t,e,i,n,r,a){var o=t[e],s=o.line;t.show&&o.show&&s.show&&(this._ctx.save(),this._ctx.lineWidth=s.size,this._ctx.strokeStyle=s.color,s.style===T&&this._ctx.setLineDash(s.dashValue),a(this._ctx,i,n,r),this._ctx.restore())}},{key:"_drawBatchTechToolTip",value:function(t,e,i){var n=this,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4?arguments[4]:void 0;if(a){var o=i.tooltip,s=r;e.forEach((function(e){n._drawTechTooltip(t,e,i,s),s+=o.text.marginTop+o.text.size+o.text.marginBottom}))}}},{key:"_drawTechTooltip",value:function(t,e,i){var n=this,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=i.tooltip,o=a.text,s=o.marginLeft,c=o.marginRight,h=o.size,l=o.color,u=0,f=o.marginTop+r,d=this._getTechTooltipData(t,e,i);if(this._ctx.textBaseline="top",this._ctx.font=qt(h,o.weight,o.family),a.showName){var v=d.name,p=Kt(this._ctx,v);Xe(this._ctx,l,u+=s,f,v),u+=p,a.showParams||(u+=c)}if(a.showParams){var _=d.calcParamText,y=Kt(this._ctx,_);a.showName||(u+=s),Xe(this._ctx,l,u,f,_),u+=y+c}d.values.forEach((function(t){u+=s;var e="".concat(t.title).concat(t.value),i=Kt(n._ctx,e);Xe(n._ctx,t.color||o.color,u,f,e),u+=i+c}))}},{key:"_shouldDrawTooltip",value:function(t,e){var i=e.showRule;return i===Y||i===W&&!!t.paneId}},{key:"_getTechTooltipData",value:function(t,e,i){var n=this._chartStore.dataList(),r=e.result,a="",o=e.calcParams;if(o.length>0){var s=o.map((function(t){return b(t)?t.value:t}));a="(".concat(s.join(","),")")}var c=[];if(k(e.createToolTipDataSource))c=e.createToolTipDataSource({dataSource:{from:this._chartStore.timeScaleStore().from(),to:this._chartStore.timeScaleStore().to(),kLineDataList:this._chartStore.dataList(),technicalIndicatorDataList:r},viewport:{width:this._width,height:this._height,dataSpace:this._chartStore.timeScaleStore().dataSpace(),barSpace:this._chartStore.timeScaleStore().barSpace()},crosshair:t,technicalIndicator:e,xAxis:this._xAxis,yAxis:this._yAxis,defaultStyles:i})||[];else{var h=e.styles||i,l=r[t.dataIndex],u=e.precision,f=e.shouldFormatBigNumber,d=h.line.colors||[],v=d.length,p=0;e.plots.forEach((function(e){var a={};switch(e.type){case St:a={color:h.circle.noChangeColor};break;case gt:a={color:h.bar.noChangeColor};break;case xt:a={color:d[p%v]||i.tooltip.text.color},p++}var o=bt(n,r,t.dataIndex,e,h,a),s={};if(E(e.title)){var _=(l||{})[e.key];E(_)&&(_=rt(_,u),f&&(_=at(_))),s.title=e.title,s.value=_||i.tooltip.defaultValue,s.color=o.color,c.push(s)}}))}return{values:c,name:e.shortName||e.name,calcParamText:a}}}]),i}(Ne);function Ze(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Ke=function(t){dt(i,t);var e=Ze(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_createMainView",value:function(t,e){return new We(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new Ue(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}}]),i}(je);function qe(t,e,i,n,r,a,o,s,c){Je(t,e,r,a,o,s,c),$e(t,i,n,r,a,o,s,c)}function $e(t,e,i,n,r,a,o,s){t.lineWidth=i,t.strokeStyle=e,Qe(t,n,r,a,o,s),t.stroke()}function Je(t,e,i,n,r,a,o){t.fillStyle=e,Qe(t,i,n,r,a,o),t.fill()}function Qe(t,e,i,n,r,a){t.beginPath(),t.moveTo(e+a,i),t.arcTo(e+n,i,e+n,i+r,a),t.arcTo(e+n,i+r,e,i+r,a),t.arcTo(e,i+r,e,i,a),t.arcTo(e,i,e+n,i,a),t.closePath()}function ti(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function ei(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ti(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ti(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function ii(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var ni=function(t){dt(i,t);var e=ii(i);function i(t,n,r,a){var o;return p(this,i),(o=e.call(this,t,n))._yAxis=r,o._paneId=a,o}return y(i,[{key:"_draw",value:function(){var t=this._chartStore.styleOptions().yAxis;t.show&&(this._drawAxisLine(t),this._drawTickLines(t),this._drawTickLabels(t),this._drawTechLastValue(),this._drawLastPriceLabel())}},{key:"_drawAxisLine",value:function(t){var e,i=t.axisLine;i.show&&(this._ctx.strokeStyle=i.color,this._ctx.lineWidth=i.size,e=this._yAxis.isFromYAxisZero()?0:this._width-1,Gt(this._ctx,e,0,this._height))}},{key:"_drawTickLines",value:function(t){var e=this,i=t.tickLine;if(i.show){this._ctx.lineWidth=i.size,this._ctx.strokeStyle=i.color;var n,r,a=i.length;this._yAxis.isFromYAxisZero()?(n=0,t.axisLine.show&&(n+=t.axisLine.size),r=n+a):(n=this._width,t.axisLine.show&&(n-=t.axisLine.size),r=n-a),this._yAxis.ticks().forEach((function(t){Xt(e._ctx,t.y,n,r)}))}}},{key:"_drawTickLabels",value:function(t){var e=this,i=t.tickText;if(i.show){var n,r=t.tickLine,a=r.show,o=r.length;this._yAxis.isFromYAxisZero()?(n=i.paddingLeft,t.axisLine.show&&(n+=t.axisLine.size),a&&(n+=o),this._ctx.textAlign="left"):(n=this._width-i.paddingRight,t.axisLine.show&&(n-=t.axisLine.size),a&&(n-=o),this._ctx.textAlign="right"),this._ctx.textBaseline="middle",this._ctx.font=qt(i.size,i.weight,i.family),this._ctx.fillStyle=i.color,this._yAxis.ticks().forEach((function(t){e._ctx.fillText(t.v,n,t.y)})),this._ctx.textAlign="left"}}},{key:"_drawTechLastValue",value:function(){var t=this,e=this._chartStore.styleOptions().technicalIndicator,i=e.lastValueMark;if(i.show&&i.text.show){var n=this._chartStore.technicalIndicatorStore().instances(this._paneId),r=this._chartStore.dataList();n.forEach((function(n){var a=n.result||[],o=a.length,s=a[o-1]||{},c={prev:{kLineData:r[o-2],technicalIndicatorData:a[o-2]},current:{kLineData:r[o-1],technicalIndicatorData:s},next:{kLineData:null,technicalIndicatorData:null}},h=n.precision,l=n.styles||e,u=l.line.colors||[],f=u.length,d=0;n.plots.forEach((function(e){var r,a=s[e.key];switch(e.type){case St:r=e.color&&e.color(c,l)||l.circle.noChangeColor;break;case gt:r=e.color&&e.color(c,l)||l.bar.noChangeColor;break;case xt:r=u[d%f],d++}E(a)&&t._drawMarkLabel(a,h,n.shouldFormatBigNumber,ei(ei({},i.text),{},{backgroundColor:r}))}))}))}}},{key:"_drawLastPriceLabel",value:function(){if(this._yAxis.isCandleYAxis()){var t=this._chartStore.styleOptions().candle.priceMark,e=t.last;if(t.show&&e.show&&e.text.show){var i=this._chartStore.dataList(),n=i[i.length-1];if(n){var r,a=n.close,o=n.open;r=a>o?e.upColor:o>a?e.downColor:e.noChangeColor,this._drawMarkLabel(a,this._chartStore.pricePrecision(),!1,ei(ei({},e.text),{},{backgroundColor:r}))}}}}},{key:"_drawMarkLabel",value:function(t,e,i,n){var r,a=n.size,o=n.weight,s=n.family,c=n.color,h=n.backgroundColor,l=n.borderRadius,u=n.paddingLeft,f=n.paddingTop,d=n.paddingRight,v=n.paddingBottom,p=this._yAxis.convertToNicePixel(t);if(this._yAxis.yAxisType()===L){var _=((this._chartStore.visibleDataList()[0]||{}).data||{}).close;r="".concat(((t-_)/_*100).toFixed(2),"%")}else r=rt(t,e),i&&(r=at(r));this._ctx.font=qt(a,o,s);var y,m=Kt(this._ctx,r)+u+d,x=f+a+v;y=this._yAxis.isFromYAxisZero()?0:this._width-m,Je(this._ctx,h,y,p-f-a/2,m,x,l),this._ctx.textBaseline="middle",Xe(this._ctx,c,y+u,p,r)}}]),i}(Ne);function ri(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var ai=function(t){dt(i,t);var e=ri(i);function i(t,n,r,a){var o;return p(this,i),(o=e.call(this,t,n))._yAxis=r,o._paneId=a,o}return y(i,[{key:"_draw",value:function(){this._ctx.textBaseline="middle",this._drawTag(),this._drawCrossHairLabel()}},{key:"_drawTag",value:function(){var t=this,e=this._chartStore.tagStore().get(this._paneId);e&&e.forEach((function(e){e.drawText(t._ctx)}))}},{key:"_drawCrossHairLabel",value:function(){var t=this._chartStore.crosshairStore().get();if(t.paneId===this._paneId&&0!==this._chartStore.dataList().length){var e=this._chartStore.styleOptions().crosshair,i=e.horizontal,n=i.text;if(e.show&&i.show&&n.show){var r,a,o=this._yAxis.convertFromPixel(t.y);if(this._yAxis.yAxisType()===L){var s=(this._chartStore.visibleDataList()[0]||{}).data||{};r="".concat(((o-s.close)/s.close*100).toFixed(2),"%")}else{var c=this._chartStore.technicalIndicatorStore().instances(this._paneId),h=0,l=!1;this._yAxis.isCandleYAxis()?h=this._chartStore.pricePrecision():c.forEach((function(t){h=Math.max(t.precision,h),l||(l=t.shouldFormatBigNumber)})),r=rt(o,h),l&&(r=at(r))}var u=n.borderSize,f=$t(this._ctx,r,n),d=Jt(n);a=this._yAxis.isFromYAxisZero()?0:this._width-f,qe(this._ctx,n.backgroundColor,n.borderColor,u,a,t.y-u-n.paddingTop-n.size/2,f,d,n.borderRadius),Xe(this._ctx,n.color,a+u+n.paddingLeft,t.y,r)}}}}]),i}(Ne);function oi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var si=function(t){dt(i,t);var e=oi(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_createMainView",value:function(t,e){return new ni(t,e.chartStore,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new ai(t,e.chartStore,e.yAxis,e.paneId)}}]),i}(je),ci=function(){function t(e){p(this,t),this._chartStore=e,this._width=0,this._height=0,this._cacheMinValue=0,this._cacheMaxValue=0,this._minValue=0,this._maxValue=0,this._range=0,this._ticks=[],this._initMeasureCanvas()}return y(t,[{key:"_initMeasureCanvas",value:function(){var t=Fe("canvas"),e=Zt(t);this._measureCtx=t.getContext("2d"),this._measureCtx.scale(e,e)}},{key:"min",value:function(){return this._minValue}},{key:"max",value:function(){return this._maxValue}},{key:"width",value:function(){return this._width}},{key:"height",value:function(){return this._height}},{key:"setWidth",value:function(t){this._width=t}},{key:"setHeight",value:function(t){this._height=t}},{key:"ticks",value:function(){return this._ticks}},{key:"computeAxis",value:function(t){var e=this._optimalMinMax(this._computeMinMax());return this._minValue=e.min,this._maxValue=e.max,this._range=e.range,!(this._cacheMinValue===e.min&&this._cacheMaxValue===e.max&&!t)&&(this._cacheMinValue=e.min,this._cacheMaxValue=e.max,this._ticks=this._optimalTicks(this._computeTicks()),!0)}},{key:"_computeMinMax",value:function(){}},{key:"_optimalMinMax",value:function(t){}},{key:"_computeTicks",value:function(){var t=[];if(this._range>=0){var e=this._computeInterval(this._range),i=e.interval,n=e.precision,r=ot(Math.ceil(this._minValue/i)*i,n),a=ot(Math.floor(this._maxValue/i)*i,n),o=0,s=r;if(0!==i)for(;a>=s;)t[o]={v:s.toFixed(n)},++o,s+=i}return t}},{key:"_optimalTicks",value:function(t){}},{key:"_computeInterval",value:function(t){var e,i,n,r,a=(i=Math.floor(st(e=t/8)),n=ct(i),e=(1.5>(r=e/n)?1:2.5>r?2:3.5>r?3:4.5>r?4:5.5>r?5:6.5>r?6:8)*n,-20>i?e:+e.toFixed(0>i?-i:0)),o=function(t){var e=""+t,i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}(a);return{interval:a,precision:o}}}]),t}();function hi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var li=function(t){dt(i,t);var e=hi(i);function i(t,n,r){var a;return p(this,i),(a=e.call(this,t))._realRange=0,a._isCandleYAxis=n,a._paneId=r,a}return y(i,[{key:"_computeMinMax",value:function(){var t,e=this,i=[Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER],n=[],r=!1,a=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER;this._chartStore.technicalIndicatorStore().instances(this._paneId).forEach((function(i){if(r||(r=i.shouldOhlc),s=Math.min(s,i.precision),w(i.minValue)&&(a=Math.min(a,i.minValue)),w(i.maxValue)&&(o=Math.max(o,i.maxValue)),i.styles){t||(t={top:0,bottom:0});var c=i.styles.margin;w(c.top)&&(t.top=Math.max(1>c.top?c.top:c.top/e._height,t.top)),w(c.bottom)&&(t.bottom=Math.max(1>c.bottom?c.bottom:c.bottom/e._height,t.bottom))}n.push({plots:i.plots,result:i.result})}));var c=4;if(this._isCandleYAxis){var h=this._chartStore.pricePrecision();c=s!==Number.MAX_SAFE_INTEGER?Math.min(s,h):h}else s!==Number.MAX_SAFE_INTEGER&&(c=s);var l=this._chartStore.visibleDataList(),u=this._chartStore.styleOptions().candle,f=u.type===N,d=u.area.value,v=this._isCandleYAxis&&!f||!this._isCandleYAxis&&r;return l.forEach((function(t){var r=t.index,a=t.data;v&&(i[0]=Math.min(i[0],a.low),i[1]=Math.max(i[1],a.high)),e._isCandleYAxis&&f&&(i[0]=Math.min(i[0],a[d]),i[1]=Math.max(i[1],a[d])),n.forEach((function(t){var e=t.result[r]||{};t.plots.forEach((function(t){var n=e[t.key];E(n)&&(i[0]=Math.min(i[0],n),i[1]=Math.max(i[1],n))}))}))})),i[0]!==Number.MAX_SAFE_INTEGER&&i[1]!==Number.MIN_SAFE_INTEGER?(i[0]=Math.min(a,i[0]),i[1]=Math.max(o,i[1])):(i[0]=0,i[1]=10),{min:i[0],max:i[1],precision:c,specifyMin:a,specifyMax:o,techGap:t}}},{key:"_optimalMinMax",value:function(t){var e,i,n=t.precision,r=t.specifyMin,a=t.specifyMax,o=t.techGap,s=t.min,c=t.max,h=this.yAxisType();switch(h){case L:var l=(this._chartStore.visibleDataList()[0]||{}).data||{};w(l.close)&&(s=(s-l.close)/l.close*100,c=(c-l.close)/l.close*100),e=.01;break;case B:s=st(s),c=st(c),e=.05*ct(-n);break;default:e=ct(-n)}if(s===c||e>Math.abs(s-c)){var u=r===s,f=a===c;s=u?s:f?s-8*e:s-4*e,c=f?c:u?c+8*e:c+4*e}var d,v=.2;w((i=this._isCandleYAxis?this._chartStore.styleOptions().candle.margin:o?{top:0,bottom:0}:this._chartStore.styleOptions().technicalIndicator.margin).top)&&(d=1>i.top?i.top:i.top/this._height,v=o?Math.max(o.top,d):d);var p,_=.1;w(i.bottom)&&(p=1>i.bottom?i.bottom:i.bottom/this._height,_=o?Math.max(o.bottom,p):p);var y=Math.abs(c-s);return y=Math.abs((c+=y*v)-(s-=y*_)),this._realRange=h===B?Math.abs(ct(c)-ct(s)):y,{min:s,max:c,range:y}}},{key:"_optimalTicks",value:function(t){var e=this,i=[],n=this.yAxisType(),r=this._chartStore.technicalIndicatorStore().instances(this._paneId),a=0,o=!1;this._isCandleYAxis?a=this._chartStore.pricePrecision():r.forEach((function(t){a=Math.max(a,t.precision),o||(o=t.shouldFormatBigNumber)}));var s,c,h=this._chartStore.styleOptions().xAxis.tickText.size;return n===B&&(s=this._computeInterval(this._realRange)),t.forEach((function(t){var r,l=t.v,u=e._innerConvertToPixel(+l);switch(n){case L:r="".concat(rt(l,2),"%");break;case B:r=ot(ct(l),s.precision),u=e._innerConvertToPixel(st(r)),r=rt(r,a);break;default:r=rt(l,a),o&&(r=at(r))}u>h&&e._height-h>u&&(c&&c-u>2*h||!c)&&(i.push({v:r,y:u}),c=u)})),i}},{key:"_innerConvertToPixel",value:function(t){return Math.round((1-(t-this._minValue)/this._range)*this._height)}},{key:"isCandleYAxis",value:function(){return this._isCandleYAxis}},{key:"yAxisType",value:function(){return this._isCandleYAxis?this._chartStore.styleOptions().yAxis.type:R}},{key:"isFromYAxisZero",value:function(){var t=this._chartStore.styleOptions().yAxis;return t.position===O&&t.inside||t.position===D&&!t.inside}},{key:"getSelfWidth",value:function(){var t=this,e=this._chartStore.styleOptions(),i=e.yAxis,n=i.width;if(w(n))return n;var r=0;if(i.show&&(i.axisLine.show&&(r+=i.axisLine.size),i.tickLine.show&&(r+=i.tickLine.length),i.tickText.show)){var a=0;this._measureCtx.font=qt(i.tickText.size,i.tickText.weight,i.tickText.family),this._ticks.forEach((function(e){a=Math.max(a,Kt(t._measureCtx,e.v))})),r+=i.tickText.paddingLeft+i.tickText.paddingRight+a}var o=e.crosshair,s=0;if(o.show&&o.horizontal.show&&o.horizontal.text.show){var c=this._chartStore.technicalIndicatorStore().instances(this._paneId),h=0,l=!1;c.forEach((function(t){h=Math.max(t.precision,h),l||(l=t.shouldFormatBigNumber)})),this._measureCtx.font=qt(o.horizontal.text.size,o.horizontal.text.weight,o.horizontal.text.family);var u=2;if(this.yAxisType()!==L)if(this._isCandleYAxis){var f=this._chartStore.pricePrecision(),d=e.technicalIndicator.lastValueMark;u=d.show&&d.text.show?Math.max(h,f):f}else u=h;var v=rt(this._maxValue,u);l&&(v=at(v)),s+=o.horizontal.text.paddingLeft+o.horizontal.text.paddingRight+2*o.horizontal.text.borderSize+Kt(this._measureCtx,v)}return Math.max(r,s)}},{key:"convertFromPixel",value:function(t){var e=(1-t/this._height)*this._range+this._minValue;switch(this.yAxisType()){case L:var i=(this._chartStore.visibleDataList()[0]||{}).data||{};if(w(i.close))return i.close*e/100+i.close;break;case B:return ct(e);default:return e}}},{key:"convertToPixel",value:function(t){var e;switch(this.yAxisType()){case L:var i=(this._chartStore.visibleDataList()[0]||{}).data||{};w(i.close)&&(e=(t-i.close)/i.close*100);break;case B:e=st(t);break;default:e=t}return this._innerConvertToPixel(e)}},{key:"convertToNicePixel",value:function(t){var e=this.convertToPixel(t);return Math.round(Math.max(.05*this._height,Math.min(e,.98*this._height)))}}]),i}(ci);function ui(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var fi=function(t){dt(i,t);var e=ui(i);function i(t){var n;return p(this,i),n=e.call(this,t),"height"in t&&n.setHeight(t.height),n}return y(i,[{key:"_initBefore",value:function(t){this._id=t.id,this._yAxis=this._createYAxis(t)}},{key:"_createYAxis",value:function(t){return new li(t.chartStore,!1,t.id)}},{key:"_createMainWidget",value:function(t,e){return new Ke({container:t,chartStore:e.chartStore,xAxis:e.xAxis,yAxis:this._yAxis,paneId:e.id})}},{key:"_createYAxisWidget",value:function(t,e){return new si({container:t,chartStore:e.chartStore,yAxis:this._yAxis,paneId:e.id})}},{key:"setHeight",value:function(t){Be(pt(i.prototype),"setHeight",this).call(this,t),this._yAxis.setHeight(t)}},{key:"setWidth",value:function(t,e){Be(pt(i.prototype),"setWidth",this).call(this,t,e),this._yAxis.setWidth(e)}},{key:"id",value:function(){return this._id}},{key:"yAxis",value:function(){return this._yAxis}}]),i}(ze);function di(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var vi=function(t){dt(i,t);var e=di(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_drawContent",value:function(){var t=this._chartStore.styleOptions().candle;this._drawLastPriceLine(t.priceMark),t.type===N?this._drawArea(t):(this._drawLowHighPrice(t.priceMark,"high","high",Number.MIN_SAFE_INTEGER,[-2,-5],(function(t,e){if(t>e)return t})),this._drawLowHighPrice(t.priceMark,"low","low",Number.MAX_SAFE_INTEGER,[2,5],(function(t,e){if(e>t)return t})),this._drawCandle(t)),this._drawTechs(),this._drawGrid()}},{key:"_drawArea",value:function(t){var e=this,i=[],n=[],r=Number.MAX_SAFE_INTEGER,a=t.area;this._drawGraphics((function(t,o,s,c,h,l){var u=s[a.value];if(w(u)){var f=e._yAxis.convertToPixel(u);if(0===l){var d=t-c;n.push({x:d,y:e._height}),n.push({x:d,y:f}),i.push({x:d,y:f})}i.push({x:t,y:f}),n.push({x:t,y:f}),r=Math.min(r,f)}}),(function(){var t=n.length;if(t>0){var o=n[t-1],s=e._chartStore.timeScaleStore().halfBarSpace(),c=o.x+s;i.push({x:c,y:o.y}),n.push({x:c,y:o.y}),n.push({x:c,y:e._height})}if(i.length>0&&(e._ctx.lineWidth=a.lineSize,e._ctx.strokeStyle=a.lineColor,Ut(e._ctx,i)),n.length>0){var h=a.backgroundColor;if(S(h)){var l=e._ctx.createLinearGradient(0,e._height,0,r);try{h.forEach((function(t){l.addColorStop(t.offset,t.color)}))}catch(t){}e._ctx.fillStyle=l}else e._ctx.fillStyle=h;Wt(e._ctx,n)}}))}},{key:"_drawCandle",value:function(t){var e=this;this._drawGraphics((function(i,n,r,a,o){e._drawCandleBar(i,a,o,n,r,t.bar,t.type)}))}},{key:"_drawLowHighPrice",value:function(t,e,i,n,r,a){var o=t[e];if(t.show&&o.show){var s=this._chartStore.visibleDataList(),c=n,h=-1;s.forEach((function(t){var e=t.index,r=a(it(t.data,i,n),c);r&&(c=r,h=e)}));var l=this._chartStore.pricePrecision(),u=this._yAxis.convertToPixel(c),f=this._xAxis.convertToPixel(h),d=u+r[0];this._ctx.textAlign="left",this._ctx.lineWidth=1,this._ctx.strokeStyle=o.color,this._ctx.fillStyle=o.color,Ut(this._ctx,[{x:f-2,y:d+r[0]},{x:f,y:d},{x:f+2,y:d+r[0]}]);var v=d+r[1];Ut(this._ctx,[{x:f,y:d},{x:f,y:v},{x:f+5,y:v}]),this._ctx.font=qt(o.textSize,o.textWeight,o.textFamily);var p=rt(c,l);this._ctx.textBaseline="middle",this._ctx.fillText(p,f+5+o.textMargin,v)}}},{key:"_drawLastPriceLine",value:function(t){var e=t.last;if(t.show&&e.show&&e.line.show){var i=this._chartStore.dataList(),n=i[i.length-1];if(n){var r,a=n.close,o=n.open,s=this._yAxis.convertToNicePixel(a);r=a>o?e.upColor:o>a?e.downColor:e.noChangeColor,this._ctx.save(),this._ctx.strokeStyle=r,this._ctx.lineWidth=e.line.size,e.line.style===T&&this._ctx.setLineDash(e.line.dashValue),Xt(this._ctx,s,0,this._width),this._ctx.restore()}}}}]),i}(We);function pi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var _i=function(t){dt(i,t);var e=pi(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_drawTooltip",value:function(t,e){var i=this._chartStore.styleOptions(),n=i.candle,r=n.tooltip,a=i.technicalIndicator,o=a.tooltip,s=this._shouldDrawTooltip(t,r),c=this._shouldDrawTooltip(t,o);r.showType===X&&o.showType===X?this._drawCandleTooltipWithRect(t,e,n,s,a,c):r.showType===G?(this._drawCandleTooltipWithStandard(t.kLineData,n,s),o.showType===G?this._drawBatchTechToolTip(t,e,a,s?r.text.size+r.text.marginTop:0,c):this._drawCandleTooltipWithRect(t,e,n,!1,a,c)):(this._drawCandleTooltipWithRect(t,e,n,s,a,!1),this._drawBatchTechToolTip(t,e,a,0,c))}},{key:"_drawCandleTooltipWithStandard",value:function(t,e,i){var n=this;if(i){var r=this._getCandleTooltipData(t,e),a=e.tooltip,o=a.text.marginLeft,s=a.text.marginRight,c=a.text.size,h=a.text.color,l=a.labels;this._ctx.textBaseline="top",this._ctx.font=qt(c,a.text.weight,a.text.family);var u=o,f=a.text.marginTop;l.forEach((function(t,e){var i=Kt(n._ctx,t);Xe(n._ctx,h,u,f,t),u+=i;var c,l,d=r[e]||a.defaultValue;b(d)?(c=d.value||a.defaultValue,l=d.color||h):(l=h,c=d);var v=Kt(n._ctx,c);Xe(n._ctx,l,u,f,c),u+=v+o+s}))}}},{key:"_drawCandleTooltipWithRect",value:function(t,e,i,n,r,a){var o=this;if(n||a){var s=i.tooltip,c=s.labels,h=this._getCandleTooltipData(t.kLineData,i),l=s.text.marginLeft,u=s.text.marginRight,f=s.text.marginTop,d=s.text.marginBottom,v=s.text.size,p=s.text.color,_=s.rect,y=_.borderSize,m=_.paddingLeft,x=_.paddingRight,g=_.paddingTop,S=_.paddingBottom,k=_.offsetLeft,w=_.offsetRight,P=0,C=0,A=0;this._ctx.save(),this._ctx.textBaseline="top",n&&(this._ctx.font=qt(v,s.text.weight,s.text.family),c.forEach((function(t,e){var i,n=h[e];i=b(n)?n.value||s.defaultValue:n;var r="".concat(t).concat(i),a=Kt(o._ctx,r)+l+u;P=Math.max(P,a)})),A+=(d+f+v)*c.length);var I=r.tooltip,T=I.text.marginLeft,M=I.text.marginRight,O=I.text.marginTop,D=I.text.marginBottom,R=I.text.size,L=[];if(e.forEach((function(e){L.push(o._getTechTooltipData(t,e,r))})),a&&(this._ctx.font=qt(R,I.text.weight,I.text.family),L.forEach((function(t){t.values.forEach((function(t){var e=t.title,i=t.value;if(E(e)){var n="".concat(e).concat(i),r=Kt(o._ctx,n)+T+M;P=Math.max(P,r),A+=O+D+R}}))}))),0!==(C+=P)&&0!==A){var B;C+=2*y+m+x;var F=_.offsetTop,z=_.borderRadius;Je(this._ctx,_.backgroundColor,B=this._width/2>t.realX?this._width-w-C:k,F,C,A+=2*y+g+S,z),$e(this._ctx,_.borderColor,y,B,F,C,A,z);var V=B+y+m+l,j=F+y+g;if(n&&(this._ctx.font=qt(v,s.text.weight,s.text.family),c.forEach((function(t,e){j+=f,o._ctx.textAlign="left",Xe(o._ctx,p,V,j,t);var i,n,r=h[e];b(r)?(n=r.color||p,i=r.value||s.defaultValue):(n=p,i=r||s.defaultValue),o._ctx.textAlign="right",Xe(o._ctx,n,B+C-y-u-x,j,i),j+=v+d}))),a){var H=B+y+m+T;this._ctx.font=qt(R,I.text.weight,I.text.family),L.forEach((function(t){t.values.forEach((function(t){j+=O,o._ctx.textAlign="left",o._ctx.fillStyle=t.color||I.text.color,o._ctx.fillText(t.title,H,j),o._ctx.textAlign="right",o._ctx.fillText(t.value,B+C-y-M-x,j),j+=R+D}))}))}this._ctx.restore()}}}},{key:"_getCandleTooltipData",value:function(t,e){var i=this,n=e.tooltip.values,r=[];if(n)k(n)?r=n(t,e)||[]:S(n)&&(r=n);else{var a=this._chartStore.pricePrecision(),o=this._chartStore.volumePrecision();(r=[it(t,"timestamp"),it(t,"open"),it(t,"close"),it(t,"high"),it(t,"low"),it(t,"volume")]).forEach((function(t,e){switch(e){case 0:r[e]=nt(i._chartStore.timeScaleStore().dateTimeFormat(),t,"YYYY-MM-DD hh:mm");break;case r.length-1:r[e]=at(rt(t,o));break;default:r[e]=rt(t,a)}}))}return r}}]),i}(Ue);function yi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var mi=function(t){dt(i,t);var e=yi(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_createMainView",value:function(t,e){return new vi(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new _i(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}}]),i}(Ke);function xi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var gi=function(t){dt(i,t);var e=xi(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_createYAxis",value:function(t){return new li(t.chartStore,!0,t.id)}},{key:"_createMainWidget",value:function(t,e){return new mi({container:t,chartStore:e.chartStore,xAxis:e.xAxis,yAxis:this._yAxis,paneId:e.id})}}]),i}(fi);function Si(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var ki=function(t){dt(i,t);var e=Si(i);function i(t,n,r){var a;return p(this,i),(a=e.call(this,t,n))._xAxis=r,a}return y(i,[{key:"_draw",value:function(){var t=this._chartStore.styleOptions().xAxis;t.show&&(this._drawAxisLine(t),this._drawTickLines(t),this._drawTickLabels(t))}},{key:"_drawAxisLine",value:function(t){var e=t.axisLine;e.show&&(this._ctx.strokeStyle=e.color,this._ctx.lineWidth=e.size,Xt(this._ctx,0,0,this._width))}},{key:"_drawTickLines",value:function(t){var e=this,i=t.tickLine;if(i.show){this._ctx.lineWidth=i.size,this._ctx.strokeStyle=i.color;var n=t.axisLine.show?t.axisLine.size:0,r=n+i.length;this._xAxis.ticks().forEach((function(t){Gt(e._ctx,t.x,n,r)}))}}},{key:"_drawTickLabels",value:function(t){var e=t.tickText;if(e.show){var i=t.tickLine;this._ctx.textBaseline="top",this._ctx.font=qt(e.size,e.weight,e.family),this._ctx.textAlign="center",this._ctx.fillStyle=e.color;var n=e.paddingTop;t.axisLine.show&&(n+=t.axisLine.size),i.show&&(n+=i.length);for(var r=this._xAxis.ticks(),a=r.length,o=0;a>o;o++)this._ctx.fillText(r[o].v,r[o].x,n)}}}]),i}(Ne);function bi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var wi=function(t){dt(i,t);var e=bi(i);function i(t,n,r){var a;return p(this,i),(a=e.call(this,t,n))._xAxis=r,a}return y(i,[{key:"_draw",value:function(){this._drawCrosshairLabel()}},{key:"_drawCrosshairLabel",value:function(){var t=this._chartStore.crosshairStore().get();if(t.paneId){var e=this._chartStore.styleOptions().crosshair,i=e.vertical,n=i.text;if(e.show&&i.show&&n.show&&t.dataIndex===t.realDataIndex){var r=t.kLineData.timestamp,a=nt(this._chartStore.timeScaleStore().dateTimeFormat(),r,"YYYY-MM-DD hh:mm"),o=n.paddingLeft,s=n.paddingRight,c=n.paddingTop,h=n.borderSize,l=$t(this._ctx,a,n),u=Jt(n),f=l-2*h-o-s,d=t.realX-f/2;o+h>d?d=o+h:d>this._width-f-h-s&&(d=this._width-f-h-s),qe(this._ctx,n.backgroundColor,n.borderColor,h,d-h-o,0,l,u,n.borderRadius),this._ctx.textBaseline="top",Xe(this._ctx,n.color,d,h+c,a)}}}}]),i}(Ne);function Ei(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Pi=function(t){dt(i,t);var e=Ei(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_createMainView",value:function(t,e){return new ki(t,e.chartStore,e.xAxis)}},{key:"_createOverlayView",value:function(t,e){return new wi(t,e.chartStore,e.xAxis)}}]),i}(je);function Ci(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Ai=function(t){dt(i,t);var e=Ci(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_computeMinMax",value:function(){return{min:this._chartStore.timeScaleStore().from(),max:this._chartStore.timeScaleStore().to()-1}}},{key:"_optimalMinMax",value:function(t){var e=t.min,i=t.max;return{min:e,max:i,range:i-e+1}}},{key:"_optimalTicks",value:function(t){var e=[],i=t.length,n=this._chartStore.dataList();if(i>0){var r=this._chartStore.timeScaleStore().dateTimeFormat(),a=this._chartStore.styleOptions().xAxis.tickText;this._measureCtx.font=qt(a.size,a.weight,a.family);var o=Kt(this._measureCtx,"00-00 00:00"),s=this.convertToPixel(parseInt(t[0].v,10)),c=1;if(i>1){var h=this.convertToPixel(parseInt(t[1].v,10)),l=Math.abs(h-s);o>l&&(c=Math.ceil(o/l))}for(var u=0;i>u;u+=c){var f=parseInt(t[u].v,10),d=n[f].timestamp,v=nt(r,d,"hh:mm");if(0!==u)v=this._optimalTickLabel(r,d,n[parseInt(t[u-c].v,10)].timestamp)||v;var p=this.convertToPixel(f);e.push({v:v,x:p,oV:d})}if(1===e.length)e[0].v=nt(r,e[0].oV,"YYYY-MM-DD hh:mm");else{var _=e[0].oV,y=e[1].oV;if(e[2]){var m=e[2].v;/^[0-9]{2}-[0-9]{2}$/.test(m)?e[0].v=nt(r,_,"MM-DD"):/^[0-9]{4}-[0-9]{2}$/.test(m)?e[0].v=nt(r,_,"YYYY-MM"):/^[0-9]{4}$/.test(m)&&(e[0].v=nt(r,_,"YYYY"))}else e[0].v=this._optimalTickLabel(r,_,y)||e[0].v}}return e}},{key:"_optimalTickLabel",value:function(t,e,i){var n=nt(t,e,"YYYY"),r=nt(t,e,"YYYY-MM"),a=nt(t,e,"MM-DD");return n!==nt(t,i,"YYYY")?n:r!==nt(t,i,"YYYY-MM")?r:a!==nt(t,i,"MM-DD")?a:null}},{key:"getSelfHeight",value:function(){var t=this._chartStore.styleOptions(),e=t.xAxis,i=e.height;if(w(i))return i;var n=t.crosshair,r=0;e.show&&(e.axisLine.show&&(r+=e.axisLine.size),e.tickLine.show&&(r+=e.tickLine.length),e.tickText.show&&(r+=e.tickText.paddingTop+e.tickText.paddingBottom+e.tickText.size));var a=0;return n.show&&n.vertical.show&&n.vertical.text.show&&(a+=n.vertical.text.paddingTop+n.vertical.text.paddingBottom+2*n.vertical.text.borderSize+n.vertical.text.size),Math.max(r,a)}},{key:"convertFromPixel",value:function(t){return this._chartStore.timeScaleStore().coordinateToDataIndex(t)}},{key:"convertToPixel",value:function(t){return this._chartStore.timeScaleStore().dataIndexToCoordinate(t)}}]),i}(ci);function Ii(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Ti=function(t){dt(i,t);var e=Ii(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"_initBefore",value:function(){this._xAxis=new Ai(this._chartStore)}},{key:"_createMainWidget",value:function(t,e){return new Pi({container:t,chartStore:e.chartStore,xAxis:this._xAxis})}},{key:"xAxis",value:function(){return this._xAxis}},{key:"setWidth",value:function(t,e){Be(pt(i.prototype),"setWidth",this).call(this,t,e),this._xAxis.setWidth(t)}},{key:"setHeight",value:function(t){Be(pt(i.prototype),"setHeight",this).call(this,t),this._xAxis.setHeight(t)}}]),i}(ze),Mi="mouse",Oi="touch";function Di(t){return t.type===Oi}function Ri(t){return t.type===Mi}function Li(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Bi(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Li(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Li(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var Fi=0,zi=2;function Vi(t){return t.getBoundingClientRect()||{left:0,top:0}}function ji(t){return!!t.touches}function Hi(t){t.cancelable&&t.preventDefault()}function Ni(t,e){var i=t.clientX-e.clientX,n=t.clientY-e.clientY;return Math.sqrt(i*i+n*n)}var Yi=function(){function t(e,i,n){p(this,t),this._target=e,this._handler=i,this._options=n,this._clickCount=0,this._clickTimeoutId=null,this._longTapTimeoutId=null,this._longTapActive=!1,this._mouseMoveStartPosition=null,this._moveExceededManhattanDistance=!1,this._cancelClick=!1,this._unsubscribeOutsideEvents=null,this._unsubscribeMousemove=null,this._unsubscribeRoot=null,this._startPinchMiddleCoordinate=null,this._startPinchDistance=0,this._pinchPrevented=!1,this._preventDragProcess=!1,this._mousePressed=!1,this._init()}return y(t,[{key:"setOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._options=Bi(Bi({},this.options),t)}},{key:"destroy",value:function(){null!==this._unsubscribeOutsideEvents&&(this._unsubscribeOutsideEvents(),this._unsubscribeOutsideEvents=null),null!==this._unsubscribeMousemove&&(this._unsubscribeMousemove(),this._unsubscribeMousemove=null),null!==this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null),this._clearLongTapTimeout(),this._resetClickTimeout()}},{key:"_mouseEnterHandler",value:function(t){var e=this;this._unsubscribeMousemove&&this._unsubscribeMousemove();var i=this._mouseMoveHandler.bind(this),n=this._mouseWheelHandler.bind(this);this._unsubscribeMousemove=function(){e._target.removeEventListener("mousemove",i),e._target.removeEventListener("wheel",n)},this._target.addEventListener("mousemove",i),this._target.addEventListener("wheel",n,{passive:!1}),ji(t)&&this._mouseMoveHandler(t);var r=this._makeCompatEvent(t);this._processEvent(r,this._handler.mouseEnterEvent)}},{key:"_resetClickTimeout",value:function(){null!==this._clickTimeoutId&&clearTimeout(this._clickTimeoutId),this._clickCount=0,this._clickTimeoutId=null}},{key:"_mouseMoveHandler",value:function(t){if(!this._mousePressed||ji(t)){var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.mouseMoveEvent)}}},{key:"_mouseWheelHandler",value:function(t){var e=this._makeCompatEvent(t);t.localX=e.localX,t.localY=e.localY,this._processEvent(t,this._handler.mouseWheelEvent)}},{key:"_mouseMoveWithDownHandler",value:function(t){if((!("button"in t)||t.button===Fi)&&null===this._startPinchMiddleCoordinate){var e=ji(t);if(!this._preventDragProcess||!e){this._pinchPrevented=!0;var i=this._makeCompatEvent(t),n=this._mouseMoveStartPosition,r=Math.abs(n.x-i.pageX),a=Math.abs(n.y-i.pageY),o=r+a>5;if(o||!e){if(o&&!this._moveExceededManhattanDistance&&e){var s=.5*r;a>=s&&!this._options.treatVertTouchDragAsPageScroll||s>a&&!this._options.treatHorzTouchDragAsPageScroll||(this._preventDragProcess=!0)}o&&(this._moveExceededManhattanDistance=!0,this._cancelClick=!0,e&&this._clearLongTapTimeout()),this._preventDragProcess||(this._processEvent(i,this._handler.pressedMouseMoveEvent),e&&Hi(t))}}}}},{key:"_mouseUpHandler",value:function(t){if(!("button"in t)||t.button===Fi){var e=this._makeCompatEvent(t);this._clearLongTapTimeout(),this._mouseMoveStartPosition=null,this._mousePressed=!1,this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null),ji(t)&&this._mouseLeaveHandler(t),this._processEvent(e,this._handler.mouseUpEvent),++this._clickCount,this._clickTimeoutId&&this._clickCount>1?(this._processEvent(e,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()):this._cancelClick||this._processEvent(e,this._handler.mouseClickEvent),ji(t)&&(Hi(t),this._mouseLeaveHandler(t),0===t.touches.length&&(this._longTapActive=!1))}}},{key:"_clearLongTapTimeout",value:function(){null!==this._longTapTimeoutId&&(clearTimeout(this._longTapTimeoutId),this._longTapTimeoutId=null)}},{key:"_mouseDownHandler",value:function(t){if(!("button"in t)||t.button===Fi||t.button===zi){var e=this._makeCompatEvent(t);if("button"in t&&t.button===zi)this._processEvent(e,this._handler.mouseRightDownEvent);else{this._cancelClick=!1,this._moveExceededManhattanDistance=!1,this._preventDragProcess=!1,ji(t)&&this._mouseEnterHandler(t),this._mouseMoveStartPosition={x:e.pageX,y:e.pageY},this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null);var i=this._mouseMoveWithDownHandler.bind(this),n=this._mouseUpHandler.bind(this),r=this._target.ownerDocument.documentElement;this._unsubscribeRoot=function(){r.removeEventListener("touchmove",i),r.removeEventListener("touchend",n),r.removeEventListener("mousemove",i),r.removeEventListener("mouseup",n)},r.addEventListener("touchmove",i,{passive:!1}),r.addEventListener("touchend",n,{passive:!1}),this._clearLongTapTimeout(),ji(t)&&1===t.touches.length?this._longTapTimeoutId=setTimeout(this._longTapHandler.bind(this,t),600):(r.addEventListener("mousemove",i),r.addEventListener("mouseup",n)),this._mousePressed=!0,this._processEvent(e,this._handler.mouseDownEvent),this._clickTimeoutId||(this._clickCount=0,this._clickTimeoutId=setTimeout(this._resetClickTimeout.bind(this),500))}}}},{key:"_init",value:function(){var t=this;this._target.addEventListener("mouseenter",this._mouseEnterHandler.bind(this)),this._target.addEventListener("touchcancel",this._clearLongTapTimeout.bind(this));var e,i=this._target.ownerDocument,n=function(e){t._handler.mouseDownOutsideEvent&&(e.target&&t._target.contains(e.target)||t._handler.mouseDownOutsideEvent())};this._unsubscribeOutsideEvents=function(){i.removeEventListener("mousedown",n),i.removeEventListener("touchstart",n)},i.addEventListener("mousedown",n),i.addEventListener("touchstart",n,{passive:!0}),this._target.addEventListener("mouseleave",this._mouseLeaveHandler.bind(this)),this._target.addEventListener("touchstart",this._mouseDownHandler.bind(this),{passive:!0}),e="ontouchstart"in window||!!(window.DocumentTouch&&document instanceof window.DocumentTouch),"onorientationchange"in window&&(navigator.maxTouchPoints||navigator.msMaxTouchPoints||e)||this._target.addEventListener("mousedown",this._mouseDownHandler.bind(this)),this._initPinch(),this._target.addEventListener("touchmove",(function(){}),{passive:!1})}},{key:"_initPinch",value:function(){var t=this;void 0===this._handler.pinchStartEvent&&void 0===this._handler.pinchEvent&&void 0===this._handler.pinchEndEvent||(this._target.addEventListener("touchstart",(function(e){return t._checkPinchState(e.touches)}),{passive:!0}),this._target.addEventListener("touchmove",(function(e){if(2===e.touches.length&&null!==t._startPinchMiddleCoordinate&&void 0!==t._handler.pinchEvent){var i=Ni(e.touches[0],e.touches[1]);t._handler.pinchEvent(t._startPinchMiddleCoordinate,i/t._startPinchDistance),Hi(e)}}),{passive:!1}),this._target.addEventListener("touchend",(function(e){t._checkPinchState(e.touches)})))}},{key:"_checkPinchState",value:function(t){1===t.length&&(this._pinchPrevented=!1),2!==t.length||this._pinchPrevented||this._longTapActive?this._stopPinch():this._startPinch(t)}},{key:"_startPinch",value:function(t){var e=Vi(this._target);this._startPinchMiddleCoordinate={x:(t[0].clientX-e.left+(t[1].clientX-e.left))/2,y:(t[0].clientY-e.top+(t[1].clientY-e.top))/2},this._startPinchDistance=Ni(t[0],t[1]),void 0!==this._handler.pinchStartEvent&&this._handler.pinchStartEvent(),this._clearLongTapTimeout()}},{key:"_stopPinch",value:function(){null!==this._startPinchMiddleCoordinate&&(this._startPinchMiddleCoordinate=null,void 0!==this._handler.pinchEndEvent&&this._handler.pinchEndEvent())}},{key:"_mouseLeaveHandler",value:function(t){this._unsubscribeMousemove&&this._unsubscribeMousemove();var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.mouseLeaveEvent)}},{key:"_longTapHandler",value:function(t){var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.longTapEvent),this._cancelClick=!0,this._longTapActive=!0}},{key:"_processEvent",value:function(t,e){e&&e.call(this._handler,t)}},{key:"_makeCompatEvent",value:function(t){var e;e="touches"in t&&t.touches.length?t.touches[0]:"changedTouches"in t&&t.changedTouches.length?t.changedTouches[0]:t;var i=Vi(this._target);return{clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY,screenX:e.screenX,screenY:e.screenY,localX:e.clientX-i.left,localY:e.clientY-i.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,type:t.type.startsWith("mouse")?Mi:Oi,target:e.target,view:t.view}}}]),t}(),Wi=function(){function t(e,i,n,r,a,o){p(this,t),this._chartStore=i,this._topPaneId=n,this._bottomPaneId=r,this._dragEnabled=a,this._width=0,this._offsetLeft=0,this._dragEventHandler=o,this._dragFlag=!1,this._initElement(e),this._initEvent(a)}return y(t,[{key:"_initElement",value:function(t){this._container=t,this._wrapper=Fe("div",{margin:"0",padding:"0",position:"relative",boxSizing:"border-box"}),this._element=Fe("div",{width:"100%",height:"7px",margin:"0",padding:"0",position:"absolute",top:"-3px",zIndex:"20",boxSizing:"border-box"}),this._wrapper.appendChild(this._element);var e=t.lastChild;e?t.insertBefore(this._wrapper,e):t.appendChild(this._wrapper)}},{key:"_initEvent",value:function(t){t&&(this._element.style.cursor="ns-resize",this._dragEvent=new Yi(this._element,{mouseDownEvent:this._mouseDownEvent.bind(this),mouseUpEvent:this._mouseUpEvent.bind(this),pressedMouseMoveEvent:this._pressedMouseMoveEvent.bind(this),mouseEnterEvent:this._mouseEnterEvent.bind(this),mouseLeaveEvent:this._mouseLeaveEvent.bind(this)},{treatVertTouchDragAsPageScroll:!1,treatHorzTouchDragAsPageScroll:!0}))}},{key:"_mouseDownEvent",value:function(t){this._dragFlag=!0,this._startY=t.pageY,this._dragEventHandler.startDrag(this._topPaneId,this._bottomPaneId)}},{key:"_mouseUpEvent",value:function(){this._dragFlag=!1,this._chartStore.setDragPaneFlag(!1)}},{key:"_pressedMouseMoveEvent",value:function(t){this._dragEventHandler.drag(t.pageY-this._startY,this._topPaneId,this._bottomPaneId),this._chartStore.setDragPaneFlag(!0),this._chartStore.crosshairStore().set()}},{key:"_mouseEnterEvent",value:function(){var t=this._chartStore.styleOptions().separator;this._element.style.background=t.activeBackgroundColor,this._chartStore.crosshairStore().set()}},{key:"_mouseLeaveEvent",value:function(){this._dragFlag||(this._element.style.background=null,this._chartStore.setDragPaneFlag(!1))}},{key:"height",value:function(){return this._wrapper.offsetHeight}},{key:"setSize",value:function(t,e){this._offsetLeft=t,this._width=e,this.invalidate()}},{key:"setDragEnabled",value:function(t){t!==this._dragEnabled&&(this._dragEnabled=t,t?!this._dragEvent&&this._initEvent(t):(this._element.style.cursor="default",this._dragEvent&&this._dragEvent.destroy(),this._dragEvent=null))}},{key:"topPaneId",value:function(){return this._topPaneId}},{key:"bottomPaneId",value:function(){return this._bottomPaneId}},{key:"updatePaneId",value:function(t,e){E(t)&&(this._topPaneId=t),E(e)&&(this._bottomPaneId=e)}},{key:"invalidate",value:function(){var t=this._chartStore.styleOptions().separator;this._element.style.top="".concat(-Math.floor((7-t.size)/2),"px"),this._wrapper.style.backgroundColor=t.color,this._wrapper.style.height="".concat(t.size,"px"),this._wrapper.style.marginLeft="".concat(t.fill?0:this._offsetLeft,"px"),this._wrapper.style.width=t.fill?"100%":"".concat(this._width,"px")}},{key:"getImage",value:function(){var t=this._chartStore.styleOptions().separator,e=this._wrapper.offsetWidth,i=t.size,n=Fe("canvas",{width:"".concat(e,"px"),height:"".concat(i,"px"),boxSizing:"border-box"}),r=n.getContext("2d"),a=Zt(n);return n.width=e*a,n.height=i*a,r.scale(a,a),r.fillStyle=t.color,r.fillRect(this._offsetLeft,0,e,i),n}},{key:"destroy",value:function(){this._dragEvent&&this._dragEvent.destroy(),this._container.removeChild(this._wrapper)}}]),t}(),Xi=y((function t(e){p(this,t),this._chartStore=e}));function Gi(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Ui=function(t){dt(i,t);var e=Gi(i);function i(t){var n;return p(this,i),(n=e.call(this,t))._startScrollCoordinate=null,n._touchCoordinate=null,n._touchCancelCrosshair=!1,n._touchZoomed=!1,n._pinchScale=1,n}return y(i,[{key:"pinchStartEvent",value:function(){this._pinchScale=1,this._touchZoomed=!0}},{key:"pinchEvent",value:function(t,e){var i=5*(e-this._pinchScale);this._pinchScale=e,this._chartStore.timeScaleStore().zoom(i,t)}},{key:"mouseUpEvent",value:function(){this._startScrollCoordinate=null}},{key:"mouseLeaveEvent",value:function(t){this._startScrollCoordinate=null,Ri(t)&&this._chartStore.crosshairStore().set()}},{key:"mouseMoveEvent",value:function(t){Ri(t)&&this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})}},{key:"mouseWheelEvent",value:function(t){if(Math.abs(t.deltaX)>Math.abs(t.deltaY)){if(t.cancelable&&t.preventDefault(),0===Math.abs(t.deltaX))return;this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(-t.deltaX)}else{var e=-t.deltaY/100;if(0===e)return;switch(t.cancelable&&t.preventDefault(),t.deltaMode){case t.DOM_DELTA_PAGE:e*=120;break;case t.DOM_DELTA_LINE:e*=32}if(0!==e){var i=Math.sign(e)*Math.min(1,Math.abs(e));this._chartStore.timeScaleStore().zoom(i,{x:t.localX,y:t.localY})}}}},{key:"mouseClickEvent",value:function(t){Di(t)&&(this._touchCoordinate||this._touchCancelCrosshair||this._touchZoomed||(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})))}},{key:"mouseDownEvent",value:function(t){if(this._startScrollCoordinate={x:t.localX,y:t.localY},this._chartStore.timeScaleStore().startScroll(),Di(t))if(this._touchZoomed=!1,this._touchCoordinate){var e=t.localX-this._touchCoordinate.x,i=t.localY-this._touchCoordinate.y;10>Math.sqrt(e*e+i*i)?(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})):(this._touchCancelCrosshair=!0,this._touchCoordinate=null,this._chartStore.crosshairStore().set())}else this._touchCancelCrosshair=!1}},{key:"pressedMouseMoveEvent",value:function(t){var e={x:t.localX,y:t.paneY,paneId:t.paneId};if(Di(t)){if(this._touchCoordinate)return this._touchCoordinate={x:t.localX,y:t.localY},void this._chartStore.crosshairStore().set(e);e=null}if(this._startScrollCoordinate){var i=t.localX-this._startScrollCoordinate.x;this._chartStore.timeScaleStore().scroll(i,e)}}},{key:"longTapEvent",value:function(t){Di(t)&&(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId}))}}]),i}(Xi);function Zi(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Ki(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Zi(Object(i),!0).forEach((function(e){It(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Zi(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function qi(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return $i(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return $i(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function $i(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function Ji(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var Qi=function(t){dt(i,t);var e=Ji(i);function i(t,n){var r;return p(this,i),(r=e.call(this,t))._yAxis=n,r}return y(i,[{key:"_performOverlayMouseHover",value:function(t,e,i,n){var r;if(t){var a,o=qi(t);try{for(o.s();!(a=o.n()).done;){if(r=a.value.checkEventCoordinateOn(i))break}}catch(t){o.e(t)}finally{o.f()}r&&e.id===r.id||(e.id&&e.instance&&Ri(n)&&e.instance.onMouseLeave({id:e.id,points:e.instance.points(),event:n}),r&&r.id!==e.id&&r.instance&&Ri(n)&&r.instance.onMouseEnter({id:r.id,points:r.instance.points(),event:n}))}return r}},{key:"mouseUpEvent",value:function(){this._chartStore.shapeStore().updatePressedInstance()}},{key:"mouseMoveEvent",value:function(t){if(this._waitingForMouseMove)return!1;this._waitingForMouseMove=!0;var e,i,n,r={x:t.localX,y:t.paneY},a=this._chartStore.shapeStore().progressInstance(),o=a.paneId,s=a.instance;if(s&&s.isDrawing())t.paneId&&(s.isStart()&&this._chartStore.shapeStore().updateProgressInstance(this._yAxis(t.paneId),t.paneId),o===t.paneId&&s.mouseMoveForDrawing(r,t),e=s.checkEventCoordinateOn(r)),i={id:"",element:re,elementIndex:-1};else{var c=this._chartStore.annotationStore().get(t.paneId),h=this._chartStore.shapeStore().instances(t.paneId),l=this._chartStore.shapeStore().mouseOperate().hover,u=this._chartStore.annotationStore().mouseOperate();e=this._performOverlayMouseHover(h,l,r,t),n=this._performOverlayMouseHover(c,u,r,t)}this._chartStore.shapeStore().setMouseOperate({hover:e||{id:"",element:re,elementIndex:-1},click:i}),this._chartStore.annotationStore().setMouseOperate(n||{id:""}),this._waitingForMouseMove=!1}},{key:"mouseDownEvent",value:function(t){var e,i={x:t.localX,y:t.paneY},n=this._chartStore.shapeStore().progressInstance(),r=n.instance,a=n.paneId,o={id:"",element:re,elementIndex:-1};if(r&&r.isDrawing()&&a===t.paneId)r.mouseLeftButtonDownForDrawing(),e=r.checkEventCoordinateOn(i);else{var s,c=qi(this._chartStore.shapeStore().instances(t.paneId));try{for(c.s();!(s=c.n()).done;){var h=s.value;if(e=h.checkEventCoordinateOn(i)){this._chartStore.shapeStore().updatePressedInstance(h,t.paneId,e.element),e.element===ne?o=Ki({},e):h.startPressedOtherMove(i),h.onClick({id:e.id,points:h.points(),event:t});break}}}catch(t){c.e(t)}finally{c.f()}var l=this._chartStore.annotationStore().get(t.paneId);if(l){var u,f=qi(l);try{for(f.s();!(u=f.n()).done;){var d=u.value,v=d.checkEventCoordinateOn(i);if(v){d.onClick({id:v.id,points:d.points(),event:t});break}}}catch(t){f.e(t)}finally{f.f()}}}this._chartStore.shapeStore().setMouseOperate({hover:o,click:e||{id:"",element:re,elementIndex:-1}})&&this._chartStore.invalidate(pe)}},{key:"mouseRightDownEvent",value:function(t){var e,i=this._chartStore.shapeStore().progressInstance().instance;i?e=i:e=this._chartStore.shapeStore().instances(t.paneId).find((function(e){return e.checkEventCoordinateOn({x:t.localX,y:t.paneY})}));e&&!e.onRightClick({id:e.id(),points:e.points(),event:t})&&this._chartStore.shapeStore().removeInstance(e.id());var n=this._chartStore.annotationStore().get(t.paneId);if(n){var r=n.find((function(e){return e.checkEventCoordinateOn({x:t.localX,y:t.paneY})}));r&&r.onRightClick({id:r.id(),points:r.points(),event:t})}}},{key:"pressedMouseMoveEvent",value:function(t){var e=this._chartStore.shapeStore().pressedInstance(),i=e.instance;if(i&&e.paneId===t.paneId){var n={x:t.localX,y:t.paneY};e.element===ne?i.mousePressedPointMove(n,t):i.mousePressedOtherMove(n,t),this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})}}}]),i}(Xi);function tn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var en="Equal",nn="Minus",rn="ArrowLeft",an="ArrowRight",on=function(t){dt(i,t);var e=tn(i);function i(){return p(this,i),e.apply(this,arguments)}return y(i,[{key:"keyBoardDownEvent",value:function(t){if(t.shiftKey)switch(t.code){case en:this._chartStore.timeScaleStore().zoom(.5);break;case nn:this._chartStore.timeScaleStore().zoom(-.5);break;case rn:this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(-3*this._chartStore.timeScaleStore().dataSpace());break;case an:this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(3*this._chartStore.timeScaleStore().dataSpace())}}}]),i}(Xi),sn=function(){function t(e,i,n){p(this,t),this._target=e,this._chartStore=i,this._chartContentLeftRight={},this._chartContentTopBottom={},this._paneContentSize={},this._event=new Yi(this._target,{pinchStartEvent:this._pinchStartEvent.bind(this),pinchEvent:this._pinchEvent.bind(this),mouseUpEvent:this._mouseUpEvent.bind(this),mouseClickEvent:this._mouseClickEvent.bind(this),mouseDownEvent:this._mouseDownEvent.bind(this),mouseRightDownEvent:this._mouseRightDownEvent.bind(this),mouseLeaveEvent:this._mouseLeaveEvent.bind(this),mouseMoveEvent:this._mouseMoveEvent.bind(this),mouseWheelEvent:this._mouseWheelEvent.bind(this),pressedMouseMoveEvent:this._pressedMouseMoveEvent.bind(this),longTapEvent:this._longTapEvent.bind(this)},{treatVertTouchDragAsPageScroll:!0,treatHorzTouchDragAsPageScroll:!1}),this._boundKeyBoardDownEvent=this._keyBoardDownEvent.bind(this),this._target.addEventListener("keydown",this._boundKeyBoardDownEvent),this._boundContextMenuEvent=function(t){t.preventDefault()},this._target.addEventListener("contextmenu",this._boundContextMenuEvent,!1),this._zoomScrollEventHandler=new Ui(i),this._overlayEventHandler=new Qi(i,n),this._keyBoardEventHandler=new on(i)}return y(t,[{key:"_keyBoardDownEvent",value:function(t){this._keyBoardEventHandler.keyBoardDownEvent(t)}},{key:"_pinchStartEvent",value:function(){this._zoomScrollEventHandler.pinchStartEvent()}},{key:"_pinchEvent",value:function(t,e){this._zoomScrollEventHandler.pinchEvent(t,e)}},{key:"_mouseUpEvent",value:function(t){this._checkEventInChartContent(t)&&(this._target.style.cursor="crosshair"),this._zoomScrollEventHandler.mouseUpEvent(t),this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseUpEvent(t)}},{key:"_mouseLeaveEvent",value:function(t){this._zoomScrollEventHandler.mouseLeaveEvent(t)}},{key:"_mouseMoveEvent",value:function(t){if(t.target instanceof HTMLCanvasElement)if(this._checkEventInChartContent(t)){this._target.style.cursor="crosshair";var e=this._compatChartEvent(t,!0);this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseMoveEvent(e),this._chartStore.dragPaneFlag()||this._zoomScrollEventHandler.mouseMoveEvent(e)}else this._target.style.cursor="default",this._zoomScrollEventHandler.mouseLeaveEvent(t);else this._target.style.cursor="default",this._chartStore.crosshairStore().set()}},{key:"_mouseWheelEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&this._zoomScrollEventHandler.mouseWheelEvent(this._compatChartEvent(t))}},{key:"_mouseClickEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&(this._zoomScrollEventHandler.mouseClickEvent(this._compatChartEvent(t,!0)),this._modifyEventOptions(t))}},{key:"_mouseDownEvent",value:function(t){if(this._checkEventInChartContent(t)){this._target.style.cursor="pointer";var e=this._compatChartEvent(t,!0);this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseDownEvent(e),this._checkZoomScroll()&&(this._zoomScrollEventHandler.mouseDownEvent(e),this._modifyEventOptions(t))}}},{key:"_mouseRightDownEvent",value:function(t){this._shouldPerformOverlayEvent()&&this._checkEventInChartContent(t)&&this._overlayEventHandler.mouseRightDownEvent(this._compatChartEvent(t,!0))}},{key:"_pressedMouseMoveEvent",value:function(t){if(this._checkEventInChartContent(t)){var e=this._compatChartEvent(t,!0);this._checkZoomScroll()?(this._zoomScrollEventHandler.pressedMouseMoveEvent(e),this._modifyEventOptions(t)):this._overlayEventHandler.pressedMouseMoveEvent(e)}}},{key:"_longTapEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&(this._zoomScrollEventHandler.longTapEvent(this._compatChartEvent(t,!0)),this._modifyEventOptions(t))}},{key:"_checkZoomScroll",value:function(){return!this._chartStore.dragPaneFlag()&&!this._chartStore.shapeStore().isPressed()&&!this._chartStore.shapeStore().isDrawing()}},{key:"_shouldPerformOverlayEvent",value:function(){return!this._chartStore.shapeStore().isEmpty()||!this._chartStore.annotationStore().isEmpty()}},{key:"_modifyEventOptions",value:function(t){Di(t)&&this._chartStore.crosshairStore().get().paneId?this._event.setOptions({treatVertTouchDragAsPageScroll:!1}):this._event.setOptions({treatVertTouchDragAsPageScroll:!0})}},{key:"_compatChartEvent",value:function(t,e){if(e)for(var i in this._paneContentSize)if(Object.prototype.hasOwnProperty.call(this._paneContentSize,i)){var n=this._paneContentSize[i];if(t.localY>n.contentTop&&n.contentBottom>t.localY){t.paneY=t.localY-n.contentTop,t.paneId=i;break}}return t.localX-=this._chartContentLeftRight.contentLeft,t}},{key:"_checkEventInChartContent",value:function(t){return t.localX>this._chartContentLeftRight.contentLeft&&this._chartContentLeftRight.contentRight>t.localX&&t.localY>this._chartContentTopBottom.contentTop&&this._chartContentTopBottom.contentBottom>t.localY}},{key:"setChartContentLeftRight",value:function(t){this._chartContentLeftRight=t}},{key:"setChartContentTopBottom",value:function(t){this._chartContentTopBottom=t}},{key:"setPaneContentSize",value:function(t){this._paneContentSize=t}},{key:"destroy",value:function(){this._event.destroy(),this._target.removeEventListener("keydown",this._boundKeyBoardDownEvent),this._target.removeEventListener("contextmenu",this._boundContextMenuEvent)}}]),t}();function cn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,i=0;return function(){var n=Date.now(),r=this,a=arguments;n-i>e&&(t.apply(r,a),i=n)}}function hn(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var ln=function(t){dt(i,t);var e=hn(i);function i(t){var n,r=t.id,a=t.point,o=t.chartStore,s=t.xAxis,c=t.yAxis,h=t.styles;return p(this,i),(n=e.call(this,{id:r,chartStore:o,xAxis:s,yAxis:c}))._point=a,n._symbolCoordinate={},n.setStyles(h,o.styleOptions().annotation),n}return y(i,[{key:"_drawSymbol",value:function(t,e,i){var n=this._chartStore.timeScaleStore().barSpace(),r=i.symbol,a=r.size,o=r.activeSize,s=e?w(o)?o:n:w(a)?a:n,c=e?r.activeColor:r.color;switch(r.type){case U:Ot(t,c,this._symbolCoordinate,s/2);break;case Z:!function(t,e,i,n,r,a){t.fillStyle=e,t.fillRect(i,n,r,a)}(t,c,this._symbolCoordinate.x-s/2,this._symbolCoordinate.y-s/2,s,s);break;case q:!function(t,e,i,n,r){t.fillStyle=e,t.beginPath(),t.moveTo(i.x-n/2,i.y),t.lineTo(i.x,i.y-r/2),t.lineTo(i.x+n/2,i.y),t.lineTo(i.x,i.y+r/2),t.closePath(),t.fill()}(t,c,this._symbolCoordinate,s,s);break;case K:!function(t,e,i,n,r){t.fillStyle=e,t.beginPath(),t.moveTo(i.x-n/2,i.y+r/2),t.lineTo(i.x,i.y-r/2),t.lineTo(i.x+n/2,i.y+r/2),t.closePath(),t.fill()}(t,c,this._symbolCoordinate,s,s);break;case $:t.save(),this.drawCustomSymbol({ctx:t,point:this._point,coordinate:this._symbolCoordinate,viewport:{width:this._xAxis.width(),height:this._yAxis.height(),barSpace:n},styles:r,isActive:e}),t.restore()}}},{key:"draw",value:function(t){var e=this._styles||this._chartStore.styleOptions().annotation,i=e.offset||[0,0],n=0;switch(e.position){case J:n=this._yAxis.convertToPixel(this._point.value);break;case Q:n=0;break;case tt:n=this._yAxis.height()}this._symbolCoordinate.y=n+i[0];var r=this._id===this._chartStore.annotationStore().mouseOperate().id;this._drawSymbol(t,r,e),this.drawExtend&&(t.save(),this.drawExtend({ctx:t,point:this._point,coordinate:this._symbolCoordinate,viewport:{width:this._xAxis.width(),height:this._yAxis.height()},styles:e,isActive:r}),t.restore())}},{key:"checkEventCoordinateOn",value:function(t){var e,i,n,r,a,o=this._chartStore.timeScaleStore().barSpace(),s=(this._styles||this._chartStore.styleOptions().annotation).symbol,c=w(s.size)?s.size:o;switch(s.type){case U:e=jt(this._symbolCoordinate,c/2,t);break;case Z:e=function(t,e,i){return!(t.x>i.x||i.x>e.x||t.y>i.y||i.y>e.y)}({x:this._symbolCoordinate.x-c/2,y:this._symbolCoordinate.y-c/2},{x:this._symbolCoordinate.x+c/2,y:this._symbolCoordinate.y+c/2},t);break;case q:e=(n=c)*(r=c)/2+2>Math.abs((i=this._symbolCoordinate).x-(a=t).x)*r+Math.abs(i.y-a.y)*n;break;case K:e=function(t,e){var i=Dt(t[0],t[1],t[2]),n=Dt(t[0],t[1],e)+Dt(t[0],t[2],e)+Dt(t[1],t[2],e);return 2>Math.abs(i-n)}([{x:this._symbolCoordinate.x-c/2,y:this._symbolCoordinate.y+c/2},{x:this._symbolCoordinate.x,y:this._symbolCoordinate.y-c/2},{x:this._symbolCoordinate.x+c/2,y:this._symbolCoordinate.y+c/2}],t);break;case $:e=this.checkEventCoordinateOnCustomSymbol({eventCoordinate:t,coordinate:this._symbolCoordinate,size:c})}if(e)return{id:this._id,instance:this}}},{key:"createSymbolCoordinate",value:function(t){var e=this._styles||this._chartStore.styleOptions().annotation;this._symbolCoordinate={x:t+(e.offset||[0,0])[1]}}},{key:"points",value:function(){return this._point}},{key:"checkEventCoordinateOnCustomSymbol",value:function(t){}},{key:"drawCustomSymbol",value:function(t){}}]),i}(Mt);function un(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=pt(t);if(e){var r=pt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return vt(this,i)}}var fn=function(t){dt(i,t);var e=un(i);function i(t){var n,r=t.id,a=t.point,o=t.text,s=t.mark,c=t.chartStore,h=t.xAxis,l=t.yAxis,u=t.styles;return p(this,i),(n=e.call(this,{id:r,chartStore:c,xAxis:h,yAxis:l}))._point=a||{},n._text=o,n._mark=s,n.setStyles(u,c.styleOptions().tag),n}return y(i,[{key:"update",value:function(t){var e=t.point,i=t.text,n=t.mark,r=t.styles,a=!1;return b(e)&&(this._point=e,a=!0),E(i)&&(this._text=i,a=!0),E(n)&&(this._mark=n,a=!0),this.setStyles(r,this._chartStore.styleOptions().tag)&&(a=!0),a}},{key:"drawMarkLine",value:function(t){var e=this._chartStore.styleOptions(),i=e.yAxis,n=this._styles||e.tag,r=this._getY(n);t.save(),this._drawLine(t,r,n,i),this._drawMark(t,r,n,i),t.restore()}},{key:"drawText",value:function(t){if(E(this._text)){var e=this._chartStore.styleOptions(),i=this._styles||e.tag,n=i.text;t.save();var r,a=$t(t,this._text,n),o=Jt(n);r=this._yAxis.isFromYAxisZero()?0:this._yAxis.width()-a;var s=this._getY(i);qe(t,n.backgroundColor,n.borderColor,n.borderSize,r,s-o/2,a,o,n.borderRadius),Xe(t,n.color,r+n.paddingLeft,s,this._text),t.restore()}}},{key:"_drawLine",value:function(t,e,i,n){var r=i.line;if(r.show){t.save();var a=$t(t,this._text,i.text),o=$t(t,this._mark,i.mark);t.strokeStyle=r.color,t.lineWidth=r.size,r.style===T&&t.setLineDash(r.dashValue);var s=i.mark.offset,c=[],h=E(this._text),l=E(this._mark);n.inside?n.position===O?h&&l?s>0?(c.push([a,a+s]),c.push([a+s+o,this._xAxis.width()])):c.push(Math.min(a,o)>Math.abs(s)?[a+s+o,this._xAxis.width()]:[Math.max(a,o),this._xAxis.width()]):h?c.push([a,this._xAxis.width()]):l?s>0?(c.push([0,s]),c.push([s+o,this._xAxis.width()])):c.push(o>Math.abs(s)?[s+o,this._xAxis.width()]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):h&&l?0>s?(c.push([0,this._xAxis.width()-a+s-o]),c.push([this._xAxis.width()-a+s,this._xAxis.width()-a])):c.push(Math.min(a,o)>s?[0,this._xAxis.width()-a-o+s]:[0,this._xAxis.width()-Math.max(a,o)]):h?c.push([0,this._xAxis.width()-a]):l?0>s?(c.push([0,this._xAxis.width()+s-o]),c.push([this._xAxis.width()+s,this._xAxis.width()])):c.push(o>s?[0,this._xAxis.width()-o+s]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):n.position===O?l?s>0?(c.push([0,s]),c.push([s+o,this._xAxis.width()])):c.push(o>Math.abs(s)?[o+s,this._xAxis.width()]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):l?0>s?(c.push([0,this._xAxis.width()-o+s]),c.push([this._xAxis.width()+s,this._xAxis.width()])):c.push(o>s?[0,this._xAxis.width()-o+s]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]),c.forEach((function(i){Xt(t,e,i[0],i[1])})),t.restore()}}},{key:"_drawMark",value:function(t,e,i,n){if(E(this._mark)){var r,a=i.mark,o=$t(t,this._mark,a),s=Jt(a);if(n.inside){var c=0;E(this._text)&&(c=$t(t,this._text,i.text)),r=n.position===O?c:this._xAxis.width()-c-o}else r=n.position===O?0:this._xAxis.width()-o;qe(t,a.backgroundColor,a.borderColor,a.borderSize,r+=a.offset,e-s/2,o,s,a.borderRadius),t.textBaseline="middle",t.font=qt(a.size,a.weight,a.family),Xe(t,a.color,r+a.paddingLeft,e,this._mark)}}},{key:"_getY",value:function(t){var e=t.offset;switch(t.position){case Q:return e;case tt:return this._yAxis.height()+e;default:return this._yAxis.convertToNicePixel(this._point.value)+e}}}]),i}(Mt);function dn(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return vn(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return vn(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function vn(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var pn="technical_indicator_pane_",_n="candle_pane",yn=function(){function t(e,i){var n=this;p(this,t),this._initChartContainer(e),this._shapeBaseId=0,this._paneBaseId=0,this._separatorDragStartTopPaneHeight=0,this._separatorDragStartBottomPaneHeight=0,this._chartStore=new Re(i,{invalidate:this._invalidatePane.bind(this),crosshair:this._crosshairObserver.bind(this)}),this._xAxisPane=new Ti({id:"x_axis_pane",container:this._chartContainer,chartStore:this._chartStore}),this._panes=new Map([[_n,new gi({container:this._chartContainer,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),id:_n})]]),this._separators=new Map,this._chartWidth={},this._chartHeight={},this._chartEvent=new sn(this._chartContainer,this._chartStore,(function(t){return n._panes.get(t).yAxis()})),this.adjustPaneViewport(!0,!0,!0)}return y(t,[{key:"_initChartContainer",value:function(t){this._container=t,this._chartContainer=Fe("div",{userSelect:"none",webkitUserSelect:"none",msUserSelect:"none",MozUserSelect:"none",webkitTapHighlightColor:"transparent",position:"relative",outline:"none",borderStyle:"none",width:"100%",cursor:"crosshair",boxSizing:"border-box"}),this._chartContainer.tabIndex=1,t.appendChild(this._chartContainer)}},{key:"_crosshairObserver",value:function(t){var e=this,i=t.paneId,n=t.dataIndex,r=t.kLineData,a=t.x,o=t.y;if(this._chartStore.actionStore().has(ht.CROSSHAIR)||this._chartStore.actionStore().has(ht.TOOLTIP)){var s={};this._panes.forEach((function(t,i){var a={},o=[];e.chartStore().technicalIndicatorStore().instances(i).forEach((function(t){var e=t.result[n];a[t.name]=e,o.push({name:t.name,data:e})})),s[i]=a,e._chartStore.actionStore().execute(ht.TOOLTIP,{paneId:i,dataIndex:n,kLineData:r,technicalIndicatorData:o})})),i&&this._chartStore.actionStore().execute(ht.CROSSHAIR,{paneId:i,coordinate:{x:a,y:o},dataIndex:n,kLineData:r,technicalIndicatorData:s})}}},{key:"_separatorStartDrag",value:function(t,e){this._separatorDragStartTopPaneHeight=this._panes.get(t).height(),this._separatorDragStartBottomPaneHeight=this._panes.get(e).height()}},{key:"_separatorDrag",value:function(t,e,i){var n=this._separatorDragStartTopPaneHeight+t,r=this._separatorDragStartBottomPaneHeight-t;n>this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight&&(n=this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight,r=0),0>n&&(n=0,r=this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight),this._panes.get(e).setHeight(n),this._panes.get(i).setHeight(r),this._chartStore.actionStore().execute(ht.PANE_DRAG,{topPaneId:e,bottomPaneId:i,topPaneHeight:n,bottomPaneHeight:r}),this.adjustPaneViewport(!0,!0,!0,!0,!0)}},{key:"_invalidatePane",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ye;if(t===pe)this._xAxisPane.invalidate(t),this._panes.forEach((function(e){e.invalidate(t)}));else{var e=!1;this._panes.forEach((function(t){var i=t.yAxis().computeAxis();i&&(e=i)})),this.adjustPaneViewport(!1,e,!0)}}},{key:"_measurePaneHeight",value:function(){var t=this,e=this._chartStore.styleOptions(),i=this._container.offsetHeight,n=e.separator.size,r=n*this._separators.size,a=this._xAxisPane.xAxis().getSelfHeight(),o=i-a-r,s=0;this._panes.forEach((function(t){if(t.id()!==_n){var e=t.height();s+e>o?(t.setHeight(o-s),s=o):s+=e}}));var c=o-s,h={};h.candle_pane={contentTop:0,contentBottom:c};var l=c,u=c;this._panes.get(_n).setHeight(c),this._chartHeight.candle_pane=c,this._panes.forEach((function(e){if(e.id()!==_n){var i=e.height();u+=i+n,h[e.id()]={contentTop:l,contentBottom:u},t._chartHeight[e.id()]=i,l=u}})),this._xAxisPane.setHeight(a),this._chartHeight.xAxis=a,this._chartHeight.total=i,this._chartEvent.setPaneContentSize(h),this._chartEvent.setChartContentTopBottom({contentTop:0,contentBottom:u})}},{key:"_measurePaneWidth",value:function(){var t,e,i,n=this,r=this._chartStore.styleOptions().yAxis,a=r.position===O,o=this._container.offsetWidth,s=Number.MIN_SAFE_INTEGER;!r.inside?(this._panes.forEach((function(t){s=Math.max(s,t.yAxis().getSelfWidth())})),t=o-s,a?(e=0,i=s):(i=0,e=o-s)):(t=o,s=o,e=0,i=0),this._chartStore.timeScaleStore().setTotalDataSpace(t),this._panes.forEach((function(r,a){r.setWidth(t,s),r.setOffsetLeft(i,e);var o=n._separators.get(a);o&&o.setSize(i,t)})),this._chartWidth={content:t,yAxis:s,total:o},this._xAxisPane.setWidth(t,s),this._xAxisPane.setOffsetLeft(i,e),this._chartEvent.setChartContentLeftRight({contentLeft:i,contentRight:i+t})}},{key:"adjustPaneViewport",value:function(t,e,i,n,r){t&&this._measurePaneHeight();var a=!1;n&&this._panes.forEach((function(t){var e=t.yAxis().computeAxis(r);a||(a=e)})),(!n&&e||n&&a)&&this._measurePaneWidth(),i&&(this._xAxisPane.xAxis().computeAxis(!0),this._xAxisPane.layout(),this._panes.forEach((function(t){t.layout()})))}},{key:"hasPane",value:function(t){return this._panes.has(t)}},{key:"getPane",value:function(t){return this._panes.get(t)}},{key:"chartStore",value:function(){return this._chartStore}},{key:"removeTechnicalIndicator",value:function(t,e){var i=this;if(this._chartStore.technicalIndicatorStore().removeInstance(t,e)){var n=!1;if(t!==_n&&!this._chartStore.technicalIndicatorStore().hasInstance(t)){n=!0,this._panes.get(t).destroy();var r=this._separators.get(t).topPaneId();this._separators.get(t).destroy(),this._panes.delete(t),this._separators.delete(t),this._separators.forEach((function(t){var e=t.topPaneId();i._separators.has(e)||t.updatePaneId(r)}))}this.adjustPaneViewport(n,!0,!0,!0,!0)}}},{key:"createTechnicalIndicator",value:function(t,e){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this._panes.has(n.id)){var r=this._chartStore.technicalIndicatorStore().addInstance(n.id,t,e);return r&&r.finally((function(t){i.setPaneOptions(n,i._panes.get(n.id).yAxis().computeAxis())})),n.id}var a=n.id||"".concat(pn).concat(++this._paneBaseId),o=!P(n.dragEnabled)||n.dragEnabled;this._separators.set(a,new Wi(this._chartContainer,this._chartStore,Array.from(this._panes.keys()).pop(),a,o,{startDrag:this._separatorStartDrag.bind(this),drag:cn(this._separatorDrag.bind(this),50)}));var s=new fi({container:this._chartContainer,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),id:a,height:n.height||100});this._panes.set(a,s);var c=this._chartStore.technicalIndicatorStore().addInstance(a,t,e);return c&&c.finally((function(t){i.adjustPaneViewport(!0,!0,!0,!0,!0)})),a}},{key:"createShape",value:function(t,e,i){var n=e.points,r=e.styles,a=e.lock,o=e.mode,s=e.data,c=e.onDrawStart,h=e.onDrawing,l=e.onDrawEnd,u=e.onClick,f=e.onRightClick,d=e.onPressedMove,v=e.onMouseEnter,p=e.onMouseLeave,_=e.onRemove,y=e.id||"".concat("shape_").concat(++this._shapeBaseId);if(!this._chartStore.shapeStore().hasInstance(y)){var m=null;this.hasPane(i)?m=this._panes.get(i).yAxis():n&&n.length>0&&(i=_n,m=this._panes.get(_n).yAxis());var x=new t({id:y,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),yAxis:m,points:n,styles:r,lock:a,mode:o,data:s});return k(c)&&c({id:y}),Tt(x,[{key:"onDrawing",fn:h},{key:"onDrawEnd",fn:l},{key:"onClick",fn:u},{key:"onRightClick",fn:f},{key:"onPressedMove",fn:d},{key:"onMouseEnter",fn:v},{key:"onMouseLeave",fn:p},{key:"onRemove",fn:_}]),this._chartStore.shapeStore().addInstance(x,i),y}return null}},{key:"createAnnotation",value:function(t,e){var i=this,n=[];t.forEach((function(t){var r=t.point,a=t.styles,o=t.checkEventCoordinateOnCustomSymbol,s=t.drawCustomSymbol,c=t.drawExtend,h=t.onClick,l=t.onRightClick,u=t.onMouseEnter,f=t.onMouseLeave;if(r&&r.timestamp){var d=new ln({id:r.timestamp,chartStore:i._chartStore,point:r,xAxis:i._xAxisPane.xAxis(),yAxis:i._panes.get(e).yAxis(),styles:a});Tt(d,[{key:"drawExtend",fn:c},{key:"drawCustomSymbol",fn:s},{key:"checkEventCoordinateOnCustomSymbol",fn:o},{key:"onClick",fn:h},{key:"onRightClick",fn:l},{key:"onMouseEnter",fn:u},{key:"onMouseLeave",fn:f}]),n.push(d)}})),n.length>0&&this._chartStore.annotationStore().add(n,e)}},{key:"createTag",value:function(t,e){var i=this,n=[],r=!1,a=!1;t.forEach((function(t){var o=t.id,s=t.point,c=t.text,h=t.mark,l=t.styles;if(E(o))if(i._chartStore.tagStore().has(o,e)){var u=i._chartStore.tagStore().update(o,e,{point:s,text:c,mark:h,styles:l});r||(r=u)}else a=!0,n.push(new fn({id:o,point:s,text:c,mark:h,styles:l,chartStore:i._chartStore,xAxis:i._xAxisPane.xAxis(),yAxis:i._panes.get(e).yAxis()}))})),a?this._chartStore.tagStore().add(n,e):r&&this._invalidatePane(pe)}},{key:"removeAllHtml",value:function(){this._panes.forEach((function(t){t.removeHtml()})),this._xAxisPane.removeHtml()}},{key:"setPaneOptions",value:function(t,e){var i=e,n=!1;if(t.id!==_n){var r=this._panes.get(t.id);r&&(w(t.height)&&t.height>0&&r.height()!==t.height&&(i=!0,r.setHeight(t.height),n=!0),P(t.dragEnabled)&&this._separators.get(t.id).setDragEnabled(t.dragEnabled))}i&&this.adjustPaneViewport(n,!0,!0,!0,!0)}},{key:"setTimezone",value:function(t){this._chartStore.timeScaleStore().setTimezone(t),this._xAxisPane.xAxis().computeAxis(!0),this._xAxisPane.invalidate(ye)}},{key:"convertToPixel",value:function(t,e){var i,n=this,r=e.paneId,a=void 0===r?_n:r,o=e.absoluteYAxis,s=[].concat(t),c=[],h=this._chartStore.styleOptions().separator.size,l=0,u=dn(this._panes.values());try{var f=function(){var t=i.value;if(t.id()===a)return c=s.map((function(e){var i=e.timestamp,r=e.value,a={},s=e.dataIndex;if(E(i)&&(s=n._chartStore.timeScaleStore().timestampToDataIndex(i)),E(s)&&(a.x=n._xAxisPane.xAxis().convertToPixel(s)),E(r)){var c=t.yAxis().convertToPixel(r);a.y=o?l+c:c}return a})),"break";l+=t.height()+h};for(u.s();!(i=u.n()).done;){if("break"===f())break}}catch(t){u.e(t)}finally{u.f()}return S(t)?c:c[0]||{}}},{key:"convertFromPixel",value:function(t,e){var i,n=this,r=e.paneId,a=void 0===r?_n:r,o=e.absoluteYAxis,s=[].concat(t),c=[],h=this._chartStore.styleOptions().separator.size,l=0,u=dn(this._panes.values());try{var f=function(){var t=i.value;if(t.id()===a)return c=s.map((function(e){var i=e.x,r=e.y,a={};if(E(i)&&(a.dataIndex=n._xAxisPane.xAxis().convertFromPixel(i),a.timestamp=n._chartStore.timeScaleStore().dataIndexToTimestamp(a.dataIndex)),E(r)){var s=o?r-l:r;a.value=t.yAxis().convertFromPixel(s)}return a})),"break";l+=t.height()+h};for(u.s();!(i=u.n()).done;){if("break"===f())break}}catch(t){u.e(t)}finally{u.f()}return S(t)?c:c[0]||{}}},{key:"chartWidth",value:function(){return this._chartWidth}},{key:"chartHeight",value:function(){return this._chartHeight}},{key:"getConvertPictureUrl",value:function(t,e,i){var n=this,r=this._chartContainer.offsetWidth,a=this._chartContainer.offsetHeight,o=Fe("canvas",{width:"".concat(r,"px"),height:"".concat(a,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),c=Zt(o);o.width=r*c,o.height=a*c,s.scale(c,c),s.fillStyle=i,s.fillRect(0,0,r,a);var h=0;return this._panes.forEach((function(e,i){if(i!==_n){var a=n._separators.get(i);s.drawImage(a.getImage(),0,h,r,a.height()),h+=a.height()}s.drawImage(e.getImage(t),0,h,r,e.height()),h+=e.height()})),s.drawImage(this._xAxisPane.getImage(t),0,h,r,this._xAxisPane.height()),o.toDataURL("image/".concat(e))}},{key:"destroy",value:function(){this._panes.forEach((function(t){t.destroy()})),this._separators.forEach((function(t){t.destroy()})),this._panes.clear(),this._separators.clear(),this._xAxisPane.destroy(),this._container.removeChild(this._chartContainer),this._chartEvent.destroy()}}]),t}(),mn=function(){function t(e,i){p(this,t),this._chartPane=new yn(e,i)}return y(t,[{key:"getWidth",value:function(){return this._chartPane.chartWidth()}},{key:"getHeight",value:function(){return this._chartPane.chartHeight()}},{key:"setStyleOptions",value:function(t){b(t)&&(this._chartPane.chartStore().applyStyleOptions(t),this._chartPane.adjustPaneViewport(!0,!0,!0,!0,!0))}},{key:"getStyleOptions",value:function(){return g(this._chartPane.chartStore().styleOptions())}},{key:"setPriceVolumePrecision",value:function(t,e){w(t)&&t>=0&&w(e)&&e>=0&&this._chartPane.chartStore().setPriceVolumePrecision(t,e)}},{key:"setTimezone",value:function(t){this._chartPane.setTimezone(t)}},{key:"getTimezone",value:function(){return this._chartPane.chartStore().timeScaleStore().timezone()}},{key:"resize",value:function(){this._chartPane.adjustPaneViewport(!0,!0,!0,!0,!0)}},{key:"setOffsetRightSpace",value:function(t){w(t)&&this._chartPane.chartStore().timeScaleStore().setOffsetRightSpace(t,!0)}},{key:"setLeftMinVisibleBarCount",value:function(t){w(t)&&t>0&&this._chartPane.chartStore().timeScaleStore().setLeftMinVisibleBarCount(Math.ceil(t))}},{key:"setRightMinVisibleBarCount",value:function(t){w(t)&&t>0&&this._chartPane.chartStore().timeScaleStore().setRightMinVisibleBarCount(Math.ceil(t))}},{key:"setDataSpace",value:function(t){w(t)&&this._chartPane.chartStore().timeScaleStore().setDataSpace(t)}},{key:"getDataSpace",value:function(){return this._chartPane.chartStore().timeScaleStore().dataSpace()}},{key:"getBarSpace",value:function(){return this._chartPane.chartStore().timeScaleStore().barSpace()}},{key:"clearData",value:function(){this._chartPane.chartStore().clearDataList()}},{key:"getDataList",value:function(){return this._chartPane.chartStore().dataList()}},{key:"applyNewData",value:function(t,e){var i=this;if(S(t)){var n=this._chartPane.chartStore();n.clearDataList(),n.addData(t,0,e),n.technicalIndicatorStore().calcInstance().finally((function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)}))}}},{key:"applyMoreData",value:function(t,e){var i=this;if(S(t)){var n=this._chartPane.chartStore();n.addData(t,0,e),n.technicalIndicatorStore().calcInstance().finally((function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)}))}}},{key:"updateData",value:function(t){var e=this;if(b(t)&&!S(t)){var i=this._chartPane.chartStore(),n=i.dataList(),r=n.length,a=it(t,"timestamp",0),o=it(n[r-1],"timestamp",0);if(a>=o){var s=r;a===o&&(s=r-1),i.addData(t,s),i.technicalIndicatorStore().calcInstance().finally((function(t){e._chartPane.adjustPaneViewport(!1,!0,!0,!0)}))}}}},{key:"loadMore",value:function(t){k(t)&&this._chartPane.chartStore().timeScaleStore().setLoadMoreCallback(t)}},{key:"createTechnicalIndicator",value:function(t,e,i){if(!E(t))return null;var n=b(t)&&!S(t)?t:{name:t};return this._chartPane.chartStore().technicalIndicatorStore().hasTemplate(n.name)?this._chartPane.createTechnicalIndicator(n,e,i):null}},{key:"addTechnicalIndicatorTemplate",value:function(t){if(b(t)){var e=[].concat(t);this._chartPane.chartStore().technicalIndicatorStore().addTemplate(e)}}},{key:"overrideTechnicalIndicator",value:function(t,e){var i=this;if(b(t)&&!S(t)){var n=this._chartPane.chartStore().technicalIndicatorStore().override(t,e);n&&n.then((function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)}))}}},{key:"getTechnicalIndicatorTemplate",value:function(t){return this._chartPane.chartStore().technicalIndicatorStore().getTemplateInfo(t)}},{key:"getTechnicalIndicatorByPaneId",value:function(t,e){return this._chartPane.chartStore().technicalIndicatorStore().getInstanceInfo(t,e)}},{key:"removeTechnicalIndicator",value:function(t,e){this._chartPane.removeTechnicalIndicator(t,e)}},{key:"addShapeTemplate",value:function(t){if(b(t)){var e=[].concat(t);this._chartPane.chartStore().shapeStore().addTemplate(e)}}},{key:"createShape",value:function(t,e){if(!E(t))return null;var i=b(t)&&!S(t)?t:{name:t},n=this._chartPane.chartStore().shapeStore().getTemplate(i.name);return n?this._chartPane.createShape(n,i,e):null}},{key:"getShape",value:function(t){return this._chartPane.chartStore().shapeStore().getInstanceInfo(t)}},{key:"setShapeOptions",value:function(t){b(t)&&!S(t)&&this._chartPane.chartStore().shapeStore().setInstanceOptions(t)}},{key:"removeShape",value:function(t){this._chartPane.chartStore().shapeStore().removeInstance(t)}},{key:"createAnnotation",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_n;if(b(t)&&this._chartPane.hasPane(e)){var i=[].concat(t);this._chartPane.createAnnotation(i,e)}}},{key:"removeAnnotation",value:function(t,e){this._chartPane.chartStore().annotationStore().remove(t,e)}},{key:"createTag",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_n;if(b(t)&&this._chartPane.hasPane(e)){var i=[].concat(t);this._chartPane.createTag(i,e)}}},{key:"removeTag",value:function(t,e){this._chartPane.chartStore().tagStore().remove(t,e)}},{key:"createHtml",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_n;if(!b(t))return null;if(!(C(t.content)||t.content instanceof HTMLElement))return null;var i=this._chartPane.getPane(e);return i?i.createHtml(t):null}},{key:"removeHtml",value:function(t,e){if(t){var i=this._chartPane.getPane(t);i&&i.removeHtml(e)}else this._chartPane.removeAllHtml()}},{key:"setPaneOptions",value:function(t){b(t)&&this._chartPane.setPaneOptions(t,!1)}},{key:"setZoomEnabled",value:function(t){this._chartPane.chartStore().timeScaleStore().setZoomEnabled(t)}},{key:"isZoomEnabled",value:function(){return this._chartPane.chartStore().timeScaleStore().zoomEnabled()}},{key:"setScrollEnabled",value:function(t){this._chartPane.chartStore().timeScaleStore().setScrollEnabled(t)}},{key:"isScrollEnabled",value:function(){return this._chartPane.chartStore().timeScaleStore().scrollEnabled()}},{key:"scrollByDistance",value:function(t,e){var i=this;if(w(t))if(w(e)&&e>0){this._chartPane.chartStore().timeScaleStore().startScroll();var n=(new Date).getTime();!function r(){var a=((new Date).getTime()-n)/e,o=a>=1,s=o?t:t*a;i._chartPane.chartStore().timeScaleStore().scroll(s),o||He(r)}()}else this._chartPane.chartStore().timeScaleStore().startScroll(),this._chartPane.chartStore().timeScaleStore().scroll(t)}},{key:"scrollToRealTime",value:function(t){var e=(this._chartPane.chartStore().timeScaleStore().offsetRightBarCount()-this._chartPane.chartStore().timeScaleStore().offsetRightSpace()/this._chartPane.chartStore().timeScaleStore().dataSpace())*this._chartPane.chartStore().timeScaleStore().dataSpace();this.scrollByDistance(e,t)}},{key:"scrollToDataIndex",value:function(t,e){if(w(t)){var i=(this._chartPane.chartStore().dataList().length-1-t)*this._chartPane.chartStore().timeScaleStore().dataSpace();this.scrollByDistance(i,e)}}},{key:"zoomAtCoordinate",value:function(t,e,i){var n=this;if(w(t))if(w(i)&&i>0){var r=this._chartPane.chartStore().timeScaleStore().dataSpace(),a=r*t-r,o=(new Date).getTime();!function t(){var s=((new Date).getTime()-o)/i,c=s>=1,h=c?a:a*s;n._chartPane.chartStore().timeScaleStore().zoom(h/r,e),c||He(t)}()}else this._chartPane.chartStore().timeScaleStore().zoom(t,e)}},{key:"zoomAtDataIndex",value:function(t,e,i){if(w(t)&&w(e)){var n=this._chartPane.chartStore().timeScaleStore().dataIndexToCoordinate(e);this.zoomAtCoordinate(t,{x:n},i)}}},{key:"convertToPixel",value:function(t,e){return this._chartPane.convertToPixel(t,e)}},{key:"convertFromPixel",value:function(t,e){return this._chartPane.convertFromPixel(t,e)}},{key:"subscribeAction",value:function(t,e){this._chartPane.chartStore().actionStore().subscribe(t,e)}},{key:"unsubscribeAction",value:function(t,e){this._chartPane.chartStore().actionStore().unsubscribe(t,e)}},{key:"getConvertPictureUrl",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"jpeg",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"#FFFFFF";if("png"===e||"jpeg"===e||"bmp"===e)return this._chartPane.getConvertPictureUrl(t,e,i)}},{key:"destroy",value:function(){this._chartPane.destroy()}}]),t}(),xn={},gn=1,Sn="k_line_chart_";var kn={clone:g,merge:x,isString:C,isNumber:w,isValid:E,isObject:b,isArray:S,isFunction:k,isBoolean:P,formatValue:it,formatPrecision:rt,formatBigNumber:at};v.addTechnicalIndicatorTemplate([{name:"AVP",shortName:"AVP",series:"price",precision:2,plots:[{key:"avp",title:"AVP: ",type:"line"}],calcTechnicalIndicator:function(t){var e=0,i=0;return t.map((function(t){var n={};return e+=t.turnover||0,0!==(i+=t.volume||0)&&(n.avp=e/i),n}))}},e,i,n,{name:"EMV",shortName:"EMV",calcParams:[14,9],plots:[{key:"emv",title:"EMV: ",type:"line"},{key:"maEmv",title:"MAEMV: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[],o=[];return t.forEach((function(e,s){var c={},h=t[s-1]||e,l=e.high,u=e.low,f=e.turnover||0,d=0;0!==f&&(d=((l+u)/2-(h.high+h.low)/2)*(l-u)/f),a.push(d),n+=d,i[0]-1>s||(c.emv=n,n-=a[s-(i[0]-1)],r+=c.emv,i[0]+i[1]-2>s||(c.maEmv=r/i[1],r-=o[s-(i[1]-1)].emv)),o.push(c)})),o}},{name:"EMA",shortName:"EMA",series:"price",calcParams:[6,12,20],precision:2,shouldCheckParamCount:!1,shouldOhlc:!0,plots:[{key:"ema6",title:"EMA6: ",type:"line"},{key:"ema12",title:"EMA12: ",type:"line"},{key:"ema20",title:"EMA20: ",type:"line"}],regeneratePlots:function(t){return t.map((function(t){return{key:"ema".concat(t),title:"EMA".concat(t,": "),type:"line"}}))},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=0,a=[];return t.map((function(t,e){var o={},s=t.close;return r+=s,i.forEach((function(t,i){t-1>e||(a[i]=e>t-1?(2*s+(t-1)*a[i])/(t+1):r/t,o[n[i].key]=a[i])})),o}))}},{name:"MA",shortName:"MA",series:"price",calcParams:[5,10,30,60],precision:2,shouldCheckParamCount:!1,shouldOhlc:!0,plots:[{key:"ma5",title:"MA5: ",type:"line"},{key:"ma10",title:"MA10: ",type:"line"},{key:"ma30",title:"MA30: ",type:"line"},{key:"ma60",title:"MA60: ",type:"line"}],regeneratePlots:function(t){return t.map((function(t){return{key:"ma".concat(t),title:"MA".concat(t,": "),type:"line"}}))},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map((function(e,a){var o={},s=e.close;return i.forEach((function(e,i){r[i]=(r[i]||0)+s,e-1>a||(o[n[i].key]=r[i]/e,r[i]-=t[a-(e-1)].close)})),o}))}},r,{name:"SMA",shortName:"SMA",series:"price",calcParams:[12,2],precision:2,plots:[{key:"sma",title:"SMA: ",type:"line"}],shouldCheckParamCount:!0,shouldOhlc:!0,calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0;return t.map((function(t,e){var a={},o=t.close;return n+=o,i[0]-1>e||(a.sma=r=e>i[0]-1?(o*i[1]+r*(i[0]-i[1]+1))/(i[0]+1):n/i[0]),a}))}},{name:"TRIX",shortName:"TRIX",calcParams:[12,9],plots:[{key:"trix",title:"TRIX: ",type:"line"},{key:"maTrix",title:"MATRIX: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i,n,r,a=e.params,o=0,s=0,c=0,h=0,l=[];return t.forEach((function(t,e){var u={},f=t.close;if(o+=f,e>=a[0]-1&&(s+=i=e>a[0]-1?(2*f+(a[0]-1)*i)/(a[0]+1):o/a[0],e>=2*a[0]-2&&(c+=n=e>2*a[0]-2?(2*i+(a[0]-1)*n)/(a[0]+1):s/a[0],e>=3*a[0]-3))){var d,v=0;e>3*a[0]-3?v=((d=(2*n+(a[0]-1)*r)/(a[0]+1))-r)/r*100:d=c/a[0],r=d,u.trix=v,h+=v,3*a[0]+a[1]-4>e||(u.maTrix=h/a[1],h-=l[e-(a[1]-1)].trix)}l.push(u)})),l}},{name:"BRAR",shortName:"BRAR",calcParams:[26],plots:[{key:"br",title:"BR: ",type:"line"},{key:"ar",title:"AR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0;return t.map((function(e,s){var c={},h=e.high,l=e.low,u=e.open,f=(t[s-1]||e).close;if(a+=h-u,o+=u-l,n+=h-f,r+=f-l,s>=i[0]-1){c.ar=0!==o?a/o*100:0,c.br=0!==r?n/r*100:0;var d=t[s-(i[0]-1)],v=d.high,p=d.low,_=d.open,y=(t[s-i[0]]||t[s-(i[0]-1)]).close;n-=v-y,r-=y-p,a-=v-_,o-=_-p}return c}))}},a,{name:"MTM",shortName:"MTM",calcParams:[12,6],plots:[{key:"mtm",title:"MTM: ",type:"line"},{key:"maMtm",title:"MAMTM: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=[];return t.forEach((function(e,a){var o={};a<i[0]||(o.mtm=e.close-t[a-i[0]].close,n+=o.mtm,i[0]+i[1]-1>a||(o.maMtm=n/i[1],n-=r[a-(i[1]-1)].mtm));r.push(o)})),r}},{name:"PSY",shortName:"PSY",calcParams:[12,6],plots:[{key:"psy",title:"PSY: ",type:"line"},{key:"maPsy",title:"MAPSY: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[],o=[];return t.forEach((function(e,s){var c={},h=e.close-(t[s-1]||e).close>0?1:0;a.push(h),n+=h,i[0]-1>s||(c.psy=n/i[0]*100,r+=c.psy,i[0]+i[1]-2>s||(c.maPsy=r/i[1],r-=o[s-(i[1]-1)].psy),n-=a[s-(i[0]-1)]),o.push(c)})),o}},{name:"ROC",shortName:"ROC",calcParams:[12,6],shouldCheckParamCount:!0,plots:[{key:"roc",title:"ROC: ",type:"line"},{key:"maRoc",title:"MAROC: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=[],r=0;return t.forEach((function(e,a){var o={};if(a>=i[0]-1){var s=(t[a-i[0]]||t[a-(i[0]-1)]).close;o.roc=0!==s?(e.close-s)/s*100:0,r+=o.roc,i[0]-1+i[1]-1>a||(o.maRoc=r/i[1],r-=n[a-(i[1]-1)].roc)}n.push(o)})),n}},{name:"VR",shortName:"VR",calcParams:[26,6],plots:[{key:"vr",title:"VR: ",type:"line"},{key:"maVr",title:"MAVR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0,s=[];return t.forEach((function(e,c){var h={},l=e.close,u=(t[c-1]||e).close,f=e.volume;if(l>u?n+=f:u>l?r+=f:a+=f,c>=i[0]-1){var d=a/2;h.vr=r+d===0?0:(n+d)/(r+d)*100,o+=h.vr,i[0]+i[1]-2>c||(h.maVr=o/i[1],o-=s[c-(i[1]-1)].vr);var v=t[c-(i[0]-1)],p=t[c-i[0]]||v,_=v.close,y=v.volume;_>p.close?n-=y:p.close>_?r-=y:a-=y}s.push(h)})),s}},o,{name:"BIAS",shortName:"BIAS",calcParams:[6,12,24],shouldCheckParamCount:!1,plots:[{key:"bias6",title:"BIAS6: ",type:"line"},{key:"bias12",title:"BIAS12: ",type:"line"},{key:"bias24",title:"BIAS24: ",type:"line"}],regeneratePlots:function(t){return t.map((function(t){return{key:"bias".concat(t),title:"BIAS".concat(t,": "),type:"line"}}))},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map((function(e,a){var o={},s=e.close;return i.forEach((function(e,c){if(r[c]=(r[c]||0)+s,a>=e-1){var h=r[c]/i[c];o[n[c].key]=(s-h)/h*100,r[c]-=t[a-(e-1)].close}})),o}))}},s,c,l,u,f,d,{name:"OBV",shortName:"OBV",calcParams:[30],plots:[{key:"obv",title:"OBV: ",type:"line"},{key:"maObv",title:"MAOBV: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[];return t.forEach((function(e,o){var s=t[o-1]||e;s.close>e.close?r-=e.volume:e.close>s.close&&(r+=e.volume);var c={obv:r};n+=r,i[0]-1>o||(c.maObv=n/i[0],n-=a[o-(i[0]-1)].obv),a.push(c)})),a}},{name:"PVT",shortName:"PVT",plots:[{key:"pvt",title:"PVT: ",type:"line"}],calcTechnicalIndicator:function(t){var e=0;return t.map((function(i,n){var r={},a=(t[n-1]||i).close,o=0;return 0!==a&&(o=(i.close-a)/a*i.volume),r.pvt=e+=o,r}))}},{name:"VOL",shortName:"VOL",series:"volume",calcParams:[5,10,20],shouldCheckParamCount:!1,shouldFormatBigNumber:!0,precision:0,minValue:0,plots:[{key:"ma5",title:"MA5: ",type:"line"},{key:"ma10",title:"MA10: ",type:"line"},{key:"ma20",title:"MA20: ",type:"line"},{key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,color:function(t,e){var i=t.current.kLineData||{};return i.close>i.open?e.bar.upColor:i.open>i.close?e.bar.downColor:e.bar.noChangeColor}}],regeneratePlots:function(t){var e=t.map((function(t){return{key:"ma".concat(t),title:"MA".concat(t,": "),type:"line"}}));return e.push({key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,color:function(t,e){var i=t.current.kLineData||{};return i.close>i.open?e.bar.upColor:i.open>i.close?e.bar.downColor:e.bar.noChangeColor}}),e},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map((function(e,a){var o=e.volume||0,s={volume:o};return i.forEach((function(e,i){r[i]=(r[i]||0)+o,e-1>a||(s[n[i].key]=r[i]/e,r[i]-=t[a-(e-1)].volume)})),s}))}}]),v.addShapeTemplate([{name:"horizontalRayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return zt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i={x:0,y:e[0].y};return e[1]&&e[1].x>e[0].x&&(i.x=t.viewport.width),[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],i]]}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].value=i.value,e[1].value=i.value},performEventMoveForDrawing:function(t){2===t.step&&(t.points[0].value=t.movePoint.value)}},{name:"horizontalSegment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Vt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].value=i.value,e[1].value=i.value},performEventMoveForDrawing:function(t){2===t.step&&(t.points[0].value=t.movePoint.value)}},{name:"horizontalStraightLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:0,y:e[0].y},{x:t.viewport.width,y:e[0].y}]]}]}},{name:"verticalRayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return zt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i={x:e[0].x,y:0};return e[1]&&e[1].y>e[0].y&&(i.y=t.viewport.height),[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],i]]}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex,e[1].timestamp=i.timestamp,e[1].dataIndex=i.dataIndex},performEventMoveForDrawing:function(t){var e=t.points,i=t.movePoint;2===t.step&&(e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex)}},{name:"verticalSegment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Vt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex,e[1].timestamp=i.timestamp,e[1].dataIndex=i.dataIndex},performEventMoveForDrawing:function(t){var e=t.points,i=t.movePoint;2===t.step&&(e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex)}},{name:"verticalStraightLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:e[0].x,y:0},{x:e[0].x,y:t.viewport.height}]]}]}},{name:"rayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return zt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e,i,n,r=t.coordinates,a=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[(e=r[0],i=r[1],n={x:a.width,y:a.height},e&&i?[e,e.x===i.x&&e.y!==i.y?i.y>e.y?{x:e.x,y:n.y}:{x:e.x,y:0}:e.x>i.x?{x:0,y:Lt(e,i,{x:0,y:e.y})}:{x:n.x,y:Lt(e,i,{x:n.x,y:e.y})}]:[])]}]}},{name:"segment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Vt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]}},{name:"straightLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=t.viewport;return 2>e.length||e[0].x===e[1].x?[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:e[0].x,y:0},{x:e[0].x,y:i.height}]]}]:[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:0,y:Lt(e[0],e[1],{x:0,y:e[0].y})},{x:i.width,y:Lt(e[0],e[1],{x:i.width,y:e[0].y})}]]}]}},{name:"parallelStraightLine",totalStep:4,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:Ht(t.coordinates,{x:e.width,y:e.height})}]}},{name:"priceChannelLine",totalStep:4,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:Ht(t.coordinates,{x:e.width,y:e.height},1)}]}},{name:"priceLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return zt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=t.precision;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],{x:t.viewport.width,y:e[0].y}]]},{type:"text",isDraw:!0,isCheck:!1,dataSource:[{x:e[0].x,y:e[0].y,text:t.yAxis.convertFromPixel(e[0].y).toFixed(i.price)}]}]}},{name:"fibonacciLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ft(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.points,i=t.coordinates,n=t.precision;if(i.length>0){var r=[],a=[],o=t.viewport.width;if(i.length>1){var s=i[0].y-i[1].y,c=e[0].value-e[1].value;[1,.786,.618,.5,.382,.236,0].forEach((function(t){var h=i[1].y+s*t,l=(e[1].value+c*t).toFixed(n.price);r.push([{x:0,y:h},{x:o,y:h}]),a.push({x:0,y:h,text:"".concat(l," (").concat((100*t).toFixed(1),"%)")})}))}return[{type:"line",isDraw:!0,isCheck:!0,dataSource:r},{type:"text",isDraw:!0,isCheck:!1,dataSource:a}]}return[]}}]),t.dispose=function(t){if(t){var e;if(C(t)){var i=document.getElementById(t);e=i&&i.chartId}else e=t instanceof mn?t.id:t&&t.chartId;e&&(xn[e].destroy(),delete xn[e])}},t.extension=v,t.init=function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)return null;if(!(e=C(t)?document.getElementById(t):t))return null;var n=xn[e.chartId||""];if(n)return n;var r="".concat(Sn).concat(gn++);return(n=new mn(e,i)).id=r,e.chartId=r,xn[r]=n,n},t.utils=kn,t.version=function(){return"8.3.2"},Object.defineProperty(t,"__esModule",{value:!0})}));

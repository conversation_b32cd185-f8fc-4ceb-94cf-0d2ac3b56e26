/* #ifdef H5 */
uni-page {
	opacity: 0;
}

uni-page.animation-before {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	transform: translateX(20px);
}

uni-page.animation-leave {
	transition: all .2s ease;
}

uni-page.animation-enter {
	transition: all .2s ease;
}

uni-page.animation-show {
	opacity: 1;
}

uni-page.animation-after {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	transform: translateX(0);
}

/* #endif */

const t=["style","currency","currencyDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","localeMatcher","formatMatcher","unit"];function e(t,e){"undefined"!=typeof console&&(console.warn("[vue-i18n] "+t),e&&console.warn(e.stack))}const n=Array.isArray;function s(t){return null!==t&&"object"==typeof t}function r(t){return"string"==typeof t}const a=Object.prototype.toString,i="[object Object]";function o(t){return a.call(t)===i}function l(t){return null==t}function c(...t){let e=null,n=null;return 1===t.length?s(t[0])||Array.isArray(t[0])?n=t[0]:"string"==typeof t[0]&&(e=t[0]):2===t.length&&("string"==typeof t[0]&&(e=t[0]),(s(t[1])||Array.isArray(t[1]))&&(n=t[1])),{locale:e,params:n}}function h(t){return JSON.parse(JSON.stringify(t))}function u(t,e){return!!~t.indexOf(e)}const m=Object.prototype.hasOwnProperty;function _(t,e){return m.call(t,e)}function f(t){const e=Object(t);for(let t=1;t<arguments.length;t++){const n=arguments[t];if(null!=n){let t;for(t in n)_(n,t)&&(s(n[t])?e[t]=f(e[t],n[t]):e[t]=n[t])}}return e}function p(t,e){if(t===e)return!0;const n=s(t),r=s(e);if(!n||!r)return!n&&!r&&String(t)===String(e);try{const n=Array.isArray(t),s=Array.isArray(e);if(n&&s)return t.length===e.length&&t.every((t,n)=>p(t,e[n]));if(n||s)return!1;{const n=Object.keys(t),s=Object.keys(e);return n.length===s.length&&n.every(n=>p(t[n],e[n]))}}catch(t){return!1}}var g={beforeCreate(){const t=this.$options;if(t.i18n=t.i18n||(t.__i18n?{}:null),t.i18n){if(t.i18n instanceof Y){if(t.__i18n)try{let e={};t.__i18n.forEach(t=>{e=f(e,JSON.parse(t))}),Object.keys(e).forEach(n=>{t.i18n.mergeLocaleMessage(n,e[n])})}catch(t){}this._i18n=t.i18n,this._i18nWatcher=this._i18n.watchI18nData()}else if(o(t.i18n)){const e=this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Y?this.$root.$i18n:null;if(e&&(t.i18n.root=this.$root,t.i18n.formatter=e.formatter,t.i18n.fallbackLocale=e.fallbackLocale,t.i18n.formatFallbackMessages=e.formatFallbackMessages,t.i18n.silentTranslationWarn=e.silentTranslationWarn,t.i18n.silentFallbackWarn=e.silentFallbackWarn,t.i18n.pluralizationRules=e.pluralizationRules,t.i18n.preserveDirectiveContent=e.preserveDirectiveContent),t.__i18n)try{let e={};t.__i18n.forEach(t=>{e=f(e,JSON.parse(t))}),t.i18n.messages=e}catch(t){}const{sharedMessages:n}=t.i18n;n&&o(n)&&(t.i18n.messages=f(t.i18n.messages,n)),this._i18n=new Y(t.i18n),this._i18nWatcher=this._i18n.watchI18nData(),(void 0===t.i18n.sync||t.i18n.sync)&&(this._localeWatcher=this.$i18n.watchLocale()),e&&e.onComponentInstanceCreated(this._i18n)}}else this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Y?this._i18n=this.$root.$i18n:t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Y&&(this._i18n=t.parent.$i18n)},beforeMount(){const t=this.$options;t.i18n=t.i18n||(t.__i18n?{}:null),t.i18n?t.i18n instanceof Y?(this._i18n.subscribeDataChanging(this),this._subscribing=!0):o(t.i18n)&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0):this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Y?(this._i18n.subscribeDataChanging(this),this._subscribing=!0):t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Y&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0)},beforeDestroy(){if(!this._i18n)return;const t=this;this.$nextTick(()=>{t._subscribing&&(t._i18n.unsubscribeDataChanging(t),delete t._subscribing),t._i18nWatcher&&(t._i18nWatcher(),t._i18n.destroyVM(),delete t._i18nWatcher),t._localeWatcher&&(t._localeWatcher(),delete t._localeWatcher)})}},d={name:"i18n",functional:!0,props:{tag:{type:[String,Boolean,Object],default:"span"},path:{type:String,required:!0},locale:{type:String},places:{type:[Array,Object]}},render(t,{data:e,parent:n,props:s,slots:r}){const{$i18n:a}=n;if(!a)return;const{path:i,locale:o,places:l}=s,c=r(),h=a.i(i,o,function(t){let e;for(e in t)if("default"!==e)return!1;return Boolean(e)}(c)||l?function(t,e){const n=e?function(t){return Array.isArray(t)?t.reduce(v,{}):Object.assign({},t)}(e):{};if(!t)return n;const s=(t=t.filter(t=>t.tag||""!==t.text.trim())).every(y);return t.reduce(s?b:v,n)}(c.default,l):c),u=s.tag&&!0!==s.tag||!1===s.tag?s.tag:"span";return u?t(u,e,h):h}};function b(t,e){return e.data&&e.data.attrs&&e.data.attrs.place&&(t[e.data.attrs.place]=e),t}function v(t,e,n){return t[n]=e,t}function y(t){return Boolean(t.data&&t.data.attrs&&t.data.attrs.place)}var F={name:"i18n-n",functional:!0,props:{tag:{type:[String,Boolean,Object],default:"span"},value:{type:Number,required:!0},format:{type:[String,Object]},locale:{type:String}},render(e,{props:n,parent:a,data:i}){const o=a.$i18n;if(!o)return null;let l=null,c=null;r(n.format)?l=n.format:s(n.format)&&(n.format.key&&(l=n.format.key),c=Object.keys(n.format).reduce((e,s)=>u(t,s)?Object.assign({},e,{[s]:n.format[s]}):e,null));const h=n.locale||o.locale,m=o._ntp(n.value,h,l,c),_=m.map((t,e)=>{const n=i.scopedSlots&&i.scopedSlots[t.type];return n?n({[t.type]:t.value,index:e,parts:m}):t.value}),f=n.tag&&!0!==n.tag||!1===n.tag?n.tag:"span";return f?e(f,{attrs:i.attrs,class:i.class,staticClass:i.staticClass},_):_}};function k(t,e,n){M(t,n)&&C(t,e,n)}function $(t,e,n,s){if(!M(t,n))return;const r=n.context.$i18n;(function(t,e){const n=e.context;return t._locale===n.$i18n.locale})(t,n)&&p(e.value,e.oldValue)&&p(t._localeMessage,r.getLocaleMessage(r.locale))||C(t,e,n)}function w(t,n,s,r){if(!s.context)return void e("Vue instance does not exists in VNode context");const a=s.context.$i18n||{};n.modifiers.preserve||a.preserveDirectiveContent||(t.textContent=""),t._vt=void 0,delete t._vt,t._locale=void 0,delete t._locale,t._localeMessage=void 0,delete t._localeMessage}function M(t,n){const s=n.context;return s?!!s.$i18n||(e("VueI18n instance does not exists in Vue instance"),!1):(e("Vue instance does not exists in VNode context"),!1)}function C(t,n,s){const a=n.value,{path:i,locale:l,args:c,choice:h}=function(t){let e,n,s,a;r(t)?e=t:o(t)&&(e=t.path,n=t.locale,s=t.args,a=t.choice);return{path:e,locale:n,args:s,choice:a}}(a);if(!i&&!l&&!c)return void e("value type not supported");if(!i)return void e("`path` is required in v-t directive");const u=s.context;t._vt=t.textContent=null!=h?u.$i18n.tc(i,h,...T(l,c)):u.$i18n.t(i,...T(l,c)),t._locale=u.$i18n.locale,t._localeMessage=u.$i18n.getLocaleMessage(u.$i18n.locale)}function T(t,e){const n=[];return t&&n.push(t),e&&(Array.isArray(e)||o(e))&&n.push(e),n}let L;function D(t){D.installed=!0;(L=t).version&&Number(L.version.split(".")[0]);!function(t){t.prototype.hasOwnProperty("$i18n")||Object.defineProperty(t.prototype,"$i18n",{get(){return this._i18n}}),t.prototype.$t=function(t,...e){const n=this.$i18n;return n._t(t,n.locale,n._getMessages(),this,...e)},t.prototype.$tc=function(t,e,...n){const s=this.$i18n;return s._tc(t,s.locale,s._getMessages(),this,e,...n)},t.prototype.$te=function(t,e){const n=this.$i18n;return n._te(t,n.locale,n._getMessages(),e)},t.prototype.$d=function(t,...e){return this.$i18n.d(t,...e)},t.prototype.$n=function(t,...e){return this.$i18n.n(t,...e)}}(L),L.mixin(g),L.directive("t",{bind:k,update:$,unbind:w}),L.component(d.name,d),L.component(F.name,F),L.config.optionMergeStrategies.i18n=function(t,e){return void 0===e?t:e}}const I=/^(?:\d)+/,O=/^(?:\w)+/;const x=0,W=1,j=2,N=3,A=0,S=4,R=5,H=6,P=7,V=8,E=[];E[A]={ws:[A],ident:[3,x],"[":[S],eof:[P]},E[1]={ws:[1],".":[2],"[":[S],eof:[P]},E[2]={ws:[2],ident:[3,x],0:[3,x],number:[3,x]},E[3]={ident:[3,x],0:[3,x],number:[3,x],ws:[1,W],".":[2,W],"[":[S,W],eof:[P,W]},E[S]={"'":[R,x],'"':[H,x],"[":[S,j],"]":[1,N],eof:V,else:[S,x]},E[R]={"'":[S,x],eof:V,else:[R,x]},E[H]={'"':[S,x],eof:V,else:[H,x]};const z=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function B(t){if(null==t)return"eof";switch(t.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return t;case 95:case 36:case 45:return"ident";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"ws"}return"ident"}function U(t){const e=t.trim();return("0"!==t.charAt(0)||!isNaN(t))&&(n=e,z.test(n)?function(t){const e=t.charCodeAt(0);return e!==t.charCodeAt(t.length-1)||34!==e&&39!==e?t:t.slice(1,-1)}(e):"*"+e);var n}class J{constructor(){this._cache=Object.create(null)}parsePath(t){let e=this._cache[t];return e||(e=function(t){const e=[];let n,s,r,a,i,o,l,c=-1,h=A,u=0;const m=[];function _(){const e=t[c+1];if(h===R&&"'"===e||h===H&&'"'===e)return c++,r="\\"+e,m[x](),!0}for(m[W]=function(){void 0!==s&&(e.push(s),s=void 0)},m[x]=function(){void 0===s?s=r:s+=r},m[j]=function(){m[x](),u++},m[N]=function(){if(u>0)u--,h=S,m[x]();else{if(u=0,void 0===s)return!1;if(!1===(s=U(s)))return!1;m[W]()}};null!==h;)if("\\"!==(n=t[++c])||!_()){if(a=B(n),(i=(l=E[h])[a]||l.else||V)===V)return;if(h=i[0],(o=m[i[1]])&&(r=void 0===(r=i[2])?n:r,!1===o()))return;if(h===P)return e}}(t))&&(this._cache[t]=e),e||[]}getPathValue(t,e){if(!s(t))return null;const n=this.parsePath(e);if(0===n.length)return null;{const e=n.length;let s=t,r=0;for(;r<e;){const t=s[n[r]];if(void 0===t)return null;s=t,r++}return s}}}const q=/<\/?[\w\s="/.':;#-\/]+>/,G=/(?:@(?:\.[a-z]+)?:(?:[\w\-_|.]+|\([\w\-_|.]+\)))/g,X=/^@(?:\.([a-z]+))?:/,Z=/[()]/g,K={upper:t=>t.toLocaleUpperCase(),lower:t=>t.toLocaleLowerCase(),capitalize:t=>`${t.charAt(0).toLocaleUpperCase()}${t.substr(1)}`},Q=new class{constructor(){this._caches=Object.create(null)}interpolate(t,e){if(!e)return[t];let n=this._caches[t];return n||(n=function(t){const e=[];let n=0,s="";for(;n<t.length;){let r=t[n++];if("{"===r){s&&e.push({type:"text",value:s}),s="";let a="";for(r=t[n++];void 0!==r&&"}"!==r;)a+=r,r=t[n++];const i="}"===r,o=I.test(a)?"list":i&&O.test(a)?"named":"unknown";e.push({value:a,type:o})}else"%"===r?"{"!==t[n]&&(s+=r):s+=r}return s&&e.push({type:"text",value:s}),e}(t),this._caches[t]=n),function(t,e){const n=[];let r=0;const a=Array.isArray(e)?"list":s(e)?"named":"unknown";if("unknown"===a)return n;for(;r<t.length;){const s=t[r];switch(s.type){case"text":n.push(s.value);break;case"list":n.push(e[parseInt(s.value,10)]);break;case"named":"named"===a&&n.push(e[s.value])}r++}return n}(n,e)}};class Y{constructor(t={}){!L&&"undefined"!=typeof window&&window.Vue&&D(window.Vue);const e=t.locale||"en-US",n=!1!==t.fallbackLocale&&(t.fallbackLocale||"en-US"),s=t.messages||{},r=t.dateTimeFormats||{},a=t.numberFormats||{};this._vm=null,this._formatter=t.formatter||Q,this._modifiers=t.modifiers||{},this._missing=t.missing||null,this._root=t.root||null,this._sync=void 0===t.sync||!!t.sync,this._fallbackRoot=void 0===t.fallbackRoot||!!t.fallbackRoot,this._formatFallbackMessages=void 0!==t.formatFallbackMessages&&!!t.formatFallbackMessages,this._silentTranslationWarn=void 0!==t.silentTranslationWarn&&t.silentTranslationWarn,this._silentFallbackWarn=void 0!==t.silentFallbackWarn&&!!t.silentFallbackWarn,this._dateTimeFormatters={},this._numberFormatters={},this._path=new J,this._dataListeners=[],this._componentInstanceCreatedListener=t.componentInstanceCreatedListener||null,this._preserveDirectiveContent=void 0!==t.preserveDirectiveContent&&!!t.preserveDirectiveContent,this.pluralizationRules=t.pluralizationRules||{},this._warnHtmlInMessage=t.warnHtmlInMessage||"off",this._postTranslation=t.postTranslation||null,this.getChoiceIndex=((t,e)=>{const n=Object.getPrototypeOf(this);if(n&&n.getChoiceIndex){return n.getChoiceIndex.call(this,t,e)}return this.locale in this.pluralizationRules?this.pluralizationRules[this.locale].apply(this,[t,e]):((t,e)=>(t=Math.abs(t),2===e?t?t>1?1:0:1:t?Math.min(t,2):0))(t,e)}),this._exist=((t,e)=>!(!t||!e)&&(!l(this._path.getPathValue(t,e))||!!t[e])),"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||Object.keys(s).forEach(t=>{this._checkLocaleMessage(t,this._warnHtmlInMessage,s[t])}),this._initVM({locale:e,fallbackLocale:n,messages:s,dateTimeFormats:r,numberFormats:a})}_checkLocaleMessage(t,n,s){const a=(t,n,s,i)=>{if(o(s))Object.keys(s).forEach(e=>{const r=s[e];o(r)?(i.push(e),i.push("."),a(t,n,r,i),i.pop(),i.pop()):(i.push(e),a(t,n,r,i),i.pop())});else if(Array.isArray(s))s.forEach((e,s)=>{o(e)?(i.push(`[${s}]`),i.push("."),a(t,n,e,i),i.pop(),i.pop()):(i.push(`[${s}]`),a(t,n,e,i),i.pop())});else if(r(s)){if(q.test(s)){const r=`Detected HTML in message '${s}' of keypath '${i.join("")}' at '${n}'. Consider component interpolation with '<i18n>' to avoid XSS. See https://bit.ly/2ZqJzkp`;"warn"===t?e(r):"error"===t&&function(t,e){"undefined"!=typeof console&&(console.error("[vue-i18n] "+t),e&&console.error(e.stack))}(r)}}};a(n,t,s,[])}_initVM(t){const e=L.config.silent;L.config.silent=!0,this._vm=new L({data:t}),L.config.silent=e}destroyVM(){this._vm.$destroy()}subscribeDataChanging(t){this._dataListeners.push(t)}unsubscribeDataChanging(t){!function(t,e){if(t.length){const n=t.indexOf(e);if(n>-1)t.splice(n,1)}}(this._dataListeners,t)}watchI18nData(){const t=this;return this._vm.$watch("$data",()=>{let e=t._dataListeners.length;for(;e--;)L.nextTick(()=>{t._dataListeners[e]&&t._dataListeners[e].$forceUpdate()})},{deep:!0})}watchLocale(){if(!this._sync||!this._root)return null;const t=this._vm;return this._root.$i18n.vm.$watch("locale",e=>{t.$set(t,"locale",e),t.$forceUpdate()},{immediate:!0})}onComponentInstanceCreated(t){this._componentInstanceCreatedListener&&this._componentInstanceCreatedListener(t,this)}get vm(){return this._vm}get messages(){return h(this._getMessages())}get dateTimeFormats(){return h(this._getDateTimeFormats())}get numberFormats(){return h(this._getNumberFormats())}get availableLocales(){return Object.keys(this.messages).sort()}get locale(){return this._vm.locale}set locale(t){this._vm.$set(this._vm,"locale",t)}get fallbackLocale(){return this._vm.fallbackLocale}set fallbackLocale(t){this._localeChainCache={},this._vm.$set(this._vm,"fallbackLocale",t)}get formatFallbackMessages(){return this._formatFallbackMessages}set formatFallbackMessages(t){this._formatFallbackMessages=t}get missing(){return this._missing}set missing(t){this._missing=t}get formatter(){return this._formatter}set formatter(t){this._formatter=t}get silentTranslationWarn(){return this._silentTranslationWarn}set silentTranslationWarn(t){this._silentTranslationWarn=t}get silentFallbackWarn(){return this._silentFallbackWarn}set silentFallbackWarn(t){this._silentFallbackWarn=t}get preserveDirectiveContent(){return this._preserveDirectiveContent}set preserveDirectiveContent(t){this._preserveDirectiveContent=t}get warnHtmlInMessage(){return this._warnHtmlInMessage}set warnHtmlInMessage(t){const e=this._warnHtmlInMessage;if(this._warnHtmlInMessage=t,e!==t&&("warn"===t||"error"===t)){const t=this._getMessages();Object.keys(t).forEach(e=>{this._checkLocaleMessage(e,this._warnHtmlInMessage,t[e])})}}get postTranslation(){return this._postTranslation}set postTranslation(t){this._postTranslation=t}_getMessages(){return this._vm.messages}_getDateTimeFormats(){return this._vm.dateTimeFormats}_getNumberFormats(){return this._vm.numberFormats}_warnDefault(t,e,n,s,a,i){if(!l(n))return n;if(this._missing){const n=this._missing.apply(null,[t,e,s,a]);if(r(n))return n}if(this._formatFallbackMessages){const t=c(...a);return this._render(e,i,t.params,e)}return e}_isFallbackRoot(t){return!t&&!l(this._root)&&this._fallbackRoot}_isSilentFallbackWarn(t){return this._silentFallbackWarn instanceof RegExp?this._silentFallbackWarn.test(t):this._silentFallbackWarn}_isSilentFallback(t,e){return this._isSilentFallbackWarn(e)&&(this._isFallbackRoot()||t!==this.fallbackLocale)}_isSilentTranslationWarn(t){return this._silentTranslationWarn instanceof RegExp?this._silentTranslationWarn.test(t):this._silentTranslationWarn}_interpolate(t,e,n,s,a,i,c){if(!e)return null;const h=this._path.getPathValue(e,n);if(Array.isArray(h)||o(h))return h;let u;if(l(h)){if(!o(e))return null;if(!r(u=e[n]))return null}else{if(!r(h))return null;u=h}return(u.indexOf("@:")>=0||u.indexOf("@.")>=0)&&(u=this._link(t,e,u,s,"raw",i,c)),this._render(u,a,i,n)}_link(t,e,n,s,r,a,i){let o=n;const l=o.match(G);for(let n in l){if(!l.hasOwnProperty(n))continue;const c=l[n],h=c.match(X),[m,_]=h,f=c.replace(m,"").replace(Z,"");if(u(i,f))return o;i.push(f);let p=this._interpolate(t,e,f,s,"raw"===r?"string":r,"raw"===r?void 0:a,i);if(this._isFallbackRoot(p)){if(!this._root)throw Error("unexpected error");const t=this._root.$i18n;p=t._translate(t._getMessages(),t.locale,t.fallbackLocale,f,s,r,a)}p=this._warnDefault(t,f,p,s,Array.isArray(a)?a:[a],r),this._modifiers.hasOwnProperty(_)?p=this._modifiers[_](p):K.hasOwnProperty(_)&&(p=K[_](p)),i.pop(),o=p?o.replace(c,p):o}return o}_render(t,e,n,s){let a=this._formatter.interpolate(t,n,s);return a||(a=Q.interpolate(t,n,s)),"string"!==e||r(a)?a:a.join("")}_appendItemToChain(t,e,n){let s=!1;return u(t,e)||(s=!0,e&&(s="!"!==e[e.length-1],e=e.replace(/!/g,""),t.push(e),n&&n[e]&&(s=n[e]))),s}_appendLocaleToChain(t,e,n){let s;const r=e.split("-");do{const e=r.join("-");s=this._appendItemToChain(t,e,n),r.splice(-1,1)}while(r.length&&!0===s);return s}_appendBlockToChain(t,e,n){let s=!0;for(let a=0;a<e.length&&"boolean"==typeof s;a++){const i=e[a];r(i)&&(s=this._appendLocaleToChain(t,i,n))}return s}_getLocaleChain(t,e){if(""===t)return[];this._localeChainCache||(this._localeChainCache={});let a=this._localeChainCache[t];if(!a){e||(e=this.fallbackLocale),a=[];let i,o=[t];for(;n(o);)o=this._appendBlockToChain(a,o,e);(o=r(i=n(e)?e:s(e)?e.default?e.default:null:e)?[i]:i)&&this._appendBlockToChain(a,o,null),this._localeChainCache[t]=a}return a}_translate(t,e,n,s,r,a,i){const o=this._getLocaleChain(e,n);let c;for(let e=0;e<o.length;e++){const n=o[e];if(!l(c=this._interpolate(n,t[n],s,r,a,i,[s])))return c}return null}_t(t,e,n,s,...r){if(!t)return"";const a=c(...r),i=a.locale||e;let o=this._translate(n,i,this.fallbackLocale,t,s,"string",a.params);if(this._isFallbackRoot(o)){if(!this._root)throw Error("unexpected error");return this._root.$t(t,...r)}return o=this._warnDefault(i,t,o,s,r,"string"),this._postTranslation&&null!=o&&(o=this._postTranslation(o,t)),o}t(t,...e){return this._t(t,this.locale,this._getMessages(),null,...e)}_i(t,e,n,s,r){const a=this._translate(n,e,this.fallbackLocale,t,s,"raw",r);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.i(t,e,r)}return this._warnDefault(e,t,a,s,[r],"raw")}i(t,e,n){return t?(r(e)||(e=this.locale),this._i(t,e,this._getMessages(),null,n)):""}_tc(t,e,n,s,r,...a){if(!t)return"";void 0===r&&(r=1);const i={count:r,n:r},o=c(...a);return o.params=Object.assign(i,o.params),a=null===o.locale?[o.params]:[o.locale,o.params],this.fetchChoice(this._t(t,e,n,s,...a),r)}fetchChoice(t,e){if(!t&&!r(t))return null;const n=t.split("|");return n[e=this.getChoiceIndex(e,n.length)]?n[e].trim():t}tc(t,e,...n){return this._tc(t,this.locale,this._getMessages(),null,e,...n)}_te(t,e,n,...s){const r=c(...s).locale||e;return this._exist(n[r],t)}te(t,e){return this._te(t,this.locale,this._getMessages(),e)}getLocaleMessage(t){return h(this._vm.messages[t]||{})}setLocaleMessage(t,e){"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||this._checkLocaleMessage(t,this._warnHtmlInMessage,e),this._vm.$set(this._vm.messages,t,e)}mergeLocaleMessage(t,e){"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||this._checkLocaleMessage(t,this._warnHtmlInMessage,e),this._vm.$set(this._vm.messages,t,f({},this._vm.messages[t]||{},e))}getDateTimeFormat(t){return h(this._vm.dateTimeFormats[t]||{})}setDateTimeFormat(t,e){this._vm.$set(this._vm.dateTimeFormats,t,e),this._clearDateTimeFormat(t,e)}mergeDateTimeFormat(t,e){this._vm.$set(this._vm.dateTimeFormats,t,f(this._vm.dateTimeFormats[t]||{},e)),this._clearDateTimeFormat(t,e)}_clearDateTimeFormat(t,e){for(let n in e){const e=`${t}__${n}`;this._dateTimeFormatters.hasOwnProperty(e)&&delete this._dateTimeFormatters[e]}}_localizeDateTime(t,e,n,s,r){let a=e,i=s[a];const o=this._getLocaleChain(e,n);for(let t=0;t<o.length;t++){const e=o[t];if(a=e,!l(i=s[e])&&!l(i[r]))break}if(l(i)||l(i[r]))return null;{const e=i[r],n=`${a}__${r}`;let s=this._dateTimeFormatters[n];return s||(s=this._dateTimeFormatters[n]=new Intl.DateTimeFormat(a,e)),s.format(t)}}_d(t,e,n){if(!n)return new Intl.DateTimeFormat(e).format(t);const s=this._localizeDateTime(t,e,this.fallbackLocale,this._getDateTimeFormats(),n);if(this._isFallbackRoot(s)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.d(t,n,e)}return s||""}d(t,...e){let n=this.locale,a=null;return 1===e.length?r(e[0])?a=e[0]:s(e[0])&&(e[0].locale&&(n=e[0].locale),e[0].key&&(a=e[0].key)):2===e.length&&(r(e[0])&&(a=e[0]),r(e[1])&&(n=e[1])),this._d(t,n,a)}getNumberFormat(t){return h(this._vm.numberFormats[t]||{})}setNumberFormat(t,e){this._vm.$set(this._vm.numberFormats,t,e),this._clearNumberFormat(t,e)}mergeNumberFormat(t,e){this._vm.$set(this._vm.numberFormats,t,f(this._vm.numberFormats[t]||{},e)),this._clearNumberFormat(t,e)}_clearNumberFormat(t,e){for(let n in e){const e=`${t}__${n}`;this._numberFormatters.hasOwnProperty(e)&&delete this._numberFormatters[e]}}_getNumberFormatter(t,e,n,s,r,a){let i=e,o=s[i];const c=this._getLocaleChain(e,n);for(let t=0;t<c.length;t++){const e=c[t];if(i=e,!l(o=s[e])&&!l(o[r]))break}if(l(o)||l(o[r]))return null;{const t=o[r];let e;if(a)e=new Intl.NumberFormat(i,Object.assign({},t,a));else{const n=`${i}__${r}`;(e=this._numberFormatters[n])||(e=this._numberFormatters[n]=new Intl.NumberFormat(i,t))}return e}}_n(t,e,n,s){if(!Y.availabilities.numberFormat)return"";if(!n){return(s?new Intl.NumberFormat(e,s):new Intl.NumberFormat(e)).format(t)}const r=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,s),a=r&&r.format(t);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.n(t,Object.assign({},{key:n,locale:e},s))}return a||""}n(e,...n){let a=this.locale,i=null,o=null;return 1===n.length?r(n[0])?i=n[0]:s(n[0])&&(n[0].locale&&(a=n[0].locale),n[0].key&&(i=n[0].key),o=Object.keys(n[0]).reduce((e,s)=>u(t,s)?Object.assign({},e,{[s]:n[0][s]}):e,null)):2===n.length&&(r(n[0])&&(i=n[0]),r(n[1])&&(a=n[1])),this._n(e,a,i,o)}_ntp(t,e,n,s){if(!Y.availabilities.numberFormat)return[];if(!n){return(s?new Intl.NumberFormat(e,s):new Intl.NumberFormat(e)).formatToParts(t)}const r=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,s),a=r&&r.formatToParts(t);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n._ntp(t,e,n,s)}return a||[]}}let tt;Object.defineProperty(Y,"availabilities",{get(){if(!tt){const t="undefined"!=typeof Intl;tt={dateTimeFormat:t&&void 0!==Intl.DateTimeFormat,numberFormat:t&&void 0!==Intl.NumberFormat}}return tt}}),Y.install=D,Y.version="8.20.0";export default Y;
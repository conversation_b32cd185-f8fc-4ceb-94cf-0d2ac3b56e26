{"name": "vue-i18n", "description": "Internationalization plugin for Vue.js", "version": "8.20.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/kazupon/vue-i18n/issues"}, "changelog": {"labels": {"Type: Feature": ":star: New Features", "Type: Bug": ":bug: B<PERSON>xes", "Type: Security": ":lock: Security Fixes", "Type: Performance": ":chart_with_upwards_trend: Performance Fixes", "Type: Improvement": ":zap: Improved Features", "Type: Breaking": ":boom: Breaking Change", "Type: Deprecated": ":warning: Deprecated Features", "Type: I18n": ":globe_with_meridians: Internationalization", "Type: A11y": ":wheelchair: Accessibility", "Type: Documentation": ":pencil: Documentation"}}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-syntax-flow": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.0.0", "@typescript-eslint/eslint-plugin": "^3.0.0", "@typescript-eslint/parser": "^3.0.0", "@vue/babel-preset-app": "^4.4.1", "babel-eslint": "^10.1.0", "babel-loader": "^8.1.0", "babel-plugin-istanbul": "^6.0.0", "babel-preset-power-assert": "^3.0.0", "buble": "^0.19.3", "chromedriver": "^83.0.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "cross-spawn": "^7.0.3", "eslint": "^6.8.0", "eslint-loader": "^4.0.2", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-no-autofix": "^1.0.1", "eslint-plugin-vue": "^6.2.2", "eslint-plugin-vue-libs": "^4.0.0", "flow-bin": "^0.38.0", "http-server": "^0.12.3", "intl": "^1.2.5", "karma": "^5.0.9", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.2", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.1.5", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^4.0.2", "lerna-changelog": "^1.0.0", "lerna-changelog-label-schema": "^3.0.0", "mocha": "^7.2.0", "mocha-loader": "^5.0.0", "nightwatch": "^1.3.5", "nightwatch-helpers": "^1.2.0", "power-assert": "^1.6.0", "rollup": "^0.66.0", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.1.8", "rollup-plugin-flow-no-whitespace": "^1.0.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-replace": "^2.0.0", "selenium-server": "^3.141.59", "shipjs": "^0.17.0", "sinon": "^9.0.2", "terser": "^3.17.0", "typescript": "^3.9.3", "vue": "^2.5.17", "vue-github-button": "^1.1.2", "vue-template-compiler": "^2.5.17", "vuepress": "^1.5.0", "webpack": "^4.43.0", "webpack-cli": "^3.1.1", "webpack-dev-middleware": "^3.7.2", "webpack-dev-server": "^3.11.0"}, "files": ["dist/vue-i18n.js", "dist/vue-i18n.min.js", "dist/vue-i18n.common.js", "dist/vue-i18n.esm.js", "dist/vue-i18n.esm.browser.js", "dist/vue-i18n.esm.browser.min.js", "src/**/*.js", "types/*.d.ts", "decls", "vetur/tags.json", "vetur/attributes.json"], "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "homepage": "https://github.com/kazupon/vue-i18n#readme", "keywords": ["i18n", "internationalization", "plugin", "vue", "vue.js"], "license": "MIT", "main": "dist/vue-i18n.common.js", "module": "dist/vue-i18n.esm.js", "repository": {"type": "git", "url": "git+https://github.com/kazupon/vue-i18n.git"}, "scripts": {"build": "node config/build.js", "clean": "rm -rf coverage && rm -rf dist/*.js* && rm ./*.log", "coverage": "cat ./coverage/lcov.info", "dev": "cross-env BABEL_ENV=test webpack-dev-server --inline --hot --open --content-base ./test/unit/ --config config/webpack.dev.conf.js", "docs:build": "cross-env NODE_ENV=production node config/version.js && cross-env NODE_ENV=production vuepress build vuepress -d docs", "docs:clean": "rm -rf docs/**", "docs:dev": "vuepress dev vuepress", "flow": "flow check", "lint": "eslint --fix src test types/**/*.ts", "release:prepare": "shipjs prepare", "release:trigger": "shipjs trigger", "sauce": "npm run sauce:coolkids && npm run sauce:ie && npm run sauce:mobile", "sauce:coolkids": "karma start config/karma.sauce.conf.js -- 0", "sauce:ie": "karma start config/karma.sauce.conf.js -- 1", "sauce:mobile": "karma start config/karma.sauce.conf.js -- 2", "test": "npm run lint && npm run flow && npm run test:types && npm run test:cover && npm run test:e2e", "test:cover": "cross-env BABEL_ENV=test karma start config/karma.cover.conf.js", "test:e2e": "npm run build && node test/e2e/runner.js", "test:types": "tsc -p types", "test:unit": "cross-env BABEL_ENV=test karma start config/karma.unit.conf.js", "test:unit:ci": "cross-env BABEL_ENV=test karma start config/karma.unit.ci.conf.js"}, "sideEffects": false, "types": "types/index.d.ts", "unpkg": "dist/vue-i18n.js"}
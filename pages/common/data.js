import i18n from '@/common/locales/config.js'
const {mine} = i18n.messages[i18n.locale];



const langs = [
	{
		name:'Türkçe',
		value:'tr'
	},
	{
		name:'简体中文',
		value:'zh'
	},
	{
		name:'繁体中文',
		value:'hk'
	},
	{
		name:'English',
		value:'en'
	},
	{
		name:'ไทย',
		value:'th'
	},
	{
		name:'日本語',
		value:'jp'
	},
	{
		name:'한국어',
		value:'kor'
	},
	{
		name:'Français',
		value:'fra'
	},
	{
		name:'Español',
		value:'spa'
	},
	{
		name:'Português (Brasil)',
		value:'pt'
	}
]

export {

	langs
}
<template>
	<view class="text-white">
		<u-navbar :title="i18n.withdraw2">
			<navigator url="/pages/fund/receive_withdraw_record" slot="right">
				<u-icon name="order" size="38"></u-icon>
			</navigator>
		</u-navbar>
		<view class="mx-36 py-20">
			<!-- 移除币种选择，直接显示提现标题 -->
			<view class="bg-black px-30 py-16 box-shadow border-radius-10">
				<view class="d-flex align-items-center justify-content-center">
					<text class="font-size-32">{{i18n.withdraw2}}</text>
				</view>
			</view>

			<view class="d-flex font-size-34 mt-28 withdraw_type">
				<text class="mr-20 item" :class="type == 0 ? 'active' : ''" @click="type=0">{{i18n.withdrawToAddress}}</text>
				<text class="mr-20 item" :class="type == 1 ? 'active' : ''" @click="type=1">{{i18n.withdrawToCard}}</text>
				<text class="item" :class="type == 2 ? 'active' : ''" @click="type=2">{{i18n.withdrawToCardInternational}}</text>
			</view>
			<!-- 收款地址 -->
			<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 " v-if="type == 0">
				<view class="d-flex-between-center">
					<text class="d-block font-size-32">{{i18n.withdrawAddress}}</text>
				</view>
				<view class="input-group" @click="showSelectAddress=true">
					<input type="text" v-if="selectAddress" :value="selectAddress.address" :placeholder="i18n.plsSelectWithdrawAddress" disabled class="input" >
					<text class="absolute-text iconfont icon-xiala text-white"></text>
				</view>
			</view>
			<!-- 体现到银行卡 -->
			<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 " v-if="type == 1">
				<view class="d-flex-between-center">
					<text class="d-block font-size-32">{{i18n.card}}</text>
				</view>
				<view class="input-group">
					<input type="text" v-if="card && card.bank_account" :value="card.bank_account" disabled class="input" >
					<button v-else class="warning-button py-0 font-size-22 w-50 mx-auto" @click="$utils.jump('/pages/setting/bank')">{{i18n.addCard}}</button>
				</view>
			</view>
			<!-- 体現到银行卡 國際 -->
			<template v-if="type == 2">
				<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 ">
					<view class="d-flex-between-center">
						<text class="d-block font-size-32">{{i18n.card}}</text>
					</view>
					<view class="input-group">
						<input type="text" v-if="card_international && card_international.data[0]" :value="card_international.data[0]" disabled class="input" >
						<button v-else class="warning-button py-0 font-size-22 w-50 mx-auto" @click="$utils.jump('/pages/setting/bank_international')">{{i18n.addCard}}</button>
					</view>
				</view>
			</template>
			<!-- 转账数量 -->
			<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 ">
				<view class="d-flex-between-center">
					<text class="d-block font-size-32">{{i18n.withdrawNumber}}</text>
					<text>{{i18n.canUse}} {{totalBalance}}</text>
				</view>
				<view class="input-group">
					<input type="digit" v-model="number" class="input" :placeholder="i18n.plsIptWithdrawNumber" @blur="checkNumber">
					<text class="absolute-text" @click="setMaxAmount">{{i18n.all}}</text>
				</view>
			</view>
			<!-- 手续费 -->
			<text class="d-block text-right" style="height: 70rpx;line-height: 70rpx;">
				<text class="opacity-30">{{i18n.leastNumber}}</text> <text class="ml-26">{{min_number}}</text>
			</text>
			<!-- 到账数量 -->
			<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 ">
				<view class="d-flex-between-center">
					<text class="d-block font-size-32">{{i18n.canGetNumber}}</text>
				</view>
				<view class="input-group">
					<input type="text" class="input" disabled :value="canGetNumber">
				</view>
			</view>
			<!-- 手续费 -->
			<text class="d-block text-right" style="height: 70rpx;line-height: 70rpx;">
				<text class="opacity-30">{{i18n.handlingFee}}</text> <text class="ml-26">{{rate}}</text>
			</text>

			<!-- 提现密码 -->
			<view class="u-border-bottom  pb-16 withdraw-input-wrap pb-16 pt-28 ">
				<view class="d-flex-between-center">
					<text class="d-block font-size-32">{{i18nSetting.withdrawPassword}}</text>
				</view>
				<view class="input-group">
					<input type="password" v-model="withdraw_password" class="input" :placeholder="i18n.plsInputWithdrawPasswordForWithdraw">
				</view>
			</view>

			<button class="warning-button mt-70" @click="withdraw">{{$t("common.confirm")}}</button>

			<u-popup v-model="showSelectAddress" mode="bottom">
				<view class="p-30">
					<block v-if="walletAddress.length">
						<view class="item" v-for="item in walletAddress" :key="item.id" @click="selectAddressFunc(item)">
							<view class="d-flex align-items-center">
								<image :src="item.qrcode | retImageUrl" class="border-radius-20 " style="width: 170rpx;height: 170rpx;"></image>
								<view style="flex: 1;" class="mx-20">
									<text class="font-size-28 font-weight-bold d-block">{{i18n.coinType}}: {{item.name}}</text>
									<text class="font-size-28 text-warning font-weight-bold d-block" style="word-wrap:break-word;">{{i18n.address}}: {{item.address}}</text>
									<text class="font-size-28 font-weight-bold d-block">{{i18n.date}}: {{item.update_time}}</text>
								</view>
								<text class="iconfont icon-checkbox-ok text-warning" v-if="selectAddress.id == item.id"></text>
								<text v-else class="iconfont icon-checkbox" ></text>
							</view>
						</view>
					</block>
					<default-page v-else>
						<button class="w-50 mx-auto warning-button py-0 border-radius-20 text-white" @click="$utils.jump('/pages/setting/addWallet')">添加提币地址</button>
					</default-page>
				</view>
			</u-popup>
		</view>
	</view>
</template>
<script>
	// 收款
	export default {
		data() {
			return {
				min_number:0, //最低提现金额
				totalBalance:0, //总可用余额（与资产页面一致）
				rate:0,//手续费,
				number:null,
				showSelectAddress:false,
				originWalletAddress:[],
				walletAddress:[],
				selectAddress:{},
				type:1, //0地址，1银行卡,
				card:{},
				card_international: {},
				withdraw_password:'',
				walletData: null // 存储钱包数据
			};
		},
		onLoad() {
			// 移除币种选择相关初始化
		},
		onShow(){
			this.getWalletAddressList()
			this.getCard()
			this.getCardInternational()
			this.getWalletBalance() // 获取与资产页面一致的余额
		},
		methods: {
			// 获取与资产页面一致的总余额
			getWalletBalance(){
				this.$u.api.wallet.getWalletList().then(res => {
					this.walletData = res.message

					// 计算总余额（与资产页面逻辑一致）
					const changeWallet = Number(res.message.change_wallet.usdt_totle) || 0
					const leverWallet = Number(res.message.lever_wallet.usdt_totle) || 0
					const legalWallet = Number(res.message.legal_wallet.usdt_totle) || 0
					const microWallet = Number(res.message.micro_wallet.usdt_totle) || 0

					const totalBalance = changeWallet + leverWallet + legalWallet + microWallet

					// 确保数字是合理的，如果太大则显示为0
					if (totalBalance > 999999999 || totalBalance < 0 || isNaN(totalBalance)) {
						this.totalBalance = '0.0000'
					} else {
						this.totalBalance = totalBalance.toFixed(4)
					}

					// 获取USDT的提现信息（手续费、最低提现金额等）
					this.getUSDTWithdrawInfo()

				}).catch(err => {
					console.error('获取钱包余额失败:', err)
					this.totalBalance = '0.0000'
				})
			},

			// 获取USDT的提现信息
			getUSDTWithdrawInfo(){
				const currency = 3 // USDT
				this.$u.api.wallet.getWalletInfo(currency).then(res=>{
					const {message:data} = res
					// 正确处理数值，避免0被当作falsy值
					this.min_number = data.min_number !== undefined && data.min_number !== null ? Number(data.min_number) : 10
					this.rate = data.rate !== undefined && data.rate !== null ? Number(data.rate) : 5

					console.log('获取到的提现信息:', {
						min_number: this.min_number,
						rate: this.rate,
						原始数据: data
					})
				}).catch(err => {
					console.error('获取USDT提现信息失败:', err)
					// 使用默认值
					this.min_number = 10
					this.rate = 5
				})
			},

			// 设置最大金额
			setMaxAmount(){
				const maxAmount = parseFloat(this.totalBalance) - this.rate
				if(maxAmount > 0) {
					this.number = maxAmount.toString()
				}
			},
			checkNumber(e){
				const {i18n,min_number} = this
				const value = Number(e.detail.value)

				if(value < min_number && value != 0){
					return this.$utils.showToast(i18n.leastNumber + min_number)
				}

				// 检查是否超过可用余额
				const maxAmount = parseFloat(this.totalBalance)
				if(value > maxAmount){
					return this.$utils.showToast(i18n.insufficientBalance)
				}
			},
			getWalletAddressList(){
				this.$u.api.setting.getWalletAddressList(1,99999).then(res=>{
					this.originWalletAddress = res.message.data
					this.walletAddress = this.originWalletAddress
					this.selectAddress = this.walletAddress[0]
				})
			},
			selectAddressFunc(item){
				this.selectAddress = item
				setTimeout(()=>{
					this.showSelectAddress=false
				},300);
			},
			withdraw(){
				const {i18n,i18nSetting,selectAddress,number,rate,type,card,card_international,withdraw_password} = this

				// 根据提现类型验证必要信息
				let address = ''
				if(type == 0) {
					// 提现到钱包地址
					if(!selectAddress || !selectAddress.address) {
						return this.$utils.showToast(i18n.plsSelectWithdrawAddress)
					}
					address = selectAddress.address
				} else if(type == 1) {
					// 提现到银行卡
					if(!card.bank_account) {
						return this.$utils.showToast(i18n.plsAddCard)
					}
					address = card.bank_account // 使用银行卡号作为地址
				} else if(type == 2) {
					// 提现到国际银行卡
					if(!card_international.bank_account) {
						return this.$utils.showToast(i18n.plsAddCard)
					}
					address = card_international.bank_account // 使用国际银行卡号作为地址
				}

				if(!number){
					//没有金额
					return this.$utils.showToast(i18n.plsIptWithdrawNumber)
				}

				// 检查余额是否足够
				const withdrawAmount = parseFloat(number)
				const availableBalance = parseFloat(this.totalBalance)
				if(withdrawAmount > availableBalance){
					return this.$utils.showToast(i18n.insufficientBalance)
				}

				// 验证提现密码
				if(!withdraw_password){
					return this.$utils.showToast(i18n.plsInputWithdrawPasswordForWithdraw)
				}

				// 使用USDT作为默认币种，构建完整的参数对象
				const currency = 3 // USDT
				const params = {
					address: address,
					currency: currency,
					number: number,
					rate: rate,
					type: type,
					withdraw_password: withdraw_password
				}

				console.log('提现参数:', params) // 调试用，可以删除

				this.$u.api.wallet.withdraw(params).then(res=>{
					this.$utils.showToast(res.message)
					this.number = 0
					this.withdraw_password = ''
					// 重新获取余额
					this.getWalletBalance()
				}).catch(err => {
					console.error('提现失败:', err)
					if(err.message) {
						this.$utils.showToast(err.message)
					} else {
						this.$utils.showToast('提现失败，请稍后重试')
					}
				})

			},
			getCard(){
				this.$u.api.setting.getCard().then(res=>{
					this.card = res.message
				})
			},
			getCardInternational(){
				this.$u.api.setting.getCardInternational().then(res=>{
					this.card_international = res.message
				})
			}
		},
		computed: {
			i18n() {
				return this.$t("fund")
			},
			i18ncommon() {
				return this.$t("common")
			},
			i18nSetting() {
				return this.$t("setting")
			},
			canGetNumber(){
				const value = this.number - this.rate
				if(value > 0) return value
				return 0
			}
		},
		// 移除币种选择相关的watch
	}
</script>
<style lang="scss" scoped>
	.select-coin {
		@extend .d-flex,
		.align-items-center,
		.justify-content-between;
		border-bottom: 1px solid #f5f5f5;

		&:last-child {
			border-bottom: none;
		}

		.type {
			background-color: $uni-color-success;
			border-radius: 22rpx;
			color: #fff;
			padding: 6rpx 18rpx;
			font-size: 20rpx;
		}

		&.cannot {
			view {
				opacity: .3;
			}

			.type {
				background-color: #d6d6d6;
			}
		}
	}

	.chain {
		margin-top: 16rpx;

		.item {
			font-size: 28rpx;
			height: 46rpx;
			line-height: 46rpx;
			padding: 0 30rpx;
			border-radius: 24rpx;
			border: 1px solid #333;
			color: #333;
			opacity: .5;
			margin-right: 18rpx;

			&.active {
				opacity: 1;
				color: $uni-color-success;
				border-color: $uni-color-success;
				background-color: #e5f8f2;
			}
		}
	}

	.withdraw-input-wrap {
		.input-group {
			position: relative;

			.input {
				height: 80rpx;
				line-height: 80rpx;
				font-size: 28rpx;
				background: none;
				padding: 0;
			}

			.absolute-btn {
				position: absolute;
				font-size: 22rpx;
				height: 42rpx;
				line-height: 42rpx;
				color: #9fa3ab;
				border-radius: 22rpx;
				background-color: #f2f5fc;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
			}

			.absolute-text {
				position: absolute;
				font-size: 28rpx;
				color: #296acf;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}

	.withdraw-alert{
		font-size: 22rpx !important;
		padding: 22rpx  34rpx;
		background-color: #4D4D4D;
		margin-top: 54rpx;
		border-radius: 10rpx;
		position: relative;
		&::after{
			content: "";
			display: block;
			width: 28rpx;
			height: 18rpx;
			background: url("/static/image/icon/withdraw-icon-1.png");
			background-size: cover;
			position: absolute;
			top: -18rpx;
			left: 80rpx;
		}
		text{
			line-height: 32rpx;
			display: block;
			color: #ccc !important;
		}
	}

	.withdraw_type{
		.item{
			position: relative;
			padding-bottom: 8rpx;
			&:after{
				display: block;
				position: absolute;
				content: "";
				left: 30%;
				bottom: 0;
				right: 30%;
				height: 4rpx;
				border-radius: 6rpx;
				background-color: $uni-color-333;
			}
			&.active:after{
				background-color: $uni-color-warning;
			}
		}
	}

</style>

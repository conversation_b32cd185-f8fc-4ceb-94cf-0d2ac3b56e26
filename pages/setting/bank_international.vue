<template>
	<view class="">
		<u-navbar :title="i18n.addBank"></u-navbar>
		<view class="mx-30 mt-30 box-shadow  bg-black p-30 border-radius-20 text-white" >
			
			<view class="login-input-group mt-0" v-for="(item, key) in forms">
				<text class="label">{{item}}</text>
				<input type="text" v-model="data[key]" class="login-input">
			</view>

			<!-- 提现密码 -->
			<view class="login-input-group">
				<text class="label">{{i18n.withdrawPassword}}</text>
				<input type="password" v-model="withdraw_password" class="login-input" :placeholder="i18n.withdrawPasswordPlaceholder">
			</view>

			<!-- 确认提现密码 -->
			<view class="login-input-group">
				<text class="label">{{i18n.confirmWithdrawPassword}}</text>
				<input type="password" v-model="confirm_withdraw_password" class="login-input" :placeholder="i18n.confirmWithdrawPasswordPlaceholder">
			</view>

		</view>

		
		<view class="m-30">
			<button class="warning-button py-0" @click="submit" >{{$t("common.confirm")}}</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				forms: [],
				data: [],
				withdraw_password:'',
				confirm_withdraw_password:''
			};
		},
		onShow() {
			this.getCard()
		},
		methods: {
			getCard(){
				this.$u.api.setting.getCardInternational().then(({message})=>{
					this.forms = message.forms
					this.data = message.data
				})
			},
			submit() {
				let {
					i18n,withdraw_password,confirm_withdraw_password
				} = this

				let post_data = {}
				for (const i in this.data) {
					if (!this.data[i]) {
						this.$utils.showToast(i18n.allNeed)
						return;
					}
					post_data['data['+i+']'] = this.data[i]
				}

				// 验证提现密码
				if(withdraw_password || confirm_withdraw_password) {
					if(!withdraw_password) {
						return this.$utils.showToast(i18n.plsInputWithdrawPassword)
					}
					if(!confirm_withdraw_password) {
						return this.$utils.showToast(i18n.plsInputConfirmWithdrawPassword)
					}
					if(withdraw_password.length < 6 || withdraw_password.length > 16) {
						return this.$utils.showToast(i18n.withdrawPasswordLengthError)
					}
					if(withdraw_password !== confirm_withdraw_password) {
						return this.$utils.showToast(i18n.withdrawPasswordNotMatch)
					}
					post_data.withdraw_password = withdraw_password
					post_data.confirm_withdraw_password = confirm_withdraw_password
				}

				this.$u.api.setting.saveCardInternational(post_data).then(res=>{
					this.$utils.showToast(res.message)
					// 清空密码字段
					this.withdraw_password = ''
					this.confirm_withdraw_password = ''
				})
			},
			
		},
		computed: {
			i18n() {
				return this.$t("setting")
			},
		}
	}
</script>

<style lang="scss" scoped>
	
</style>

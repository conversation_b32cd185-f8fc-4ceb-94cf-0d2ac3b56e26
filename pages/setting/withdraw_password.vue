<template>
	<view class="">
		<u-navbar :title="i18n.updateWithdrawPassword"></u-navbar>
		<view class="mx-30 mt-30 box-shadow  bg-black p-30 border-radius-20 text-white" >
			<!-- 原提现密码 -->
			<view class="login-input-group mt-0" v-if="hasWithdrawPassword">
				<text class="label">{{i18n.oldWithdrawPassword}}</text>
				<input type="password" v-model="old_password" class="login-input" :placeholder="i18n.plsInputOldWithdrawPassword">
			</view>
			
			<!-- 新提现密码 -->
			<view class="login-input-group" :class="{'mt-0': !hasWithdrawPassword}">
				<text class="label">{{i18n.newWithdrawPassword}}</text>
				<input type="password" v-model="password" class="login-input" :placeholder="i18n.withdrawPasswordPlaceholder">
			</view>

			<!-- 确认新提现密码 -->
			<view class="login-input-group">
				<text class="label">{{i18n.confirmWithdrawPassword}}</text>
				<input type="password" v-model="re_password" class="login-input" :placeholder="i18n.confirmWithdrawPasswordPlaceholder">
			</view>

		</view>

		
		<view class="m-30">
			<button class="warning-button py-0" @click="submit" >{{$t("common.confirm")}}</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				old_password:'',
				password:'',
				re_password:'',
				hasWithdrawPassword: false
			};
		},
		onLoad(options) {
			// 检查是否已设置提现密码
			this.hasWithdrawPassword = options.hasPassword === 'true'
		},
		methods: {
			submit() {
				let {
					i18n,old_password,password,re_password,hasWithdrawPassword
				} = this
				
				// 验证输入
				if(hasWithdrawPassword && !old_password) {
					return this.$utils.showToast(i18n.plsInputOldWithdrawPassword)
				}
				if(!password) {
					return this.$utils.showToast(i18n.plsInputNewWithdrawPassword)
				}
				if(!re_password) {
					return this.$utils.showToast(i18n.plsInputConfirmWithdrawPassword)
				}
				if(password.length < 6 || password.length > 16) {
					return this.$utils.showToast(i18n.withdrawPasswordLengthError)
				}
				if(password !== re_password) {
					return this.$utils.showToast(i18n.withdrawPasswordNotMatch)
				}
				
				this.$u.api.setting.updateWithdrawPassword(old_password,password,re_password).then(res=>{
					this.$utils.showToast(res.message)
					// 清空输入框
					this.old_password = ''
					this.password = ''
					this.re_password = ''
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				})
			},
			
		},
		computed: {
			i18n() {
				return this.$t("setting")
			},
		}
	}
</script>

<style lang="scss" scoped>
	
</style>

<template>
	<view class="test-time-page">
		<u-navbar title="多语言时间格式测试" back-icon-color="#fff" background-color="#333333">
		</u-navbar>
		
		<view class="content px-30 pt-30">
			<view class="test-section mb-30">
				<text class="section-title text-white font-size-32 font-weight-bold mb-20 d-block">
					当前语言: {{currentLang}}
				</text>
				
				<view class="time-display bg-black text-white border-radius-20 p-30 mb-20">
					<text class="font-size-28 opacity-70 d-block mb-10">原始时间 (UTC):</text>
					<text class="font-size-32 font-weight-bold d-block mb-20">{{testTime}}</text>
					
					<text class="font-size-28 opacity-70 d-block mb-10">格式化后时间:</text>
					<text class="font-size-32 font-weight-bold text-warning d-block">
						{{$utils.formatTimeByLang(testTime)}}
					</text>
				</view>
			</view>
			
			<view class="language-list">
				<text class="section-title text-white font-size-32 font-weight-bold mb-20 d-block">
					切换语言测试
				</text>
				
				<view class="lang-item bg-black text-white border-radius-20 p-20 mb-15" 
					  v-for="(lang, key) in supportedLangs" :key="key"
					  @click="switchLanguage(key)">
					<view class="d-flex justify-content-between align-items-center">
						<view>
							<text class="font-size-28 font-weight-bold d-block">{{lang.name}}</text>
							<text class="font-size-24 opacity-50">{{lang.timezone}}</text>
						</view>
						<view class="text-right">
							<text class="font-size-24 d-block text-warning">
								{{$utils.formatTimeByLang(testTime, key)}}
							</text>
							<text class="font-size-20 opacity-50">UTC{{lang.offset >= 0 ? '+' : ''}}{{lang.offset}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testTime: '2023-12-01 10:30:00', // UTC时间
			currentLang: 'tr',
			supportedLangs: {
				'tr': {
					name: 'Türkçe',
					timezone: 'Europe/Istanbul',
					offset: 3
				},
				'zh': {
					name: '中文简体',
					timezone: 'Asia/Shanghai',
					offset: 8
				},
				'hk': {
					name: '中文繁体',
					timezone: 'Asia/Hong_Kong',
					offset: 8
				},
				'en': {
					name: 'English',
					timezone: 'America/New_York',
					offset: -5
				},
				'jp': {
					name: '日本語',
					timezone: 'Asia/Tokyo',
					offset: 9
				},
				'kor': {
					name: '한국어',
					timezone: 'Asia/Seoul',
					offset: 9
				},
				'th': {
					name: 'ไทย',
					timezone: 'Asia/Bangkok',
					offset: 7
				},
				'fra': {
					name: 'Français',
					timezone: 'Europe/Paris',
					offset: 1
				},
				'spa': {
					name: 'Español',
					timezone: 'Europe/Madrid',
					offset: 1
				},
				'pt': {
					name: 'Português (Brasil)',
					timezone: 'America/Sao_Paulo',
					offset: -3
				}
			}
		}
	},
	onLoad() {
		this.currentLang = this.$store.state.lang || uni.getStorageSync('lang') || 'pt'
		// 使用当前时间作为测试
		this.testTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
	},
	methods: {
		switchLanguage(langKey) {
			this.currentLang = langKey
			// 更新存储的语言
			uni.setStorageSync('lang', langKey)
			this.$store.commit('setLang', langKey)
			
			// 提示用户
			this.$utils.showToast(`已切换到 ${this.supportedLangs[langKey].name}`)
		}
	}
}
</script>

<style lang="scss" scoped>
.test-time-page {
	background-color: #333333;
	min-height: 100vh;
}

.content {
	padding-bottom: 100rpx;
}

.lang-item {
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
		opacity: 0.8;
	}
}

.section-title {
	border-left: 4rpx solid #ffd7a6;
	padding-left: 20rpx;
}
</style>

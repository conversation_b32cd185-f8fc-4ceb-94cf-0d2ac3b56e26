<template>
	<view class="trade-query-page">
		<u-navbar :title="$t('tradeQuery.title')" back-icon-color="#fff" background-color="#333333">
		</u-navbar>

		<view class="content px-30 pt-30">
			<!-- 刷新按钮 -->
			<view class="d-flex justify-content-end mb-20">
				<button class="refresh-btn" @click="refreshData">
					<text class="iconfont icon-shuaxin mr-10"></text>
					{{$t('tradeQuery.refresh')}}
				</button>
			</view>

			<!-- 交易记录列表 -->
			<view class="trade-list" v-if="tradeList.length > 0">
				<view class="trade-item bg-black text-white border-radius-20 mb-20 p-30"
					  v-for="(item, index) in tradeList" :key="index">

					<!-- 交易对名称 -->
					<view class="d-flex justify-content-between align-items-center mb-16">
						<text class="font-size-32 font-weight-bold text-warning">
							{{item.trading_pair}}
						</text>
						<view class="status-badge" :class="getStatusClass(item.profit_status)">
							<text class="font-size-24">{{getStatusText(item.profit_status)}}</text>
						</view>
					</view>

					<!-- 交易信息 -->
					<view class="trade-info">
						<view class="info-row d-flex justify-content-between mb-12">
							<text class="label opacity-70">{{$t('tradeQuery.direction')}}:</text>
							<text class="value" :class="item.direction === '买涨' ? 'text-success' : 'text-error'">
								{{getDirectionText(item.direction)}}
							</text>
						</view>

						<view class="info-row d-flex justify-content-between mb-12">
							<text class="label opacity-70">{{$t('tradeQuery.betSeconds')}}:</text>
							<text class="value">{{item.bet_seconds}} {{$t('tradeQuery.seconds')}}</text>
						</view>

						<view class="info-row d-flex justify-content-between mb-12">
							<text class="label opacity-70">{{$t('tradeQuery.betAmount')}}:</text>
							<text class="value font-weight-bold">{{item.bet_amount}} USDT</text>
						</view>

						<view class="info-row d-flex justify-content-between" v-if="item.profit_amount && item.profit_amount !== '0.00'">
							<text class="label opacity-70">{{$t('transaction.pl')}}:</text>
							<text class="value font-weight-bold"
								  :class="parseFloat(item.profit_amount) >= 0 ? 'text-success' : 'text-error'">
								{{parseFloat(item.profit_amount) >= 0 ? '+' : ''}}{{item.profit_amount}} USDT
							</text>
						</view>
					</view>

					<!-- 时间信息 -->
					<view class="time-info mt-16 pt-16" style="border-top: 1px solid rgba(255,255,255,0.1);">
						<text class="font-size-24 opacity-50">{{item.created_at_formatted || $utils.formatTimeByLang(item.created_at)}}</text>
					</view>
				</view>
			</view>

			<!-- 无数据状态 -->
			<view class="no-data text-center py-100" v-else-if="!loading">
				<text class="iconfont icon-zanwushuju font-size-100 opacity-30 d-block mb-20"></text>
				<text class="font-size-28 opacity-50">{{$t('tradeQuery.noData')}}</text>
			</view>

			<!-- 加载状态 -->
			<view class="loading text-center py-100" v-if="loading">
				<u-loading mode="circle" color="#ffd7a6"></u-loading>
				<text class="d-block mt-20 font-size-28 opacity-50">{{$t('tradeQuery.loading')}}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			tradeList: [],
			loading: false
		}
	},
	onLoad() {
		this.loadTradeData()
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadTradeData()
	},
	methods: {
		// 加载交易数据
		async loadTradeData() {
			this.loading = true
			try {
				// 模拟API调用 - 实际项目中替换为真实API
				const res = await this.getMicroOrders()
				if (res && res.data && res.data.data) {
					this.tradeList = res.data.data
				}
			} catch (error) {
				console.error('加载交易数据失败:', error)
				this.$utils.showToast('加载数据失败')
			} finally {
				this.loading = false
			}
		},

		// 获取期权交易记录的API
		async getMicroOrders() {
			try {
				// 获取当前用户语言设置
				const lang = this.$store.state.lang || uni.getStorageSync('lang') || 'pt'
				// 调用真实API
				const res = await this.$u.api.tradeQuery.getMicroOrders(1, 20, lang)
				return res
			} catch (error) {
				console.error('API调用失败，使用模拟数据:', error)
				// 如果API调用失败，返回模拟数据
				return {
					type: "success",
					message: "获取成功",
					data: {
						current_page: 1,
						data: [
							{
								id: 1001,
								order_number: 11001,
								trading_pair: "BTC/USDT",
								direction: "买涨",
								bet_seconds: 60,
								bet_amount: "100.00",
								profit_status: "盈利",
								is_profit: true,
								is_loss: false,
								profit_amount: "85.00",
								open_price: "45000.00",
								end_price: "45100.00",
								status: "已平仓",
								created_at: "2023-12-01 10:30:00",
								created_at_formatted: "01.12.2023 13:30:00"
							},
							{
								id: 1002,
								order_number: 11002,
								trading_pair: "ETH/USDT",
								direction: "买跌",
								bet_seconds: 30,
								bet_amount: "50.00",
								profit_status: "亏损",
								is_profit: false,
								is_loss: true,
								profit_amount: "-50.00",
								open_price: "2800.00",
								end_price: "2820.00",
								status: "已平仓",
								created_at: "2023-12-01 10:25:00"
							},
							{
								id: 1003,
								order_number: 11003,
								trading_pair: "BTC/USDT",
								direction: "买涨",
								bet_seconds: 120,
								bet_amount: "200.00",
								profit_status: "交易中",
								is_profit: false,
								is_loss: false,
								profit_amount: "0.00",
								open_price: "45200.00",
								end_price: "45200.00",
								status: "交易中",
								remain_milli_seconds: 45000,
								created_at: "2023-12-01 10:35:00"
							}
						],
						total: 3
					}
				}
			}
		},

		// 刷新数据
		refreshData() {
			this.loadTradeData()
		},

		// 获取状态样式类
		getStatusClass(status) {
			switch(status) {
				case '盈利': return 'status-profit'
				case '亏损': return 'status-loss'
				case '平局': return 'status-draw'
				case '交易中': return 'status-trading'
				default: return 'status-default'
			}
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'盈利': this.$t('tradeQuery.profit'),
				'亏损': this.$t('tradeQuery.loss'),
				'平局': this.$t('tradeQuery.draw'),
				'交易中': this.$t('tradeQuery.trading')
			}
			return statusMap[status] || status
		},

		// 获取方向文本
		getDirectionText(direction) {
			const directionMap = {
				'买涨': this.$t('tradeQuery.buyUp'),
				'买跌': this.$t('tradeQuery.buyDown')
			}
			return directionMap[direction] || direction
		},


	}
}
</script>

<style lang="scss" scoped>
.trade-query-page {
	background-color: #333333;
	min-height: 100vh;
}

.content {
	padding-bottom: 100rpx;
}

.refresh-btn {
	background: linear-gradient(45deg, #ffd7a6, #ffb366);
	color: #333;
	border: none;
	border-radius: 25rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
}

.trade-item {
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.3);
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;

	&.status-profit {
		background-color: rgba(21, 190, 151, 0.2);
		color: #15be97;
	}

	&.status-loss {
		background-color: rgba(255, 65, 91, 0.2);
		color: #ff415b;
	}

	&.status-draw {
		background-color: rgba(255, 215, 166, 0.2);
		color: #ffd7a6;
	}

	&.status-trading {
		background-color: rgba(128, 174, 251, 0.2);
		color: #80aefb;
	}

	&.status-default {
		background-color: rgba(255, 255, 255, 0.1);
		color: #fff;
	}
}

.info-row {
	.label {
		font-size: 28rpx;
	}

	.value {
		font-size: 28rpx;
	}
}

.no-data, .loading {
	color: #999;
}
</style>
